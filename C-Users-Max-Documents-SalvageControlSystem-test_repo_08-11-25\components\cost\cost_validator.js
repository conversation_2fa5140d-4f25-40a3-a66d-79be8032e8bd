// Cost Validation and Sanity Gates
// Prevents export of unrealistic or incomplete cost estimates

class CostValidator {
    constructor() {
        this.warnings = [];
        this.errors = [];
        this.blocked = false;
    }

    // Validate complete cost estimate
    async validateCostEstimate(costData) {
        this.warnings = [];
        this.errors = [];
        this.blocked = false;

        // Check for required data
        await this._checkRequiredData();
        
        // Validate quantities
        this._validateQuantities(costData);
        
        // Check cost ratios
        this._checkCostRatios(costData);
        
        // Validate totals
        this._validateTotals(costData);
        
        return {
            valid: this.errors.length === 0 && !this.blocked,
            warnings: this.warnings,
            errors: this.errors,
            blocked: this.blocked
        };
    }

    // Check if required data libraries are populated
    async _checkRequiredData() {
        try {
            // Check labor tables
            const laborResponse = await fetch('/C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25//C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25//C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25/components/cost/labor_tables.json'))));
            if (laborResponse.ok) {
                const laborData = await laborResponse.json();
                if (this._isPlaceholderData(laborData.labor_rates)) {
                    this.errors.push('Labor rates not provided - placeholder data detected');
                    this.blocked = true;
                }
            } else {
                this.errors.push('Labor tables file not found');
                this.blocked = true;
            }

            // Check material prices
            const materialResponse = await fetch('/C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25//C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25//C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25/components/cost/material_prices.json'))));
            if (materialResponse.ok) {
                const materialData = await materialResponse.json();
                if (this._isPlaceholderData(materialData.materials)) {
                    this.errors.push('Material prices not provided - placeholder data detected');
                    this.blocked = true;
                }
            } else {
                this.errors.push('Material prices file not found');
                this.blocked = true;
            }

        } catch (error) {
            this.errors.push(`Error checking required data: ${error.message}`);
            this.blocked = true;
        }
    }

    // Check if data contains only placeholders
    _isPlaceholderData(data) {
        if (!data || typeof data !== 'object') return true;
        
        // Check for placeholder indicators
        if (data._placeholder_note || data._instructions) return true;
        
        // Check if all values are zero or empty
        const values = Object.values(data);
        if (values.length === 0) return true;
        
        const nonPlaceholderValues = values.filter(v => 
            v !== null && 
            v !== undefined && 
            v !== 0 && 
            v !== '' &&
            !String(v).includes('placeholder') &&
            !String(v).includes('USER MUST')
        );
        
        return nonPlaceholderValues.length === 0;
    }

    // Validate quantities for reasonableness
    _validateQuantities(costData) {
        if (!costData.quantities) return;

        const { quantities } = costData;

        // Check concrete quantities
        if (quantities.concrete_cy) {
            if (quantities.concrete_cy < 1) {
                this.warnings.push('Very small concrete quantity (< 1 CY) - verify accuracy');
            }
            if (quantities.concrete_cy > 10000) {
                this.warnings.push('Very large concrete quantity (> 10,000 CY) - verify accuracy');
            }
        }

        // Check building area
        if (quantities.building_sf) {
            if (quantities.building_sf < 100) {
                this.warnings.push('Very small building area (< 100 SF) - verify accuracy');
            }
            if (quantities.building_sf > 1000000) {
                this.warnings.push('Very large building area (> 1M SF) - verify accuracy');
            }
        }

        // Check labor hours
        if (quantities.labor_hours) {
            if (quantities.labor_hours < 1) {
                this.warnings.push('Very few labor hours (< 1) - verify accuracy');
            }
            if (quantities.labor_hours > 100000) {
                this.warnings.push('Very high labor hours (> 100,000) - verify accuracy');
            }
        }
    }

    // Check cost ratios for reasonableness
    _checkCostRatios(costData) {
        if (!costData.totals) return;

        const { totals } = costData;
        const totalCost = totals.total || 0;

        if (totalCost === 0) {
            this.errors.push('Total cost is zero - no costs calculated');
            return;
        }

        // Labor to equipment ratio
        const laborCost = totals.labor || 0;
        const equipmentCost = totals.equipment || 0;
        
        if (laborCost > 0 && equipmentCost > 0) {
            const laborEquipRatio = laborCost / equipmentCost;
            if (laborEquipRatio > 10) {
                this.warnings.push(`High labor-to-equipment ratio (${laborEquipRatio.toFixed(1)}:1) - verify labor hours`);
            }
            if (laborEquipRatio < 0.1) {
                this.warnings.push(`Low labor-to-equipment ratio (${laborEquipRatio.toFixed(1)}:1) - verify equipment hours`);
            }
        }

        // Material to labor ratio
        const materialCost = totals.materials || 0;
        if (laborCost > 0 && materialCost > 0) {
            const materialLaborRatio = materialCost / laborCost;
            if (materialLaborRatio > 5) {
                this.warnings.push(`High material-to-labor ratio (${materialLaborRatio.toFixed(1)}:1) - verify material quantities`);
            }
        }

        // Cost per square foot (if building area provided)
        if (costData.quantities?.building_sf) {
            const costPerSF = totalCost / costData.quantities.building_sf;
            
            if (costPerSF < 50) {
                this.warnings.push(`Low cost per SF ($${costPerSF.toFixed(2)}) - verify quantities and rates`);
            }
            if (costPerSF > 1000) {
                this.warnings.push(`High cost per SF ($${costPerSF.toFixed(2)}) - verify quantities and rates`);
            }
        }
    }

    // Validate total costs
    _validateTotals(costData) {
        if (!costData.totals) {
            this.errors.push('No cost totals provided');
            return;
        }

        const { totals } = costData;
        const totalCost = totals.total || 0;

        // Check for extremely high costs
        if (totalCost > 100000000) { // $100M
            this.warnings.push(`Very high total cost ($${(totalCost/1000000).toFixed(1)}M) - requires confirmation`);
        }

        // Check for suspiciously low costs
        if (totalCost < 1000 && totalCost > 0) {
            this.warnings.push(`Very low total cost ($${totalCost.toFixed(2)}) - verify all costs included`);
        }

        // Check component totals add up
        const componentSum = (totals.labor || 0) + (totals.materials || 0) + 
                           (totals.equipment || 0) + (totals.other || 0);
        
        if (Math.abs(componentSum - totalCost) > 0.01) {
            this.errors.push('Component costs do not sum to total cost');
        }
    }

    // Generate validation report
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            status: this.blocked ? 'BLOCKED' : this.errors.length > 0 ? 'FAILED' : 'PASSED',
            summary: {
                errors: this.errors.length,
                warnings: this.warnings.length,
                blocked: this.blocked
            },
            details: {
                errors: this.errors,
                warnings: this.warnings
            }
        };

        if (this.blocked) {
            report.message = 'Cost export blocked - required data missing or invalid';
        } else if (this.errors.length > 0) {
            report.message = 'Cost validation failed - errors must be resolved';
        } else if (this.warnings.length > 0) {
            report.message = 'Cost validation passed with warnings - review recommended';
        } else {
            report.message = 'Cost validation passed - ready for export';
        }

        return report;
    }

    // Check if export should be allowed
    canExport() {
        return !this.blocked && this.errors.length === 0;
    }

    // Get user confirmation for warnings
    async getUserConfirmation() {
        if (this.warnings.length === 0) return true;

        const warningText = this.warnings.join('\n• ');
        const message = `Cost validation warnings detected:\n\n• ${warningText}\n\nDo you want to proceed with export?`;
        
        return confirm(message);
    }
}

// Global instance
window.CostValidator = window.CostValidator || CostValidator;

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CostValidator;
}
