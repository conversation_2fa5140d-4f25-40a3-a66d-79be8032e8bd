 
 
Supplement to the  
Benefit-Cost Analysis  
Reference Guide 
June 2011 
 
 
 
 
 
 
 
Federal Emergency Management Agency 
 
Department of Homeland Security 
 
500 C Street, SW 
 
Washington, DC 20472 
 
 
 
 
 
 
 
This document was prepared by 
 
URS Group, I nc. 
200 Orchard Ridge Drive, Suite 101 
Gaithersburg, MD 20878 
 
 
Contract No. HSFEHQ-06-D-0162 
Task Order HSFEHQ-09-J-0024 
 
 
 
 
 
 
 
ACKNOWLEDGEMENTS 
 
Jody <PERSON> (FEMA HQ) 
 
<PERSON><PERSON> (URS Group, I nc.) 
<PERSON> (Atkins) 
<PERSON> (URS Group, Inc.) 
<PERSON> (URS Group, I nc.) 
Jae Park (URS Group, Inc.) 
<PERSON> (URS Group, Inc.) 
<PERSON> (URS Group, Inc.) 
<PERSON> (Atkins) 
<PERSON> (Atkins) 
Jeff <PERSON> (URS Group, Inc.) 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Table of Contents 
 
 i 
ACRONYMS AND ABBREVIATIONS .................................................................................................. v 
SECTION ONE 
INTRODUCTION ............................................................................................... 1-1 
1.1 
Purpose................................................................................................................. 1-1 
1.2 
Cost-Effectiveness Analysis Requirements for Hazard Mitigation 
Assistance Program Funds ................................................................................... 1-1 
SECTION TWO 
FLOOD HAZARD MITIGATION .......................................................................... 2-1 
2.1 
Working with the Damage Frequency Assessment Module ................................ 2-3 
2.1.1 Documenting Expected, Historical, and Residual Damages ................... 2-3 
2.1.2 Determining Recurrence Intervals ........................................................... 2-4 
2.1.2.1 Step 1 − Determine Gage Period of Record.............................. 2-4 
******* Step 2 − Retrieve and Download the Annual Peak 
Discharge for the Event of Interest ........................................... 2-7 
******* Step 3 − Import Annual Peak Data into PeakFQ Program 
to Identify Exceedance Probability and Return Interval ........... 2-8 
******* Calculating Peak Daily Discharges Using Mean Daily 
Discharges ............................................................................... 2-11 
2.1.3 Adjusting Analysis Duration.................................................................. 2-14 
******* Acceptable Documentation ..................................................... 2-15 
2.1.3.2 Entering the Analysis Duration in the DFA Module .............. 2-17 
2.1.4 Using National Flood I nsurance Program BureauNet Data................... 2-21 
******* Access to BureauNet ............................................................... 2-21 
******* Flood Insurance Coverage ...................................................... 2-21 
******* Estimated Flood Damages versus Actual Claims 
Payments ................................................................................. 2-22 
******* Interpreting BureauNet Claims Data....................................... 2-22 
******* How to Use NFIP BureauNet Data with the DFA Module .... 2-25 
******* Enter NFIP Data into DFA Module and Determine BCR ...... 2-25 
******* Using the BCA Full Data Flood Module to Maximize 
Benefits in the DFA Module ................................................... 2-30 
2.2 
Working with the Flood Module........................................................................ 2-38 
2.2.1 Counting Damages for Finished or Unfinished Basement .................... 2-39 
******* Unfinished Basements............................................................. 2-39 
******* Finished Basement with Window Elevation Above or 
Below the Adjacent Ground Elevation ................................... 2-40 
******* Finished Basement with Walkout or a Garage Elevation 
Equal to the Adjacent Ground................................................. 2-43 
******* Partially Finished Basement ................................................... 2-44 
2.2.2 Using Topographic Maps with 2-Foot Contour Intervals to 
Estimate First Floor Elevations.............................................................. 2-47 
******* Topographic Map Features ..................................................... 2-47 
******* Contour Interval ...................................................................... 2-49 
2.2.2.3 Estimating the First Floor Elevation ....................................... 2-49 
Table of Contents 
 
 ii 
2.2.3 Other Losses and Non-Traditional Benefits .......................................... 2-53 
******* Damage Estimates for Other Losses and Non-Traditional 
Benefits ................................................................................... 2-53 
******* Acceptable Documentation for Non-Traditional Benefits ...... 2-54 
******* Entering Non-Traditional Benefits Data in the BCA 
Flood Module .......................................................................... 2-56 
SECTION THREE WIND HAZARD MITIGATION ............................................................................. 3-1 
3.1 
Deriving Wind Speed Data for the Hurricane Wind Module .............................. 3-1 
3.2 
Working with the Tornado Safe Room Module ................................................ 3-10 
3.2.1 Identifying the Target Population .......................................................... 3-10 
3.2.2 Calculating Gross and Usable Area ....................................................... 3-14 
3.2.3 Calculating the Maximum Occupancy................................................... 3-15 
3.2.4 Example: Determining Required Usable Area from Maximum 
Occupancy.............................................................................................. 3-15 
SECTION FOUR 
INCORPORATING LOSS OF SERVICES FOR CRITICAL PUBLIC 
FACILITIES ....................................................................................................... 4-1 
4.1.1 Accessing the Critical Facility Function with Flood Module .................. 4-4 
4.1.2 Accessing the Critical Facility Function with DFA Module ................... 4-5 
4.1.3 Determining the Number of People Served by a Critical Facility ........... 4-5 
4.1.4 Determining the Distance (in miles) between Critical Facilities ............. 4-8 
4.1.5 Determining the Type of Area Served by Fire and Police Stations ....... 4-10 
4.1.5.1 Fire .......................................................................................... 4-10 
4.1.5.2 Police....................................................................................... 4-10 
4.1.6 Determining the Number of Police Officers Serving the Same Area 
in the Aftermath of a Disaster ................................................................ 4-11 
SECTION FIVE 
AVAILABLE TECHNOLOGY AIDS ..................................................................... 5-1 
5.1.1 Using Free Layers in Google Earth.......................................................... 5-1 
5.1.1.1 National Flood Hazard Layer.................................................... 5-1 
5.1.1.2 USGS Streamflow Data ............................................................ 5-5 
******* Spatial Calculations................................................................... 5-7 
5.1.2 Using Google Earth Pro License............................................................ 5-11 
5.1.3 Using Hazus for Flood Analysis ............................................................ 5-14 
5.1.3.1 Acceptable Uses of Hazus....................................................... 5-15 
5.1.3.2 Unacceptable Uses of Hazus................................................... 5-22 
SECTION SIX 
REFERENCES .................................................................................................. 6-1 
 
Table of Contents 
 
 iii 
FIGURES 
Figure 2.1: Selecting between the DFA and Flood modules for a flood mitigation project ........ 2-2 
Figure 2.2: Example of an exceedance probability chart........................................................... 2-11 
Figure 2.3: Find exceedance probability of April event ............................................................ 2-14 
Figure 2.4: Sample letter documenting a change in local flow conditions ................................ 2-16 
Figure 2.5: Sample before- and after-development photographs documenting a change in 
land use (Campoli 2001) .................................................................................... 2-17 
Figure 2.6: Sample NFIP flood claim ........................................................................................ 2-23 
Figure 2.7: FFE of an unfinished basement ............................................................................... 2-40 
Figure 2.8: FFE and DDF offset for finished basement............................................................. 2-41 
Figure 2.9: FFE for a finished walkout basement ...................................................................... 2-43 
Figure 2.10: Partial finished basement, divided into two structures .......................................... 2-45 
Figure 2.11: Example of topographic map (Lake County 2011) ............................................... 2-48 
Figure 2.12: Example of a map scale representations................................................................ 2-49 
Figure 2.13: Example of contour lines and contour interval ..................................................... 2-49 
Figure 2.14: Determining building location on a topographic map ........................................... 2-50 
Figure 2.15: Outline the building footprint (shown in green).................................................... 2-51 
Figure 2.16: Measure the height of the first floor slab .............................................................. 2-52 
Figure 2.17: Measure the height to the first floor slab............................................................... 2-52 
Figure 2.18: Sample insurance claim documenting damage ..................................................... 2-55 
Figure 2.19: Sample of repair records for vehicle damage ........................................................ 2-55 
Figure 2.20: Sample photograph showing vehicle damage due to flooding .............................. 2-56 
Figure 3.1: Sample proposed safe room location with 0.5-mile radius ..................................... 3-11 
Figure 3.2 : Sample of U.S. Census Bureau data showing average household size ................... 3-12 
Figure 3.3: Sample of U.S. Census Bureau data showing population 18 years and over .......... 3-13 
Figure 3.4: Example safe room floor plan ................................................................................. 3-16 
Figure 4.1: Hospital example - locations of critical facilities in Montgomery County, MD 
District 3............................................................................................................... 4-7 
Figure 4.2: Hospital example - population for Montgomery County District 3 population ........ 4-7 
Figure 4.3: Hospital example - nearest alternative critical facility .............................................. 4-8 
Figure 4.4: Hospital example - distance from Hospital A to Hospital B ..................................... 4-9 
Figure 5.1: Comparison of NFHL (left) and FEMA Map Service Center FIRMette (right) ....... 5-2 
Figure 5.2: NFHL high altitudes option....................................................................................... 5-3 
Table of Contents 
 
 iv 
Figure 5.3: NFHL medium altitude areas, zones, and boundaries ............................................... 5-4 
Figure 5.4: Proposed project location on NFHL.......................................................................... 5-5 
Figure 5.5: USGS streamflow gages............................................................................................ 5-6 
Figure 5.6: USGS station number display ................................................................................... 5-7 
Figure 5.7: Google Earth Pro toolbar........................................................................................... 5-7 
Figure 5.8: Proposed detour and distance .................................................................................... 5-9 
Figure 5.9: Using Google Earth Pro to determine 0.50-mile radius from the project 
location............................................................................................................... 5-11 
Figure 5.10: Data layers available in Google Earth Pro (U.S.).................................................. 5-11 
Figure 5.11: U.S. demographics using Google Earth Pro .......................................................... 5-12 
Figure 5.12: U.S. parcel data using Google Earth Pro ............................................................... 5-13 
Figure 5.13: Daily traffic counts using Google Earth Pro ......................................................... 5-14 
TABLES 
Table 1: Example of BureauNet NFIP Claims Data .................................................................. 2-25 
Table 2: Example Data Obtained from DFA Module................................................................ 2-30 
Table 3: Example of Approximate Displacement Cost Determination ..................................... 2-35 
Table 4: Example for Residence with Two or More Stories without Basement ....................... 2-43 
Table 5: WSEL and discharge data for 100 Main Street, Louisville, KY from FIS.................. 2-57 
Table 6: Other damages associated with flood depth ................................................................ 2-57 
Table 7: Typical Gross Area Reductions by Room Use ............................................................ 3-14 
Table 8: Maximum Occupant Density for Tornado Community Safe Rooms .......................... 3-15 
Table 9: Applicability of Technology in BCA ............................................................................ 5-1 
Table 10: Hazus MR2 Default Contents Value Based on Percentage of Structure Value ........ 5-18 
Table 11: Hazus MR3 Displacement Costs (2008 Values) ....................................................... 5-20 
 
Acronyms and Abbreviations 
 
 v 
ACV 
Actual Cash Value  
ADT 
Average Daily Traffic 
ASCE 
American Society of Civil Engineers 
AVM 
Automated Valuation Model 
BCA 
Benefit-Cost Analysis 
BCR 
Benefit-Cost Ratio  
BFE 
Base Flood Elevation 
BRV 
Building Replacement Value 
cfs 
Cubic Foot per Second 
DDF 
Depth-Damage Function 
DDT 
Data Documentation Template 
DFA 
Damage Frequency Assessment 
FEMA 
Federal Emergency Management Agency 
FFE 
First Floor Elevation 
FIRM 
Flood Insurance Rate Map 
FIS 
Flood Insurance Study 
GBS 
General Building Stock 
GIS 
Geographic Information System 
H&H 
Hydrology and Hydraulics 
HAG 
Highest Adjacent Grade 
Hazus 
Hazards U.S. 
HEC-SSP  
Hydrologic Engineering Center – Statistical Software Package 
HEC-RAS 
Hydrologic Engineering Centers River Analysis System 
HMA 
Hazard Mitigation Assistance 
HMGP 
Hazard Mitigation Grant Program 
LAG 
Lowest Adjacent Grade 
LOMA 
Letter of Map Amendment 
LOMR 
Letter of Map Revision 
MR2 
Major Release 2 
MR3 
Major Release 3 
MR4 
Major Release 4 
Acronyms and Abbreviations 
 
 vi 
MR5 
Major Release 5 
NAVD 
North American Vertical Datum 
NFHL 
National Flood Hazard Layer 
NFIP 
National Flood Insurance Program 
NGVD 
National Geodetic Vertical Datum 
OMB 
Office of Management and Budget 
RCV 
Replacement Cost Value 
sf  
square feet 
SFHA 
Special Flood Hazard Area 
SHMO 
State Hazard Mitigation Officer 
SOW 
Scope of Work 
UDF 
User-Defined Facilities 
USACE 
U.S. Army Corps of Engineers 
USDA 
U.S. Department of Agriculture 
USGS 
U.S. Geological Survey 
WSEL 
Water Surface Elevation 
 
 
 
Introduction 
 
1-1 
SECTION ONE INTRODUCTION 
1.1 
Purpose 
This guidance document was prepared for the U.S. Department of Homeland Security’s Federal 
Emergency Management Agency (FEMA) under Contract No. HSFEHQ-06-D-0162, Task Order 
HSFEHQ-09-J-0024. The purpose of the guide is to supplement the information available at 
http://www.fema.gov/government/grant/bca.shtm regarding the FEMA benefit-cost analysis 
(BCA) software Version 4.5.5 with additional guidance on performing a BCA using the Damage 
Frequency Assessment (DFA) and Flood modules. All references to BCA software modules in 
this document are for Version 4.5.5. This document explains what constitutes acceptable 
documentation for Hazard Mitigation Assistance (HMA) grant subapplications. In addition, it 
explains additional and alternative methods for determining benefits for some of the most 
common benefit categories for buildings (e.g., building damage, contents damage, displacement 
and loss of function) as well as non-traditional benefits (e.g., agricultural equipment and 
landscaping equipment) not generally discussed in BCA training classes. These methods will 
facilitate better preparation of a BCA for a HMA Program grant application in accordance with 
the Fiscal Year 2011 (FY11) Hazard Mitigation Assistance Unified Guidance: Hazard 
Mitigation Grant Program, Pre-Disaster Mitigation Program, Flood Mitigation Assistance 
Program, Repetitive Flood Claims Program, Severe Repetitive Loss Program (FY11 HMA 
Unified Guidance, FEMA 2010a) and recognize additional benefits not previously considered. 
This document does not replace the formal BCA training offered by FEMA. To schedule 
training, Applicants and subapplicants can contact their FEMA Regional office or State Hazard 
Mitigation Officer (SHMO). 
1.2 
Cost-Effectiveness Analysis Requirements for Hazard Mitigation Assistance 
Program Funds 
To be eligible for HMA Program funding, mitigation projects must be cost effective as 
demonstrated by a FEMA-validated BCA. A BCA evaluates the future benefits (projected losses 
avoided) of a project in relation its cost. The BCA evaluation results in a benefit-cost ratio 
(BCR). If the future benefits are equal to or greater than the project cost, then the BCR is equal 
to or greater than 1.0 and the proposed project is considered cost effective. If the benefits are less 
than the cost, then the BCR is less than 1.0, and the proposed project is not considered cost 
effective. Only projects that demonstrate a BCR of 1.0 or greater are considered for HMA 
funding.  
For projects submitted for funding under the Hazard Mitigation Grant Program (HMGP), an 
expedited cost-effectiveness determination can be made for property acquisition and structure 
demolition or relocation projects when certain conditions are met. Specifically, for structures 
identified in a riverine floodway or Special Flood Hazard Area (SFHA) on the current effective 
Flood Insurance Rate Map (FIRM) and declared substantially damaged due to the impacts of 
flood ing by a local authority having s uch jurisdiction, property acquisition and structure 
demolition or relocation is considered cost effective and a BCA is not required to be submitted 
for the structure.  
 
Flood Hazard Mitigation 
 
2-1 
SECTION TWO FLOOD HAZARD MITIGATION 
Mitigation projects must reduce the potential future damages to a building and its contents, 
infrastructure, and human lives to recognize their benefits. Additionally, loss of function—
displacement of occupants from the building and/or loss of public services—may also be reduced 
by mitigation projects, thereby further increasing the benefits of the project. The FEMA BCA 
DFA and Flood modules are designed to analyze riverine and coastal flood sources and compare 
before- and after-mitigation damages to buildings and/or infrastructure. Each of these two 
modules requires different information. The selection of which module to use is based on the 
type of information available to the user.  
The DFA module is more flexible than the Flood module, and therefore the most frequently used 
module for the HMA Program grant subapplications. The DFA module analyzes proposed 
mitigation projects based on damages (either historical or expected) and future damages avoided. 
The DFA module is extremely useful for projects in areas where high costs were incurred from 
historical flooding events but for which no FEMA Flood I nsurance Study (FIS) is available. The 
DFA module is commonly used to analyze stormwater management and drainage improvement 
projects. It can also be used to analyze much larger projects. The DFA module (Section 2.1) is 
recommended in place of the Flood module (Section 2.2) when key structural information, such 
as the first floor elevation (FFE), or critical flood hazard resources, such as the FIRM, FIS, or the 
base flood e levation (BFE), are not available.  
Another important use for the DFA module is as a secondary analysis method for mitigation 
projects that do not result in a BCR of 1.0 or more in the Flood module. The DFA module can be 
used to analyze projects with documented historical damage records or hydrology and hydraulics 
(H&H) studies that indicate expected flood damages. If the BCR is less than 1.0 for a project 
analyzed in the Flood module, but is equal to or greater than 1.0 using the DFA module, the 
complete and well-documented DFA module can be submitted in a project subapplication instead 
of the Flood module. The subapplication should explain why the DFA module was used in lieu 
of the Flood module. 
The Flood module analyzes proposed mitigation projects based on the effect a storm of a certain 
magnitude will have on buildings before and after the project is implemented.  The Flood module 
software is designed for evaluating individual buildings within a project. The Flood module is 
recommended for BCAs when users have detailed flood hazard information and structural data 
for a project, including FFEs. The default depth-damage functions (DDFs) in the Flood module 
apply to buildings, their contents, and their loss of function and displacement time during and 
after a flood event. The Flood module provides an accurate calculation of risk and maximum 
benefits for mitigation projects.  
Figure 2.1 illustrates the decision-making process for selecting between the DFA and Flood 
modules for flood hazard mitigation projects. Additional information regarding the modules is 
presented in Sections 2.1 and 2.2 and can be obtained at the BCA Web site 
http://www.bchelpline.com/BCAToolkit/index.html.  
Flood Hazard Mitigation 
 
2-2 
 
Figure 2.1: Selecting between the DFA and Flood modules for a flood mitigation project 
Flood Hazard Mitigation 
 
2-3 
2.1 
Working with the Damage Frequency Assessment Module 
The FEMA BCA DFA module is a flexible tool for calculating project benefits and costs for 
proposed mitigation projects when users do not have accurate hazard or structural information. 
The DFA module was developed to calculate project benefits and costs for proposed hazard 
mitigation projects based on either documented historic damages (such as loss of function or 
physical damage) from three or more events with unknown recurrence intervals, or expected 
damages from at least two events of different recurrence intervals. It can be used to analyze a 
variety of facility types including utilities, roads and bridges, and buildings.  
This section includes information on the following common issues encountered when using the 
DFA mod ule:  
 Documenting Historical, Expected, and Residual Damages (Section 2.1.1) 
 Determining Recurrence Intervals (Section 2.1.2) 
This section provides information for users entering expected damages 
 Adjusting Analysis Duration (Section 2.1.3)  
This section provides information for users entering historical damages 
 Using National Flood Insurance Program BureauNet Information (Section 2.1.4)  
This section provides information for users entering historical damages 
2.1.1 Documenting Expected, Historical, and Residual Damages 
The main advantage of the DFA module is its flexibility: it can be used for a wide range of 
hazards and mitigation project types. However, since some flooding events are 20 or 30 years 
old, data entered in the DFA module can be difficult to properly document. Historically, 
subapplications submitted for HMA program grant funding that use the BCA DFA module have 
commonly used values that are insufficiently documented with credible sources, resulting in 
delays in project funding. Users should begin by reviewing the suggested sources of 
documentation provided in the DFA Data Documentation Template (DDT) that is available from 
the BCA Helpline Web site (www.bchelpline.com; click the “Resource Kit” tab and the DFA 
icon).  
Expected Damages 
If historical damage amounts are unknown, engineering calculations can be used to determine 
expected damages from hazard events. Determining the recurrence intervals of historical hazard 
events is problematic because the data are often incomplete, inaccurate, or out of date. As a 
result, many users do not consistently or accurately determine the recurrence intervals of 
historical hazard events, which results in incorrect BCRs. Users should consult with an engineer 
or technical expert familiar with the proposed project to obtain cost estimates for damages 
resulting from at least two hazard events with different recurrence intervals. Calculations for at 
least two hazard events are required for the “Expected Damages” field in the DFA module. The 
applicable sections of the FIS, or the engineering report and H&H analysis must be submitted 
with the subapplication to document inputs used for expected damages in the DFA module. 
Flood Hazard Mitigation 
 
2-4 
Historical Damages 
If historical damages are being used in the DFA module, then at least three events are required, 
and the events must have occurred in different years. 
 Historical damage records for residential or non-residential structures may come from 
BureauNet data (see Section 2.1.4 for a detailed explanation) or repair receipts  
 Extrapolation outside of known data points is not acceptable  
Residual Damage s 
Regardless of whether historical or expected damages are used in the DFA module, the project 
level of effectiveness must be considered. Almost all mitigation projects (with the exception of 
acquisition projects) remain subject to damage from possible future hazard events. These 
damages are called residual damage. Users should consult with the engineer or technical expert 
familiar with the project to obtain level of effectiveness. 
2.1.2 Determining Recurrence Intervals 
To estimate the recurrence interval of a storm event near a project site, it is important to be able 
to retrieve and analyze stream gage data for the closest appropriate stream or river. The U.S. 
Geological Survey (USGS) Water Resources Division is the Federal agency responsible for 
monitoring water data. 
This section outlines a process for determining whether stream gage data are available, for 
retrieving the appropriate data, and for creating an exceedance probability chart for estimating 
the exceedance probability of the storm event. The inverse of the exceedance probability is then 
used to determine the recurrence interval. 
The process outlined in this section is one of many methods available and should not be 
considered the only acceptable method to determine recurrence intervals. For example, the 
recurrence interval of an event can also be determined from rainfall gage data but requires an 
analysis beyond the scope of this document. The limitations and acceptable methods are 
explained in detail in Appendix C of FEMA’s Guidelines and Specifications for Flood Hazard 
Mapping Partners (FEMA 2003). 
The process for using stream gage data is demonstrated using an example storm event that 
occurred in February 1996 at a project site located near the mouth of Issaquah Creek in 
Washington State. The following steps can be adapted to project locations throughout the United 
States: 
 Step 1. Determine if the gage data period of record includes the event of interest 
 Step 2. Retrieve and download the annual peak discharge for the event of interest  
 Step 3. Import annual peak discharges into the USGS PeakFQ program, output an 
exceedance probability chart for the gage, estimate the exceedance probability of the 
event (in percent), and take the inverse of the result to determine the recurrence interval 
2.1.2.1 
Step 1 − Determine Gage Period of Record 
The URL below is the USGS homepage for surface water data, where XX is replaced with the 
two letter state abbreviation (WA for Washington in the example):  
Flood Hazard Mitigation 
 
2-5 
http://waterdata.usgs.gov/XX/nwis/sw  
The first step is to check whether gage data are available for any streams near the proposed 
project site and whether the gage data include the storm event of interest.  
Example: Issaquah Creek, WA 
Background: The project site is near the mouth of Issaquah Creek. The storm event of interest 
occurred in February 1996. 
Approach:  
1. Go to http://waterdata.usgs.gov/wa/nwis/sw. 
2. Click the Daily Data button  
3. Select “Site Name” under the column labeled Site Identifier. The Site Attribute column has 
“Site Type” selected as the default and can be left as is. If desired, users can further narrow 
their search by selecting “County” in the Site Location column. Click Submit. 
 
 
4. Enter the site name. Users can enter the name of the flooding source or the city where the 
project is located. Select match any part. If the “County” box was checked in the previous 
step, choose the county where the project is located. “Site Type” will already be selected. 
 
 
 
Flood Hazard Mitigation 
 
2-6 
5. Scroll down to the Choose Output Format section and select the Brief descriptions option. 
Click Submit. 
 
 
6. The search will display a list of gages matching the criteria entered in the previous steps. 
Select the most relevant stream data. The example search resulted in 10 gages on Issaquah 
Creek. Three of the 10 gages are shown in the screen shot below. Note that gages can go off-
line without prior warning, resulting in variations over time in the number of gages found 
through the search function. 
 
 
Flood Hazard Mitigation 
 
2-7 
Example Prob lem Results: For the example problem, Gage ID#12121600 is the gage nearest to 
the project location. It has a period of record of 45 years for peak discharge (from 1963 to 2009), 
and covers the February 1996 event.  
Discussion 
If it is unclear which gage is closest to the project location, the latitude and longitude information 
provided for each gage should be compared to the project location. The proximity of the gage to 
the project site is important in determining whether flows recorded at the gage represent most or 
all of the flow at the project site. Gages upstream of the project site will not record tributary or 
groundwater inflows that enter the flooding source between the gage and the project site. 
However, if the user’s purpose is to determine the size of an event, a gage upstream or 
downstream of the project can be used as long as the gage is located on the same flooding source 
and was affected by the same storm events. 
Users can determine the period of record from the gage data, but it should be noted that the 
shorter the gage data record length, the higher the uncertainty. This means an exceedance 
probability of 10 percent should be derived from a gage record with a minimum of 10 years of 
data; however, a longer period of record is desirable, as this is more likely to capture cyclical 
variation in annual precipitation. Wet and dry years often occur in cycles lasting several years, 
and the longer the record, the more likely the computed exceedance flows will represent typical 
flow conditions. 
******* 
Step 2 − Retrieve and Download the Annual Peak Discharge for the Event of Interest 
USGS archives annual peak discharges as well as daily mean discharges for the period of record. 
Often a declared event will also be the annual peak. The simplest way to determine the peak 
discharge is to retrieve and download the annual peak discharges from the archives, as 
demonstrated with the Issaquah Creek example. 
Example: Issaquah Creek, WA 
Approach:  
1. Return to the Surface Water homepage, http://waterdata.usgs.gov/wa/nwis/sw 
2. Click on Peak-flow data 
3. Select the option to search by Site Number. Click Submit. 
 
 
 
4. Enter the Gage ID#12121600 and select exact match. Scroll down to Retrieve published peak 
streamflow data for selected sites and choose Table of data. Click Submit. 
Flood Hazard Mitigation 
 
2-8 
5. Review results. The peak discharge for the event of interest will be listed in the table. In this 
case, the February 1996 event was the annual peak and the peak discharge was 2,420 cubic 
feet per second (cfs).  
6. Download the annual peak discharge data by selecting the peakfq (watstore) format button.  
7. Save in .txt format to user’s computer.  
 
 
Discussion 
Note that multiple events could have occurred during the same year and the event of interest may 
not be largest. If the event of interest is not listed as the annual peak, other estimation methods 
will need to be used. These methods are discussed in Section *******.  
******* 
Step 3 − Import Annual Peak Data into PeakFQ Program to Identify Exceedance 
Probability and Return Interval 
The USGS PeakFQ program can provide estimates of instantaneous annual peak flows for a 
range of recurrence intervals, including 1.5, 2, 2.33, 5, 10, 25, 50, 100, 200, and 500 years. It 
also has an output option for an exceedance probability chart. Once an estimate of the 
exceedance probability for the event of interest has been determined, the inverse can be taken to 
determine the recurrence interval. 
Approach 
Follow these steps to use the USGS PeakFQ program: 
Flood Hazard Mitigation 
 
2-9 
1. Download and install the free program from http://water.usgs.gov/software/PeakFQ.  
2. Start the program 
3. Choose File→Open  
4. Select the .txt file downloaded in the previous step (Section *******) and click “Open.” This 
will load the information from the .txt file into the PeakFQ program. The PeakFQ home 
screen will open. The Station Specifications tab will be selected by default. 
 
 
 
5. Select Output Options to configure the output options. Select the checkboxes for Print 
Plotting Positions and Line Printer Plots. Choose BMP as the Graphic Plot Format. Click 
Run PEAKFQ to generate results.  
 
 
 
Flood Hazard Mitigation 
 
2-10 
6. Select the Results tab. An output file with flows corresponding to each recurrence interval 
will be listed under Output File. Click View to view the file. To view an exceedance 
probability chart for the gage period of record, select the gage number under Graphs and then 
View.  
 
 
Using Exceedance Probability Charts to Obtain Return Intervals 
For the purpose of this calculation, exceedance probability is approximately the inverse of the 
recurrence interval. The exceedance probability chart (Figure 2.2) gives exceedance probability 
(in percent) on the x-axis, and the discharge (in cfs) using a logarithmic scale on the y-axis. O n 
the logarithmic, scale 102 is equal to 100 cfs, 103 is equal to 1,000 cfs, and 104 equals 10,000 cfs. 
The exceedance probability is obtained by reading horizontally across from the y-axis until the 
data points are reached, and then reading vertically down.  
The peak discharge for the Issaquah Creek, WA, February 1996 event was 2,420 cfs, According 
to this exceedance probability chart, this event had an annual exceedance probability of 
approximately 20 percent. Since exceedance probability is the inverse of recurrence interval, this 
was a 5-year event. 
Users can also use the U.S. Army Corps of Engineers (USACE) Hydrologic Engineering Center 
– Statistical Software Package (HEC-SSP) to generate exceedance probability charts. This 
freeware program can be downloaded from http://www.hec.usace.army.mil/software/hec-ssp/. 
The advantage of HEC-SSP is that it includes an option to upload the annual peaks through a 
Microsoft Excel file, meaning estimated/unpublished peaks can be added by hand. The 
disadvantage is that it is more complicated than PeakFQ and may require engineering expertise 
and judgment.  
 
Flood Hazard Mitigation 
 
2-11 
 
Figure 2.2: Example of an exceedance probability chart 
******* 
Calculating Peak Daily Discharges Using Mean Daily Discharges 
Users should be aware of the limitations of the process described in Sections ******* to *******. 
There is an approximate 6-month lag time for the annual peak discharges published by USGS 
(i.e., an annual peak for 2010 would not be published until mid-2011). If the event occurred 
recently and there is no published peak for that event, the mean daily discharge will need to be 
scaled up using a daily mean-to-maximum ratio as shown below.  
If the event of interest was not the peak for that year or if the peak has not yet been published, 
mean daily discharges will need to be retrieved and adjusted using a ratio to determine the peak 
daily discharge of the event. The peak daily discharge would then be used to find the exceedance 
probability on the exceedance probability chart (Figure 2.2). 
Example: Issaquah Creek, WA 
Find the recurrence interval of the event that occurred on April 24, 1996. This event will not 
register as the annual maximum for 1996 because the February event was much larger. 
Therefore, the peak daily discharge must be used to determine the exceedance probability. 
Approach:  
Obtain the mean daily discharge for the event as follows. 
1. Select the checkbox for the appropriate gage. 
Flood Hazard Mitigation 
 
2-12 
2. Leave all other fields blank and scroll dow n to the Retrieve USGS Surface-Water Daily Data 
for Selected Sites section. Under Retrieve data for select that data range option and type in 
the appropriate range.  
3. The event of interest is known to have occurred in the second half of the month of April (see 
below where a data range of 1996-04-15 to 1996-04-30 is entered). Choose Table of data 
under output options and click Submit.  
 
 
4. Find the mean daily discharge in the table.  
 
 
Flood Hazard Mitigation 
 
2-13 
5. Repeat Steps 2 and 3 to determine the mean daily discharge corresponding to the annual peak 
event that occurred in February. 
 
 
6. Estimate the peak daily discharge by multiplying the mean daily discharge by the mean-to-
peak ratio (Equation 1). 
Equation 1 
 
Qmax=QavgAmax/Aavg 
where: Qmax is the peak daily discharge for the event of interest 
Qavg is the mean daily discharge for the event of interest  
Amax is the annual peak discharge for that year  
Aavg is the mean daily discharge corresponding to the annual peak discharge for 
that year  
 
The peak daily discharge for the April event is calculated as follows: 
Qmax=(209)(2,420)/(1,870)=270 cfs  
Example Results 
The exceedance probability of the event is then found using the exceedance probability chart 
created by PeakFQ in Section *******. Using the data in Figure 2.3, the exceedance probability of 
the April event is 99.5 percent. The inverse of 99.5 percent is 1; therefore, the recurrence interval 
for the April event is 1 year. 
Flood Hazard Mitigation 
 
2-14 
 
 
Figure 2.3: Find exceedance probability of April event 
2.1.3 Adjusting Analysis Duration 
When the flood recurrence interval of a hazard event is unknown, it can be estimated using an 
unknown frequency calculator integrated in the DFA module; required inputs to the calculator 
are damage values, event years, and year built or analysis duration. An analysis duration value 
can be used in lieu of the year built if it is unknown or the project area has experienced 
significant changes. For example, a project includes a bridge that was reconstructed for larger 
carrying capacity long ago, and the town does not know exactly when the bridge was built and 
has experienced flooding recent years. It is acceptable to use the analysis duration rather than the 
year built for the unknown frequency calculator. The analysis duration is defined as the number 
of years that records of damage, loss, or hazard level (e.g., flood elevation, wind speed) are 
observed for more than 10 years. For structures that are more than 10 years old, the analysis 
duration is typically the age of the structure. For structures that are less than 10 years old or 
when a change in local flow conditions occurred less than 10 years earlier, the analysis duration 
is 10 years.  
When local flow conditions have changed significantly during t he life of a structure, it may be 
appropriate to adjust the analysis duration. To address this situation, an alternate means of 
determining the analysis duration has been established, and the analysis duration value may be 
adjusted in the DFA module. For results from user-entered analysis duration in a grant 
Flood Hazard Mitigation 
 
2-15 
subapplication, acceptable documentation of a significant change in flow conditions must be 
included in the subapplication to support the adjustment of the analysis duration. If the user can 
satisfy this requirement, the analysis duration can be assumed to begin on the date the change 
first occurred if the analysis duration exceeds 10 years. 
******* 
Acceptable Documentation 
Acceptable documentation for a change in local flow conditions to support the adjustment of the 
analysis duration includes:  
 A current and old FIS showing the before and after changes in local flow conditions 
 A Conditional Letter of Map Revision (CLOMR) or Letter of Map Revision (LOMR) 
documenting the change in flow conditions 
 An H&H study that accounts for the change in local flow conditions  
 A letter on community letterhead from an official knowledgeable about the changes in 
local flow conditions (e.g., city engineer, local floodplain manager); see Figure 2.4 
 Aerial photographs of the project area before and after the change in the watershed (see 
Figure 2.5) 
 Other photographs showing development in the vicinity of the mitigation area with 
documentation of when the development occurred 
Figure 2.4 is a sample letter documenting the change in local flow conditions for a fictitious 
flooding source. The sample letter explains the drainage project that caused the changes in local 
flow conditions, including t he year of construction, as well as doc umentation of the water surface 
elevations at the project location before and after the flow change. The letter is provided by a 
local official familiar with the hydrology and hydraulics in the area. 
 
Flood Hazard Mitigation 
 
2-16 
 
Figure 2.4: Sample letter documenting a change in local flow conditions 
Figure 2.5 is an aerial photograph showing significant development in a rural area that could 
change local flow conditions. In this case, the user would need to provide the approximate year 
the development most significantly affected local flow conditions, as well as before- and after-
development photographs. 
Flood Hazard Mitigation 
 
2-17 
 
Figure 2.5: Sample before- and after-development photographs documenting a change in land use 
(Campoli 2001) 
2.1.3.2 
Entering the Analysis Duration in the DFA Module 
The following example illustrates using adjusted analysis duration along with historic flood 
claims data to complete a BCA using the DFA module.  
Example: Residential Property at 1234 Lake Drive, Jacksonville, FL 
Scope of Work 
The goal of the mitigation project is to acquire the residential property at 1234 Lake Drive, 
Jacksonville, FL, to remove the property from the Flood Creek Floodplain. The house, built in 
1970, is a repetitive loss parcel with four reported flood claims in 15 years. The total mitigation 
project cost is $150,000. This project meets the State of Florida’s goal to reduce the number of 
repetitive loss properties. Once the property is acquired, the City will demolish the house, and 
return the property to its original state within the floodplain. A permanent open space and 
conservation easement will be recorded with the title and deed to the property. 
Data Provided 
The following data are provided for the BCA DFA module: 
1. Scope of Work (SOW) and acceptable documentation to properly justify the appropriate 
values entered in the DFA module.  
2. Flood claims data from 1996, 1999, 2001, and 2004, as shown in the table below.  
 
Example of flood claims data from 1996, 1999, 2001 and 2004. The data includes the dates the 
floods occurred, the dollar amounts paid for building damages claims, and the dollar amounts 
paid for building contents claims. 
Damage Year 
Date of Loss 
Building Damage Paid  
Building Contents Paid 
1996 
6/13/1996 
$20,000 
$15,000 
1999 
5/31/1999 
$29,000 
$15,000 
2001 
7/4/2001 
$24,000 
$15,000 
2004 
6/24/2004 
$35,000 
$15,000 
Flood Hazard Mitigation 
 
2-18 
3. BCA analysis year (2011) and year the structure was built. 
4. Project useful life of 100 years (FEMA standard value for acquisition). 
5. Integrated unknown frequency calculator to calculate the recurrence intervals. 
Approach 
1. Create a structure for the property at 1234 Lake Drive and associate the structure with a 
project.  
2. Select Damage-Frequency Assessment on the Mitigation Information screen. Click Save and 
Continue. 
 
 
Flood Hazard Mitigation 
 
2-19 
3. Choose Flood as the hazard and Acquisition as the mitigation type. Select Historical 
Damages as the basis for damages. Enter “4” for the number of damage events and “0” for 
the number of events that have known recurrence intervals. Click Save and Continue. 
 
 
4. On the Cost Estimation Info screen, enter the Project Useful Life as 100 years and the 
Mitigation Project Cost as $150,000. Click Save and Continue. 
Flood Hazard Mitigation 
 
2-20 
5. Using the information from Figure 2.4, enter “17” for the User Input Analysis Duration on 
the Historic Damages-Before Mitigation screen to override the computed analysis duration.  
 
 
6. Click on the View Damages button to view the recurrence intervals determined by the 
unknown frequency calculator. 
 
 
 
Flood Hazard Mitigation 
 
2-21 
2.1.4 Using National Flood Insurance Program BureauNet Data 
BureauNet is a web-based database that contains information on all National Flood Insurance 
Program (NFIP) policies and claims since 1978. The database enables users to retrieve data by 
searching for specific flood insurance policy numbers, property addresses, or owners. These data 
are often useful for determining historic damages to specific buildings resulting from a specific 
flood event. The damage values obtained from BureauNet can be used in the DFA module as 
historical damages. 
******* 
Access to BureauNet 
While access to BureauNet is limited to specific FEMA personnel and various individuals at the 
State level who are granted access based on a need-to-know basis, subapplicants may be able to 
request BureauNet data through the following contacts:  
 Designated State Floodplain Manager. A current list of State Floodplain Managers is 
available on the Association of State Floodplain Managers (ASFPM) Web site at: 
http://www.floods.org/index.asp?menuID=274&firstlevelmenuID=185&siteID=1. The 
designated State Floodplain Manager may have direct access to BureauNet.  
 State Hazard Mitigation Officer. A current list of SHMOs is available on the FEMA Web 
site at: http://www.fema.gov/about/contact/shmo.shtm. The SHMO may have direct 
access to BureauNet or can submit a request to the appropriate FEMA Region to obtain 
BureauNet data.  
If local or State contacts do not have access to BureauNet data, the FEMA Regional office can be 
contacted. Users should initiate contact with one of the above-listed individuals well in advance 
of their deadline to ensure adequate time for personnel to retrieve the data. To facilitate the 
process, a list of property addresses, insurance policy numbers or owner name(s) at the time of 
the flood event (if known), and flood event dates should be provided to the contact person.  
******* 
Flood Insurance Coverage 
Flood insurance protects two types of insurable property: building and contents. Building 
coverage insures the building, and contents coverage insures possessions. Neither building nor 
contents coverage insures the land.  
Building coverage includes:  
 The insured building and its foundation 
 The electrical and plumbing system 
 Central air conditioning equipment, furnaces, and water heaters 
 Above-ground appliances and other items of property that are considered part of the 
building (i.e., refrigerators, stoves, and dishwashers) 
 Permanently installed cupboards, bookcases, cabinets, paneling, and wallpaper  
 Elevator equipment, fire sprinkler systems, light fixtures, plumbing fixtures 
 Permanently installed carpeting or wood flooring over unfinished flooring 
Contents coverage includes:  
Flood Hazard Mitigation 
 
2-22 
 Clothing, furniture, and electronic equipment 
 Curtains 
 Portable and window air conditioners 
 Portable microwaves and dishwashers 
 Carpeting that is not already included in property coverage 
 Clothing washers and dr yers 
Flood insurance is available to homeowners, renters, condominium owners/renters, and 
commercial owners/renters. All policy forms include coverage for buildings and contents; the 
building coverage is required, while contents coverage is optional. Therefore, if contents 
coverage was not purchased by the policy owner, flood insurance claims may list claim 
payments only for building claims.  
******* 
Estimated Flood Damages versus Actual Claims Payments 
Following a damaging flood event, the owner begins the claims process by identifying the 
damaged versus the undamaged property and works with the adjuster to prepare a damage 
estimate. A payment is issued once proof of loss is provided and the insurance claim is 
substantiated. It is important to note that estimated flood damages may differ from the actual 
claims payment. In many cases, the actual claims payment may be lower than the estimated flood 
damages because certain items were not covered under building or contents coverage. As 
described in the following subsections, the actual claims payments should be used for inputting 
into the DFA module, not the estimated flood damages. 
******* 
Interpreting BureauNet Claims Data  
Figure 2.6 shows a sample flood claim for a fictitious single-family residence that sustained 
damage during a 1974 flood. The relevant items to use in the DFA module are annotated and 
discussed below.  
Flood Hazard Mitigation 
 
2-23 
 
Figure 2.6: Sample NFIP flood claim  
 
Flood Hazard Mitigation 
 
2-24 
Note 
Explanation 
 
The Date as of field indicates the date that the flood claim was retrieved from the 
BureauNet database.  
 
The Policy Number field indicates the insurance policy number under which the 
current policy was written.  
 
The Dt of Loss field indicates the date when the flood damage occurred. This date 
should be consistent with the damage year for the events the user includes in the DFA 
module.  
 
This section shows the insured’s name (owner) and the property address.  
 
This section shows information relevant to building coverage and claims payments.  
 RCV stands for Replacement Cost Value and is the cost to replace the damaged property.  
 ACV stands for Actual Cash Value and is the RCV at the time of loss minus physical 
depreciation.  
 Bldg Val is the value of the building, both in terms of RCV and ACV.  
 Bldg Dmg is the estimated cost of building damages on the date of loss, both in terms of 
RCV and ACV.  
 Bldg Paid is the actual claim amount paid out to the owner. If an amount is specified 
under Bldg Final Pay, this is the final building claim amount paid out to the owner due to 
various adjustments.  
 For purposes of obtaining historical damages for the DFA module, it is appropriate to 
select the maximum value among the Bldg Dmg-ACV, Bldg Dmg-RCV, Bldg Paid, and 
Bldg Final Pay. 
 
This section shows information relevant to building content coverage and claims 
payments. 
 RCV and ACV are defined similarly as in Note #5.  
 Cont Dmg is the estimated cost of building contents damages on the date of loss, bo th in 
terms of RCV and ACV.  
 Cont Paid is the actual claim amount paid out to the owner. If an amount is specified 
under Cont Final Pay, this is the final contents claim amount paid out to the owner due to 
various adjustments.  
For purposes of obtaining damages for the DFA module, it is appropriate to select the 
maximum value among Cont Dmg-ACV, Cont Dmg-RCV, Cont Paid, and Cont Final 
Pay. 
Flood Hazard Mitigation 
 
2-25 
******* 
How to Use NFIP BureauNet Data with the DFA Module  
BureauNet flood claims data can be used to complete a BCA using the DFA module with 
historical damages. The following example of an acquisition project illustrates this concept by 
providing step-by-step directions for: 
 Entering building and contents loss data from BureauNet for several events into the DFA 
module 
 Using the integrated unknown frequency calculator to assign recurrence intervals for the 
events 
 Completing the DFA module to determine whether the acquisition project is cost 
effective (BCR is greater than or equal to 1.0) 
 Estimating additional losses in the form of displacement costs using DDFs from the 
Flood module 
 Entering the displacement costs back into the DFA module to complete the analysis. 
The first step is to obtain BureauNet NFIP Flood Claims data. For this example, losses were 
obtained for the years 1974, 1985, 2001, and 2004, as shown in Table 1.  
Table 1: Example of BureauNet NFIP Claims Data 
Damage Year 
Date of Loss 
Building 
Damage Paid 
Building 
Contents Paid 
1974 
6/13/1974 
$16,750.00  
$15,000.00  
1985 
5/31/1985 
$29,490.00  
$15,000.00  
2001 
7/4/2001 
$23,750.00  
$10,000.00  
2004 
6/24/2004 
$34,765.00  
$17,000.00  
 
******* 
Enter NFIP Data into DFA Module and Determine BCR 
1. Once BureauNet data has been organized into the above table, create a project and a structure 
for the property and associate the structure with the project. Select Damage-Frequency 
Assessment on the Mitigation Information screen. Click Save and Continue. 
Flood Hazard Mitigation 
 
2-26 
 
 
2. On the Hazard and Mitigation Info screen, choose flood as the Hazard and acquisition as the 
Mitigation Type. Select Historical Damages as the basis for damages. Enter “4” for the 
number of damage events and “0” for the number of events with known recurrence intervals. 
Click Save and Continue. 
 
  
Flood Hazard Mitigation 
 
2-27 
3. On the Cost Estimation Info screen enter the Project Useful Life as 100 years (for 
acquisition) and enter the Mitigation Project Cost. For this example a mitigation project cost 
of $139,500 is used. Click Save and Continue. 
 
 
4. On the Type of Services screen, select Not Applicable. Click Save and Continue. 
 
 
5. On the Historic Damages Before Mitigation screen, enter the year built (in this example, 
1960). Add a column for “Building $” damages in the Historic Damages Before Mitigation 
table. Use the BureauNet flood claims data to enter the damage years and building damages 
Flood Hazard Mitigation 
 
2-28 
under the Building $ column. Allow the tool to inflate these values by selecting “No” to the 
question “Are damages in current day dollars?” for each damage year. Make note of the Total 
Inflated building damage values for use in subsequent calculations in a separate table. Click 
Save and Continue. 
 
 
6. Add a column for “Contents $” damages. Use the BureauNet flood claims data to enter the 
building contents damages for each damage year into this column. The tool will use the 
integrated unknown frequency calculator to compute the recurrence intervals. The calculated 
recurrence intervals can be viewed by clicking on the View Damages button at the bottom of 
the Historic Damages Before Mitigation table. Make note of the recurrence intervals. 
 
 
 
7. Close the Expected Annual Damages screen and click Save and Continue. 
Flood Hazard Mitigation 
 
2-29 
8. In the Damages After Mitigation screen, it is not necessary to input any information since the 
project involves an acquisition and there are no residual damages because the building will 
no longer exist. Click Save and Continue. 
 
 
9. The structure BCR can be viewed on the Summary of Benefits screen. For this example, the 
BCR is 0.74, indicating that this project is not cost effective when considering only building 
and contents damages. Section ******* describes how to maximize benefits in the DFA 
module. 
 
 
 
 
 
Flood Hazard Mitigation 
 
2-30 
******* 
Using the BCA Full Data Flood Module to Maximize Benefits in the DFA Module 
Benefits in DFA module can be maximized by incorporating displacement costs obtained from 
DDFs in the BCA Full Data Flood module. In the example described in Section *******, inputting 
damages for building and building contents resulted in a BCR of 0.74, which is less than 1.0. The 
following example builds off the BCA completed in Section ******* and illustrates how benefits 
can be maximized to bring the BCR to above 1.0.  
1. Compile the data from the DFA module (Table 2). The table should include data from the 
DFA module including the recurrence intervals determined from the unknown frequency 
calculator (Step 6 of Section *******) and the inflated building damages (Step 5 of Section 
*******). It should also include the total building replacement value (BRV); for this example, 
use $105,600. Add a column for the percent building damage. Divide the inflated building 
damages by the total BRV for each event to determine the percent building damage. This 
data will be used to find displacement costs associated with damages from the flood claims. 
Table 2: Example Data Obtained from DFA Module 
RIs from Unknown 
Frequency Calculator 
Inflated Building 
Damages from DFA 
Total BRV 
Percent Building 
Damage 
13 
$33,015  
$105,600  
31.3% 
17.3 
$43,023  
$105,600  
40.7% 
26 
$61,897  
$105,600  
58.6% 
85.3 
$73,012  
$105,600  
69.1% 
 
2. Return to the BCA Software Project screen and enter Flood as a new mitigation type on the 
Mitigation Information screen. Double-click on the Flood row to begin the Flood module. 
 
 
3. Select Acquisition as the flood mitigation project type. Click Save and Continue. 
 
 
Flood Hazard Mitigation 
 
2-31 
 
4. On the Full Flood-Questionna ire screen indicate that an FIS is available. Answer “Yes” to all 
of the follow-up questions on this screen.  
Note that the only information needed from this module is the DDFs and therefore other 
information is not important. The Flood module will not actually be used to evaluate the cost-
effectiveness of the project. Click Save and Continue. 
 
 
5. Skip input on the Flood Data Source screen, since the data are for information only and do 
not affect calculation of the BCR or the DDFs. Click Save and Continue. 
 
6. Enter data on the Structure Information screen. This data affects which DDF is selected. 
Enter the total size of the building, BRV, and residential structure details. For this example 
the residential building has two stories, no basement, a building size of 1,600 square feet (sf), 
and a BRV of $66 per sf. The Demolition damage threshold should be entered as “50%” for a 
residential building. Click Save and Continue. 
Flood Hazard Mitigation 
 
2-32 
 
 
7. On the Residential Structure Information screen select “USACE Generic” from the DDF 
drop-down menu. The values for the DDFs will auto-populate. Select the default button for 
Displacement Costs and Building Contents. 
 
 
8. On the Depth Damage Functions screen read the results on the Building tab. The USACE 
generic DDF for residential buildings shows building damages based on flood depth. A 
building DDF is an estimate of damage that will occur to a building based on the depth of 
flooding and as a percentage of the BRV. For this example, the DDF calculates the estimated 
building damages associated with 1 foot of flooding as $16,051 (or 15.2 percent of the BRV). 
At 9 feet of flooding the structure would be more than 50 percent damaged; buildings that are 
Flood Hazard Mitigation 
 
2-33 
50 percent damaged are considered a total loss, and the building damage would be equal to 
the BRV.  
 
 
 
9. Read the results on the Displacement tab of the Depth Damage Functions screen. The curve 
for the USACE Generic DDF shows displacement time in days and cost in relation to the 
flood depth. In this example, displacement for a 1-foot flood depth is 45 days and over a year 
for a 9-foot flood depth. 
 
 
Flood Hazard Mitigation 
 
2-34 
 
10. Using the percent building damage value calculated in Table 2 (see Step 1), find the closest 
percentage listed in the DDF under the Building tab for each of the known recurrence 
intervals. In this example, use the 13-year flooding event on Table 2. Per Table 2, the percent 
building damage for the 13-year flooding event is 31.3%. Compare this to the DDF Building 
tab data. The closest DDF building damage value is 31.4%, which corresponds to a 4.0-foot 
flood depth. Repeat this step to determine the flood depth for each of the percent building 
damage values shown in Table 2 (interpolate for damage percentages between the listed 
values).  
 
 
 
11. Use the DDF flood depths to find the before-mitigation displacement cost by selecting the 
Displacement tab on the Depth Damage Functions screen. A 4.0-foot flood depth results in 
180 displacement days, which corresponds to a displacement value of $13,635. Keep track of 
the displacement values by updating Table 2 (refer to Table 3). 
 
Flood Hazard Mitigation 
 
2-35 
 
 
Table 3: Example of Approximate Displacement Cost Determination 
From Table 2 (Step 1) 
From Steps 2 - 11 
RIs from 
Unknown 
Frequency 
Calculator 
Inflated 
Building 
Damages 
from DFA 
Total 
BRV 
Percent 
Building 
Damage 
Closest 
Percentage 
Listed in 
the DDF 
Associated 
Flood 
Depth (ft) 
Approximate 
Displacement  
Cost 
13 
$33,015  
$105,600  
31.3% 
31.4% 
4.0 
$13,635  
17.3 
$43,023  
$105,600  
40.7% 
40.7% 
6.0 
$20,452  
26 
$61,897  
$105,600  
58.6% 
58.7% 
11.0 
$37,495  
85.3 
$73,012  
$105,600  
69.1% 
69.2% 
16.0 
$54,539  
 
12. Return to the Project Structures Summary screen and create a new structure within the 
project to enter the approximate displacement costs corresponding to the four recurrence 
intervals. The software will add the benefits from the two structures to determine an 
aggregated BCR. 
The user should understand that this new structure does not represent an actual physical 
building. It must be created because two different analysis methods were used to evaluate 
losses. The structure containing building and contents losses was analyzed using historical 
damages and “0” for the number of events that had known recurrence intervals. The software 
manually inflated the building and contents losses to present-day values. For the structure 
containing displacement costs, the values will be entered as expected damages with known 
recurrence intervals. The values are already present-day values and if they were entered 
simply as a new column in the first structure, they would have been inflated incorrectly. 
Flood Hazard Mitigation 
 
2-36 
 
 
13. Within the new structure, select DFA on the Mitigation Information screen. 
14. On the Hazard and Mitigation Information screen, the hazard and mitigation type remain 
unchanged. The user should select Expected Damages as the basis for the damages and enter 
“4” for the estimated damage events and “4” for the events with known recurrence intervals. 
Click Save and Continue. 
 
 
 
15. On the Cost Estimation Info screen, enter the Project Useful Life (years) as 100 years. Since 
the software will aggregate both costs and benefits from the two structures, enter the 
Mitigation Project Cost as “$0” to avoid double-counting project costs. The mitigation 
project cost was already entered in the first structure using the DFA module. Click Save and 
Continue. 
 
Flood Hazard Mitigation 
 
2-37 
 
 
16. On the Type of Services screen, select Not Applicable. Click Save and Continue. 
 
 
 
 
 
 
 
 
 
17. On the Expected Damages Before Mitigation screen, enter the four recurrence intervals 
shown in Table 3 (Step 11). Enter the Year Built. Add a column for “approximate 
displacement costs” under Expected Damages Before Mitigation, and enter the displacement 
cost values from Table 3. Click Save and Continue.  
 
 
18. Since this example is an acquisition project, there are no expected displacement costs after 
mitigation because the building will no longer exist. Click Save and Continue. 
Flood Hazard Mitigation 
 
2-38 
 
19. The additional displacement cost benefits can be viewed as Benefits Minus Costs on the 
Summary of Benefits screen.  
 
 
20. Review the Project Inventory screen for the aggregated benefits from both structures that 
make up the project and the resulting project BCR. Note that inclusion of the displacement 
costs calculated through use of the Flood module result in an increase of the project BCR 
from 0.74 (refer to Step 9 of Section *******) to 1.01.  
 
 
2.2 
Working with the Flood Module 
The Flood module analyzes proposed mitigation projects based on flood hazard conditions of 
riverine and coastal flood sources before and after implementing mitigation. The Flood module is 
designed for evaluating individual buildings within a project. The Flood module is recommended 
for BCAs when users have detailed flood hazard information and structural data. The following 
describes the essential flood hazard and structural data required to use the Flood module. 
Flood Hazard Information 
 FIRM with cross-sections and a flood profile 
 H&H study (if the project area is unmapped or outside the SFHA)  
 Streambed elevation (riverine flood hazard analysis only) 
 Stillwater elevations (coastal flood hazard analysis only) 
 BFE  
 Flood elevations for the 10-, 50-, 100-, and 500-year recurrence intervals 
 Flood discharge rates for the 10-, 50-, 100- and 500-year recurrence intervals (riverine 
flood hazard analysis only)  
Flood Hazard Mitigation 
 
2-39 
Structural Information 
 FFE for a building affected by riverine flooding or located in a Coastal A Zone 
 FFEs of the lowest floo r member for buildings located in coastal  Zone V 
 Building type and size of building 
 BRV 
 Foundation type 
Over the past several National Technical Review periods, hundreds of flood mitigation grant 
subapplications have been submitted for the HMA funding and about 40 to 50 percent failed to 
demonstrate cost effectiveness. Common errors made in the BCA using the Flood module 
include selecting an incorrect building type and size when there is a partially finished basement, 
using incorrect FFEs, and over-estimating the loss of public services. This section includes 
information on how counting damages for finished and unfinished basements, using topographic 
maps with 2-foot contour intervals to estimate FFEs, incorporating loss of services for critical 
public facilities, and how to calculate other losses and non-traditional benefits. 
2.2.1 Counting Damages for Finished or Unfinished Basement 
For the purposes of conducting a BCA and applying the various DDFs available in the Flood 
module, the basement is considered finished if there are documented features similar to the main 
(first) floor, including finished flooring (e.g., carpeting, tile, vinyl, wood), finished walls (e.g., 
framing, insulation, drywall, paneling), finished ceilings, finished electrical features (e.g., 
outlets, lighting), fully heated/air conditioned, and typical household contents.   
Acceptable documentation for a finished basement includes: photographs, tax assessments, 
building permits, as-built construction plans, copy of a current insurance policy, and/or detailed 
real estate appraisals. If the basement is fully finished, the elevation of the basement floor may 
be used as the FFE in the BCA Flood module; in this case, the DDFs in the Flood module must 
be adjusted to reflect the elevation that flood water can enter the building.  
******* 
Unfinished Basements 
For a building with an unfinished basement, the FFE is for the first habitable floor, even if the 
cellar, crawlspace, or basement has window wells or door(s) (see Figure 2.7). The building type 
selected for the Residential Structure Details on the BCA Structure Information screen will be 
either one-story with a basement or two-story with a basement. The building, contents, and 
displacement DDFs are used without adjustment. In addition, the term “first habitable floor” 
refers to the first habitable floor above the basement, regardless if the basement is finished or 
unfinished. 
Flood Hazard Mitigation 
 
2-40 
 
Figure 2.7: FFE of an unfinished basement 
The following example illustrates how to calculate damages for an unfinished basement using 
the Flood module. Assume a one-story house with an unfinished basement. The basement floor 
elevation = 980.00 feet National Geodetic Vertical Datum (NGVD) and the first habitable floor = 
988.00 feet NGVD. 
1. Enter 988.00 feet NGVD as the FFE.  
2. Enter the square footage. The square foot age reported for the structure size should be equal to 
the square footage of the first habitable floor.  
3. Select the DDF. The DDF used by the Flood module is based on answers to the software 
questionnaire. If the user selects a DDF other than the default or a DDF from the Library, 
adequate documentation must be provided in the subapplication materials to support the user-
entered adjustments.  
******* 
Finished Basement with Window Elevation Above or Below the Adjacent Ground 
Elevation  
For buildings with a finished basement with the bottom of the window elevations either above or 
below the adjacent ground elevation, the FFE is the top of the basement floor elevation (Figure 
2.8).  
 
 
 
Flood Hazard Mitigation 
 
2-41 
 
 
Figure 2.8: FFE and DDF offset for finished basement  
The finished basement is considered the first floor. For buildings with finished basements, if the 
DDFs are not set to zero below the entry point for flood water, the Flood module will greatly 
overestimate damages and the BCR because it will calculate damages for several feet of flood 
water at elevations that cannot enter the building. Users should perform the following actions to 
prevent this error. 
1. Users should select two or more stories without basement as the building type under 
Residential Structure Details on the BCA Structure Information screen.  
2. Set the building, contents, and displacement DDFs to zero below the lowest opening. This 
can be performed in two ways:  
a) The recommended method is as follows: select the Library button on the Residential 
Structure Information screen under Depth Damage Function Type. Select the DDF, 
and then enter “0” for flood depths below the entry point in the Before Mitigation 
User Entered (Pct) DDF column for the Building, Contents and Displacement tabs 
(recommended method for whole foot offsets) 
b) An alternative is as follows: select the Custom button on the Residential Structure 
Information screen under Depth Damage Function Type. Enter the default DDF 
values in the Before Mitigation User Entered DDF column starting at the flood depth 
that flood water enters the building. DDF values for non-integer flood depths must be 
interpolated.  
After adjusting the DDF, calculation of damages will begin at the elevation of either the lowest 
window opening (for basement windows above grade) or at ground elevation adjacent to the top 
of a below-grade window (i.e., top of the window well). 
Flood Hazard Mitigation 
 
2-42 
The following example illustrates how to calculate damages for a finished basement using the 
Flood module. Using Figure 2.8 as an illustration of a one-story house with a finished basement, 
assume the basement floor elevation = 980.00 feet NGVD, the first habitable floor elevation = 
988.00 feet NGVD, and the bottom of the basement window = 985.00 feet NGVD. Assume that 
the window is not in a window well and the ground adjacent to the window is below the bottom 
of the window.  
1. Set the building type as two or more stories without a basement. 
2. Set the FFE as 980.00 feet NGVD. The basement floor elevation is used as the FFE.  
3. Enter the square footage. The square foot age should equal the square footage of the first 
habitable floor plus the square footage of the basement.  Note the unit cost BRV (in $/sf) may 
need to be adjusted when the area of the finished basement is included so that the combined 
BRV for the basement and the first habitable floor is not greater than the total BRV for a one-
story house with a finished basement (i.e., the BRV using this method cannot exceed the total 
actual BRV). 
4. Adjust the DDFs for building, contents, and displacement so there are zero damages from 
elevation 980.00 through 984.00 feet NGVD. Table 4 (below) shows the adjustments that 
would be made for building damage in this example.  Similar DDF adjustment would be 
made for contents (in percent) and displacement (in days). Notice the DDFs show damages 
starting at the +5.0-foot mark (i.e., the offset), or at elevation 985.00 feet NGVD for all three 
categories.  
 
Flood Hazard Mitigation 
 
2-43 
Table 4: Example for Residence with Two or More Stories without Basement 
 
*Note: Before-mitigation percentages are different for building and contents and are represented as days of displacement. 
These values are not included in Table 4. Dollar values obtained are based on structural information and are only provided as 
an illustration-—dollar values will vary for each specific structure. 
******* 
Finished Basement with Walkout or a Garage Elevation Equal to the Adjacent Ground  
For a building with a finished basement that has a walkout or a garage equal to the elevation of 
the adjacent grade, the FFE is equal to the walkout or garage floor elevation (Figure 2.9).  
 
Figure 2.9: FFE for a finished walkout basement 
-2.0
0.0%
-1.0
0.0%
0.0
0.0%
1.0
0.0%
2.0
0.0%
3.0
0.0%
4.0
0.0%
5.0
$61,902
$36,423
$19,174
6.0
$69,597
$40,869
$23,008
7.0
$76,779
$44,973
$26,843
8.0
$83,448
$48,564
$30,678
9.0
$171,000
$51,813
$34,513
10.0
$171,000
$54,720
$38,347
11.0
$171,000
$57,114
$42,182
Flood Depth (ft)
Before Mitigation 
User Entered (Pct)
Before Mitigation ($) 
BUILDING
Before Mitigation ($) 
CONTENTS
Before Mitigation ($) 
DISPLACEMENT
Flood Hazard Mitigation 
 
2-44 
The following example illustrates how to calculate damages for a building with a finished 
basement using the Flood module. Using Figure 2.9 as an illustration of a one-story house with a 
finished basement, assume the basement floor elevation = 980.00 feet NGVD, the walkout or 
garage elevation = 980.00 feet NGVD, and the adjacent ground elevation = 980.00 feet NGVD.  
1. On the Residential Structure Details tab of the Structure Information screen, select Two or 
More Stories and click “No” to indicate the lack of a basement.  
2. Do not adjust the DDFs; these are used without adjustment by the user.  
3. Enter the square footage of the first habitable floor plus the square footage of the basement. 
Note the unit cost BRV (in $/sf) may need to be adjusted when the area of the finished 
basement is included so that the combined BRV for the basement and the first habitable floor 
is not greater than the total BRV for a one-story house with a finished basement (i.e., the 
BRV using this method cannot exceed the total actual BRV). 
******* 
Partially Finished Basement  
The recommended approach for partially finished basements is to conduct two BCA runs by 
dividing the house into two buildings. For a building with a partially finished basement (i.e., at 
least part of the basement is fully finished), the first FFE is equal to the top of the basement floor 
elevation with the appropriate adjustment described in Sections ******* or *******, and the second 
FFE is equal to the first habitable floor. One building is a “two or more stories without a 
basement” and the other is either a “one- or two-story with a basement.”  
The following example illustrates how to calculate damages for a building with a partially 
finished basement using the Flood module. Assume a 1,000 sf house is one story with a full 
(1,000 sf) basement, where 60 percent (600 sf) of the basement is fully finished and 40 percent 
(400 sf) is unfinished.  Assume a BRV of $120/sf for a total BRV of $120,000 (this includes 
adjustments for both the finished and unfinished basement). 
1. Create two structures within a project for the Flood module. Begin with the BCA module run 
for the first building, which is the part of the house with the finished basement and the first 
floor area directly above it (Building Type A in Figure 2.10).  
 
 
Flood Hazard Mitigation 
 
2-45 
 
 
Figure 2.10: Partial finished basement, divided into two structures 
 
a) On the Structure Information screen, select the Two or More Stories for the 
residential building type and click “No” to indicate the lack of a basement.  
b) Enter data for the Total size of building (sf). The area is the 60 percent of the first 
floor area plus the finished basement area: (600 sf) + (600 sf) = 1,200 sf.  
c) Enter a $60/sf BRV for the total finished areas. This sets the BRV for building A to 
60 percent ($72,000) of the total BRV ($120,000 for Building Types A and B). 
d) Enter the FFE as the top of the finished basement floor.  
e) Adjust the DDFs (described in Section *******) to account for the elevation that flood 
water can enter the house, if applicable. 
 
Flood Hazard Mitigation 
 
2-46 
 
 
2. The second BCA module run will be for the second building representing the unfinished 
basement and the first floor area directly above it (Building Type B in Figure 2.10).  
a) On the Structure Information screen, select building type as One Story and click 
“Yes” to indicate the presence of a basement. 
b) Enter data for the Total size of building (sf). The area is 40 percent of the first floor 
area: (400 sf).  
c) Enter the FFE as the top of the first habitable floor (in this case, the above-grade 
floor).  
d) Enter the adjusted BRV of $120/sf.  This sets the BRV for building B to 40 percent 
($48,000) of the total BRV of $120,000. Note that the sum of the BRV of Building 
Type A and Building Type B cannot exceed the total actual BRV.  
e) The DDFs should not be adjusted.  
f) Enter the project cost. Do not enter the full project cost for both structures because 
that would double-count the project cost. Enter the total project cost for one structure 
and zero cost for the other. 
Discussion 
For situations with vehicle damage or other contents damage (such as home improvement 
equipment or agricultural equipment) due to flooding of a garage, refer to Section 2.2.3.  
Occasionally flood damage can occur in a finished basement and the homeowner elects to leave 
the basement unrecovered from recent flooding events. Subsequent floods may still affect the 
Flood Hazard Mitigation 
 
2-47 
basement, but a lower percentage of building, contents, and displacement damages will occur. In 
analyzing the initial flood damage to the finished basement, documentation must be provided 
that indicates the basement was finished prior to the initial flooding event. Documentation 
includes tax assessments, building permits, as-built construction plans, contractor receipts for 
finishing the basement and/ or estimates for repairing the basement, or a copy of a previous 
insurance policy or real estate appraisals, as well as photographs of the previously finished 
basement and the current basement condition. It is unacceptable to claim the basement was 
previously finished without acceptable documentation. All flood damage events subsequent to 
the initial event that changed the basement from finished to unfinished should be analyzed as if 
the basement is unfinished. 
2.2.2 Using Topographic Maps with 2-Foot Contour Intervals to Estimate First Floor 
Elevations 
The FFE of a building is required for using the Flood module, but is often difficult for users to 
obtain since a FEMA elevation certificate or an FFE from a licensed surveyor may be expensive. 
This section provides an acceptable alternate method for estimating the FFE using detailed 
topographic maps. In order to use this approach, the topographic map must be current and have a 
maximum contour interval of 2 feet. Using a current topographic map ensures that the most 
recent topography for the project area is used to determine the FFE. Especially in steep locations, 
the elevations shown on an outdated map may differ significantly from a current map if the plot 
was graded to construct a building foundation. A maximum 2-foot contour interval ensures that 
the data are detailed enough such that it does not introduce a significant amount of error when 
estimating the FFE. 
While 2-foot contour interval maps are less common than larger interval maps, possible sources 
include USGS mapping, State Geological Survey mapping, engineering drawings, light detection 
and ranging (LiDAR) data, or City and County geographic information system (GIS) 
departments.  
******* 
Topographic Map Features 
A topographic map provides approximate ground elevations typically in the form of contour 
lines. The following are key features of every topographic map: 
Title: The title of the map should indicate that the map is applicable to the proposed 
project area.  
Grid: The grid system of a map, usually in the form of latitude/longitude coordinates or 
Township/Range information, can be used along with the north arrow and features 
identified on the legend to find the approximate building location and o rientation. If the 
latitude and longitude coordinates of the building are unknown, several online resources 
can be used to estimate the coordinates based on the building address.  
Legend: The legend identifies key features on the map, which may include property 
boundaries, water sources, roads, etc.  
Datum: The vertical datum (e.g., NGVD and North American Vertical Datum [NAVD]) 
represents the reference elevation used to create the map. When comparing water surface 
elevations and FFEs, care must be taken to identify the datum in case the elevations from 
Flood Hazard Mitigation 
 
2-48 
one datum need to be converted to another. Online resources are available to convert 
elevations if the latitude and longitude coordinates are known. 
Figure 2.11 shows an example of a topographic map available from the Lake County, IL GIS 
Department (Lake County 2011) with the above features identified.  
 
Figure 2.11: Example of topographic map (Lake County 2011) 
The map scale provides the relationship between horizontal distance on the map and the actual 
distance on the ground. Figure 2.12 shows two ways that the scale could be represented. The first 
is a ratio scale. The ratio scale on this map is 1:24,000. This indicates that 1 inch on the map 
represents 24,000 inches (0.38 miles) on the ground. Below the ratio scale is a graphic scale 
representing distance in miles, feet, and kilometers. The space between the 0 and the 1 mile mark 
on the scale is the distance a user must measure on the map to identify a 1 mile distance. 
Map 
Grid 
Map 
Legend 
North Arrow 
Map Title 
Map Scale 
Datum 
Flood Hazard Mitigation 
 
2-49 
 
Figure 2.12: Example of a map scale representations  
******* 
Contour Interval 
Contour lines connect a series of points of equal 
elevation and are used to illustrate relief on a map. 
Contour lines that are close together represent steeper 
terrain, whereas contour lines that are far apart 
represent flatter terrain. A contour interval is the 
elevation change between two consecutive contour 
lines. For example, the topographic map in Figure 2.13 
shows a contour line labeled as 700 feet, followed by 
four unlabeled lines, and a fifth line labeled as 800 feet. 
This indicates that the elevation change between each 
contour line, or the contour interval, is 20 feet. Note 
that this map cannot be used to determine the FFE 
because the maximum contour interval is greater than 2 
feet. 
2.2.2.3 
Estimating the First Floor Elevation 
Step 1: Determine Building Location  
Figure 2.14 shows an example topo graphic map that shows two buildings. Using a combination 
of map features such as roads and aerial imagery, the two buildings were determined to lie in the 
southeast portion of the map.  
Figure 2.13: Example of contour lines 
and contour interval  
Flood Hazard Mitigation 
 
2-50 
 
Figure 2.14: Determining building location on a topographic map 
Step 2: Outline Building Footprint and Specify Location Used For Estimating the FFE 
In Figure 2.15, the livable area of both buildings is outlined in green and the exact location on 
the building used to estimate the FFE is indicated with arrows. Indicating the exact location is 
important for Step 3. When possible, an easily identifiable feature (e.g., a corner, entryway, etc.) 
located directly on a contour line should be chosen as it will avoid the extra step of interpolating 
the ground elevation between contour lines, and may increase the accuracy of the estimate. 
Building 1 
Building 2 
Flood Hazard Mitigation 
 
2-51 
 
Figure 2.15: Outline the building footprint (shown in green) 
Step 3: Determine Ground Elevation at Indicated Location of Building 
For Building 1, the ground elevation at the eastern corner (indicated with a red arrow) is 
approximately 846 feet since the corner lies on the 846-foot contour line. If the north corner were 
used instead, the ground elevation would need to be linearly interpolated. Since the north corner 
lies approximately halfway between the 844-foot and 846-foot contour lines, the ground 
elevation for that corner would be interpolated as 845 feet.  
For Building 2, the corner indicated with a blue arrow lies directly on the 850- foot contour. 
Therefore, the ground elevation is estimated as 850 feet. 
Step 4: Estimate Height from Ground to Top of First Floor Slab  
The difference in height between the ground and the top of the first floor must be measured at the 
indicated location in order to have the most accurate estimation of the FFE. This height may be 
determined by counting bricks, estimating the height from a photograph, or directly measuring 
Building 1 
Building 2 
Flood Hazard Mitigation 
 
2-52 
the height in the field. Photographs must be provided as documentation with the subapplication 
regardless of the method used. 
Figure 2.16 shows that, although the first floor is itself level, the height used to determine the 
FFE from the ground may differ depending on the exact location used for estimating the ground 
elevation. In this figure, the left side of the building is located at the highest adjacent grade 
(HAG) elevation, while the right side is located at the lowest adjacent grade (LAG) elevation. 
 
Figure 2.16: Measure the height of the first floor slab  
As shown in Figure 2.17, the height of the top of the first floor slab for Building 1 is 1.4 feet 
from the ground. For Building 2, the height to the top of the first floor slab is 1.5 feet from the 
ground.  
 
Figure 2.17: Measure the height to the first floor slab 
Flood Hazard Mitigation 
 
2-53 
Step 5: Estimate FFE by Adding Height (Step 4) to Ground Elevation (Step 3) 
In this example, for Building 1, the ground elevation (846 feet) plus the height to the first floor 
slab (1.4 feet) yields an FFE of 847.4 feet.  
For Building 2, the ground elevation (850 feet) plus the height to the first floor slab (1.5 feet) 
yields an FFE of 851.5 feet.  
2.2.3 Other Losses and Non-Traditional Benefits 
The most common benefit categories for mitigation projects include building damage, contents 
damage, displacement costs, and loss of function impacts. For this Guidance, these benefit 
categories will be termed “traditional.” There may be situations where a project is not cost-
effective when only traditional benefits are considered and therefore, it may be advantageous to 
include other non-traditional benefits in the BCA. Non-traditional benefits may include the 
avoidance of damage to landscaping equipment, agricultural equipment, outbuildings and 
vehicles. Non-traditional benefits can only be included for acquisition and flood control 
mitigation projects.  
The following sections identify the types of non-traditional benefits that can be claimed in the 
Flood module and provide information on how to enter these benefits into the module. 
******* 
Damage Estimates for Other Losses and Non-Traditional Benefits 
The following methods, listed in order of accuracy and preference, can be used to determine 
damage estimates for other losses and non-traditional benefits: 
 Method 1. Use historical damage data that affected a structure proposed for 
mitigation.  
For example, a home proposed for acquisition may have documented historical records of 
damage to a lawnmower and a wood shed that occurred at a flood depth of 2 feet (relative 
to the FFE of the house). Automotive damage may only be included when there is 
documented historical damage and only for acquisition projects. These damages can be 
entered in the BCA Flood module at a depth of 2 feet, as shown in Section *******. The 
software will add these user-entered damages to the benefit calculations.  
 Method 2. Use historical damage data from structures that are not part of a 
proposed mitigation project to estimate damages for structures proposed for 
mitigation.  
In this case, the proposed project should have similar structures, equipment, risk, etc. to 
those affected by the historical damages. For example, the home used in the previous 
example could be used to estimate damages for a wood shed and a lawnmower at another 
home provided that the wood shed is similar in materials and construction and the 
lawnmower is a similar type and stored in a similar location.  
 Method 3. Use professional judgment to estimate damage s that are not based on 
historical data.  
Manufacturers of agricultural equipment can make credible estimates for equipment 
damage at varying flood depths. It is important to note that this method cannot be used to 
estimate vehicle damages. 
Flood Hazard Mitigation 
 
2-54 
Credible decision making is critical to making the most accurate damage estimates possible, 
especially for Methods 2 and 3. In addition, all data and assumptions must be supported by 
appropriate documentation. Considerations in making accurate damage estimates include, but are 
not limited to the following: 
 Are flood depths or recurrence intervals (RI) for historical data similar to flood depths 
and RIs used for a damage estimate? For example, historical outbuilding damages for a 
500-year flood should not be used as an estimate of damage for a 200-year flood. 
 Is equipment for the historical data similar to the equipment used for the estimate? For 
example, a gas lawnmower may have damage that differs from an electric lawnmower. 
 Is the hazard condition for the historical damage similar to the hazard condition being 
mitigated? For example, a flash flood or flooding from a substandard flood wall may 
differ from riverine flooding in terms of flows or the ability to move equipment out of 
reach prior to being subject to flooding. 
 Was a manufacturer, mechanic or supplier for equipment consulted for estimating flood 
damage? 
 Consider the mitigation project. Elevating a structure will not reduce damages to 
equipment located in garages and outbuildings; however, acquiring a property or 
reducing flood risk through flood control projects will. 
******* 
Acceptable Documentation for Non-Traditional Benefits 
Acceptable documentation for equipment, outbuildings, and vehicle damage may include:  
 Insurance claims for landscaping and agricultural equipment damage 
 Estimates of flood depths and damages based on a homeowner affidavit 
 Photographs of damaged equipment, outbuildings and vehicles and the associated value 
of the damaged items 
 FEMA Project Worksheets/Damage Survey Reports 
 Records of historical vehicle damages, including repair invoices and insurance claims 
 High water marks, which can be used to determine the flood depth at which damages 
occurred 
 RIs based on FIS, H&H study, stream or tide gauge data, insurance records (if used to 
assess how often events occurred), and newspaper accounts citing credible sources such 
as a public agency 
Figures 2.18, 2.19, and 2.20 show examples of acceptable documentation to support estimates for 
non-traditional benefits. The cost to repair or replace the item may be used in the BCA. Figure 
2.18 shows a sample insurance claim documenting landscaping equipment and outdoor property 
damage. These damages should be associated with a flood depth or the RI of the flood event. 
Figure 2.19 shows a sample of repair records for vehicle damage. Only actual historical vehicle 
damages may be included in the BCA (i.e., Method 1 and 2 only). Figure 2.20 shows a sample 
photograph documenting vehicle damage due to flooding. Photographs may be used to estimate 
the depth of flooding associated with the vehicle damage and can be used in conjunction with 
documentation such as shown in Figure 2.19 to estimate damages. 
 
Flood Hazard Mitigation 
 
2-55 
Flood Insurance Company 
  
Date of Loss: 
10/10/2007 
  
  
  
  
Customer:   John Smith 
Claim 
Number: 
 1234C567 
  
100 Main St.  
Policy 
Number: 
********* 
  
Louisville, KY 
  
  
 
  
Item 
Qty 
Description 
Cost to 
Repair/Replace Depreciation 
Settlement 
Amount 
 1 
1 
Chair 
$3200 
$2600 
$600 
 2 
1 
Lawn 
Mower 
$480 
$390 
$90 
 3 
1 
Weed 
Whacker 
$65 
$10 
$55 
 4 
1 
Hedge 
Trimmer 
$75 
$40 
$35 
 5 
1 
Skill Saw 
$125 
$30 
$95 
Totals 
 
 
$3,945 
$3,070 
$875 
 
Figure 2.18: Sample insurance claim documenting damage  
Invoice 
  
  
Date:  
10/15/2007 
  
  
  
Car Repair Inc. 
  
Louisville, KY 
  
  
  
Bill to: 
John Smith 
  
  
1 Main St.  
  
  
Louisville, KY 
  
  
  
  
  
Quantity 
Description 
Amount 
1 Flood damage repair to drivetrain, cleaning and body repair 
$2,500  
Figure 2.19: Sample of repair records for vehicle damage  
Flood Hazard Mitigation 
 
2-56 
 
Figure 2.20: Sample photograph showing vehicle damage due to flooding  
******* 
Entering Non-Traditional Benefits Data in the BCA Flood Module  
The following example illustrates how to enter non-traditional benefits in the Flood module. 
Non-traditional benefits can be entered in other BCA modules, but these applications are not 
described in this document. 
Example: Acquisition of Residential Property in Louisville, KY 
Scope of Work 
The goal of the mitigation project is to acquire the residential property located at 100 Main 
Street, Louisville, KY in order to remove the property from the Flood Creek floodplain. The 
house, built in 1970, is located in the FEMA-delineated flood Zone AE, and has an FFE of 13 
feet NGVD. The adjacent streambed elevation is 10 feet NGVD. The home is one story on a slab 
foundation without a basement. The total project cost is $150,000. The house is 1000 sf and has a 
BRV of $100/sf. In a 2007 flood event with an unknown RI, in addition to building, contents, 
and displacement damage, the property sustained damage to landscaping equipment and outdoor 
property. The City proposes to acquire and demolish the house, and convert the property to a 
permanent open space and conservation easement. 
Data Provided 
The following data are provided for the Flood module: 
1. SOW and acceptable documentation to properly justify the values entered in the Flood 
module 
Flood Hazard Mitigation 
 
2-57 
2. Water surface elevation, discharge data, and streambed elevation from the FIS as shown in 
Table 5. 
Table 5: WSEL and discharge data for 100 Main Street, Louisville, KY from FIS  
Recurrence 
Interval (year) 
Percent 
Annual 
Chance (% ) 
Elevation Before 
Mitigation (feet)  
Discharge Before Mitigation 
(cfs) 
10 
10% 
14.5 
120 
50 
2% 
16.2 
250 
100 
1% 
17.1 
360 
500 
0.2% 
18.2 
440 
 
3. Insurance records for landscaping equipment, as shown in Figure 2.17 and repair records for 
vehicle damage, as shown in Figure 2.18 associated with flood depths, as shown in Table 6. 
Table 6: Other damages associated with flood depth 
Item 
Damage 
Amount 
Flood Depth (feet)  
Equipment 
$3,945 
0 
Vehicle 
$2,500 
1.5 
 
The associated flood depths were estimated from photographs and homeowner estimates. The 
equipment was assumed to be located at the building FFE. Based on the photographs (Figure 
2.20), the vehicle damages were estimated to occur at 1.5 feet of flooding. 
4. Project useful life of 100 years (FEMA standard value for acquisition) 
Approach 
1. Create a structure for the property at 100 Main Street and associate the structure with a 
project. 
2. Select Flood on the Mitigation Information screen. Click Save and Continue. 
Flood Hazard Mitigation 
 
2-58 
 
Flood Hazard Mitigation 
 
2-59 
3. Choose Acquisition as the mitigation type. Click Save and Continue. 
 
Flood Hazard Mitigation 
 
2-60 
4. Choose Flood Insurance Study (FIS) as the source of flood data and answer Yes to the 
questions that appear after selecting FIS. Click Save and Continue. 
 
Flood Hazard Mitigation 
 
2-61 
5. On the Cost Estimation Info screen, enter the project useful life as 100 years and the 
mitigation project cost of $150,000. Click Save and Continue. 
 
Flood Hazard Mitigation 
 
2-62 
6. On the Flood Data Source screen, enter information for the Effective Date of FIS, FIRM 
panel number, FIRM effective date, and Community ID Number. Click Save and Continue.  
Note that this screen is informational only and will not affect BCR calculation. 
 
Flood Hazard Mitigation 
 
2-63 
7. On the Riverine Discharge and Elevation screen, enter data for the FFE, streambed elevation, 
and flood hazard data. Note that the red exclamation point next to the Enter the First Floor 
Elevation box prompts the user to upload justification or supporting documentation for the 
FFE. 
The FEMA Elevation certificate diagram description, Flood Source Name, and Flood Profile 
Number are for informational purposes only and can remain blank; they will not affect the 
BCR calculation. Click Save and Continue.  
 
Flood Hazard Mitigation 
 
2-64 
8. On the Structure Information screen enter information for the building including square 
footage, BRV, and structure details. Note that the red exclamation points prompt the user to 
upload justification or supporting documentation for the building size and BRV. 
The Total value of building (BRV) will be calculated automatically by the software. 
Demolition damage threshold should be left at the default 50 percent. Click Save and 
Continue.  
 
Flood Hazard Mitigation 
 
2-65 
9. On the Residential Structure Information screen, select the applicable DDF. For this 
example, select “default” under Depth Damage Function Type and “USACE Generic” under 
the drop-down menu for Select Depth Damage Function. The Displacement Costs and 
Building Contents should be left as “default.” Click Save and Continue. 
 
 
 
Flood Hazard Mitigation 
 
2-66 
10. Click the button shown in screenshot to add a column on the Damages Before and After 
Mitigation screen for both vehicle damage and equipment damage.  
 
 
 
 
 
 
Flood Hazard Mitigation 
 
2-67 
11. Enter the damage amounts at the closest flood depths from Table 6.  
 
12. Click Save and Continue twice to get to the final Summary of Benefits screen and the BCR.  
 
Wind Hazard Mitigation 
 
3-1 
SECTION THREE WIND HAZARD MITIGATION 
There are two FEMA BCA modules that can be used to determine the BCRs for projects that 
provide mitigation against high winds: the Hurricane Wind module and the Tornado Safe Room 
module. 
The Hurricane Wind module is used to analyze the following building mitigation project types: 
window and door opening protection (shutters), load path structural retrofits, roof structural 
retrofits, and acquisitions. In the Hurricane Wind module, a key input is the wind hazard data 
which are included in the module. The default wind speed data (i.e., 3-second gust wind speeds 
for multiple recurrence intervals) were obtained from the FEMA Hazards U.S. Multi-Hazard 
(Hazus) program. The wind speed data are automatically provided based on user input of the 
building location, either by zip code or latitude and longitude coordinates. The user can override 
the default wind speeds and enter wind speed data from other sources. This section describes 
how to input wind speed data from the American Society of Civil Engineers (ASCE) into the 
Hurricane Wind module. 
The Tornado Safe Room module evaluates residential and community safe room mitigation 
projects. The benefits in the module are based solely on providing life safety benefits for the safe 
room occupants. Key input parameters include the maximum occupancy and usable area of the 
proposed safe room. This section defines these parameters and describes how to calculate them 
and enter the values into the Tornado Safe Room module.  
3.1 
Deriving Wind Speed Data for the Hurricane Wind Module  
In order to supply user-entered data for the BCA Hurricane Wind module, users first must derive 
wind speed data using ASCE 7-10 wind speeds (Minimum Design Loads for Buildings and Other 
Structures, ASCE/SEI 7-10). Wind speed data from ASCE 7-10 must be entered into a 
spreadsheet to create a graph. The graph allows users to extract wind speeds between recurrence 
intervals (i.e., derive wind speeds for a 35-year event when only the 25- and 50-year wind speeds 
are known). The graph is used to derive a formula, which calculates the needed wind speeds for 
the BCA Hurricane Wind module. The methodology is applicable for all areas covered by the 
ASCE 7-10 wind speed maps. When applying ASCE 7-10 wind speeds, it is important to note 
that the mitigation project is assumed to be designed according to ASCE 7-10. 
Approach  
 Determine the location of the site on a map showing county boundaries 
 Identify wind speeds at the project location for the following seven recurrence intervals 
use the following maps within ASCE 7-10:  
 Figure CC-1, 10-year MRI 3 sec gust wind speed, pages 584 and 585  
 Figure CC-2, 25-year MRI 3 sec gust wind speed, pages 586 and 587  
 Figure CC-3, 50-year MRI 3 sec gust wind speed, pages 588 and 589 
 Figure CC-4, 100-year MRI 3 sec gust wind speed, pages 590 and 591 
 Figure 26.5-1C, 300-year MRI 3 sec gust wind speed, pages 249a and 249b 
Wind Hazard Mitigation 
 
3-2 
 Figure 26.5-1A, 700-year MRI 3 sec gust wind speed, pages 247a and 247b 
 Figure 26.5-1B, 1700-year MRI 3 sec gust wind speed, pages 248a and 248b 
 Users are encouraged to verify the ASCE 7-10 wind speeds using the ATC Web site: 
http://atcouncil.org/windspeed/ 
1. In a spreadsheet, enter the wind data obtained from ASCE 7-10 for each of the supplied 
recurrence intervals. 
2. Use the Create Graph spreadsheet function to select a Scatter Graph  
 
 
Wind Hazard Mitigation 
 
3-3 
3. The software will insert a blank box. Right click the box and click Select Data  
 
 
4. Click on Select Data Source and then Add  
 
 
 
Wind Hazard Mitigation 
 
3-4 
5. Using the Edit Series box, select the recurrence intervals as the “Series X values” and the 
wind speeds as the “Series Y values.” 
 
 
6. Click OK   
 
 
 
Wind Hazard Mitigation 
 
3-5 
7. Place the cursor over the X Axis, right click the mouse, and select Format Axis   
 
 
8. Under Axis Options select the “Logarithmic scale” and enter “10” for the base and click the 
Close button  
 
 
 
Wind Hazard Mitigation 
 
3-6 
9. Click on one of the data points and right click the mouse. Select Add Trendline  
 
 
10. Select Format Trendline. Under Trendline Options, select Logarithmic  
 
 
 
Wind Hazard Mitigation 
 
3-7 
11. Select Display Equation on chart and then click Close  
 
 
12. Type in the recurrence intervals used in the Hurricane Wind module (10, 20, 50, 100, 200, 
500, and 1,000 years) in any column within the spreadsheet you are working in, then type the 
wind speeds for the 10-, 50-, and 100-year events (the ASCE 7-10 and BCA software 
recurrence intervals match for these three events). 
Wind Hazard Mitigation 
 
3-8 
13. Enter the wind speed equation shown on the developed graph. The X value will be the 
recurrence interval. Round to the nearest wind speed. 
 
 
14. Copy the line equation to the 200-, 500-, and 1000-year events. 
 
 
Wind Hazard Mitigation 
 
3-9 
 
15. Enter the calculated wind speeds from the spreadsheet into the BCA Hurricane Wind module. 
The user enters the data on the Wind Speed tab within the User Entered Wind Speed (mph) 
column for each appropriate recurrence interval. 
 
 
Below is a comparison of the Expected Maximum Wind Speed per Return Period. This can be 
displayed using the Show EANWS button near the center of the screenshot shown above. The 
screenshot below (left side) shows the expected wind speeds as determined by the 
latitude/longitude method using the Hurricane Wind module. The right side shows the expected 
wind speeds using the ASCE 7-10 wind speeds shown previously. 
Wind Hazard Mitigation 
 
3-10 
 
 
3.2 
Working with the Tornado Safe Room Module 
The Tornado Safe Room module is used to determine the BCR for construction of tornado safe 
rooms. The module requires the user to input the maximum occupancy, gross area, and usable 
area of the proposed safe room. These values are dependent on each other and are often 
calculated incorrectly. This section describes identifying the target population, calculating the 
gross and usable area for a safe room, and calculating the maximum occupancy and provides 
suggested sources of data and example calculations. 
3.2.1 Identifying the Target Population 
The target population for a safe room is the population that can reach the safe room within 5 
minutes after notification of an approaching tornado. For tornado community safe rooms, the 
travel limits are 5 minutes for walking and a maximum of 0.5 mile from the safe room for those 
driving; therefore, the population of potential occupants must reside or work within 0.5 mile of 
the safe room. Identifying the target population for a safe room includes delineating the area 
within a 0.5-mile radius of the safe room and then determining the population within that radius. 
To delineate the target area, locate the proposed safe room on a map and draw a circle with a 0.5-
mile radius (refer to Figure 3.1 for an example). The target population is determined from within 
this 0.5-mile radius circle. The target population cannot be forced to cross a highway, railroad, 
river, or any other obstacle that could put them in harm’s way. The use and ope ration of the safe 
room will help determine the population served. (For example, if the safe room is adjacent to a 
school or other large public building, will it be open to the neighboring population during the 
day, evening, and night?) 
 
  
 
Wind Hazard Mitigation 
 
3-11 
 
Figure 3.1: Sample proposed safe room location with 0.5-mile radius  
(Source: USGS Survey Map Data from 2011 aerial photograph from Google) 
 
The second step is to determine the population that might use the safe room. Using the example 
shown in Figure 3.1, assume a safe room is adjacent to a school. The school population may be 
obtained from the school administration, State, or a national database (e.g., 
http://www.schooldatadirect.org/). The school population includes not only the students, but also 
the faculty, staff, volunteers, and visitors. 
If the safe room will only be open to the school, then the adjacent pop ulation (typically 
residential) does not need to be considered. If the safe room will be open to the neighboring 
public, then the population within the 0.5-mile radius needs to be estimated. 
One way to estimate the pop ulation within the 0.5-mile radius is to make use of data from the 
U.S. Census Bureau (http://www.census.gov/). In some cases, the State may have Web sites with 
Proposed Safe Room 
0.5-mile 
Wind Hazard Mitigation 
 
3-12 
tools that can facilitate population calculations. For example, the Missouri Census Data Center 
(http://mcdc.missouri.edu/websas/caps.html) developed an online GIS-based tool that uses the 
2000 U.S. Census data to provide estimates of the population within a 0.5-mile radius of a 
location selected by a user. For areas that have experienced significant growth since the 2000 
Census, users can alternatively count the number of houses within the 0.5-mile radius. The 
population within the 0.5-mile radius then can be estimated by multiplying the number of houses 
by the average household occupancy for the community or county. Average occupancy can be 
obtained from the U.S. Census Bureau, shown in Figure 3.2.  
 
 
Figure 3.2: Sample of U.S. Census Bureau data showing average household size 
If the proposed safe room is located in a school, adjustments may be needed to avoid double-
counting children already included in the school population and counting adults working outside 
the 0.5-mile radius. Adults working outside the radius are presumed to be absent from the 
Wind Hazard Mitigation 
 
3-13 
targeted population during daylight hours and should therefore not be included in the population 
that would use the safe room in daytime hours. 
The following example provides a method to determine the residential daytime population within 
the 0.5-mile radius of a school safe room: 
 Assume there are 100 houses within 0.5 mile of a proposed school safe room 
 U.S. Census data shows 3.18 people per house (Figure 3.2) 
 (3.18 people/house) x (100 houses) = 318 people  
 To subtract the children already in the school, remove the population that are less than 18 
years old. Use U.S. Census data to determine the percentage of population over 18 years 
(Figure 3.3). In this example, 68.8 percent are 18 and over.  
 (318 people) x (0.688) = 219 people 
 Adjust the population for people that work outside the area during the day. Information 
related to this can be determined local employment records, or US Census Bureau data 
(refer to Economic Characteristics for your local area). For this example, assume that 
local employment records show that an estimated 75 percent work outside the 0.5-mile 
radius.  
 (219 people) x (1.0 - 0.75) = 55 people 
 
Figure 3.3: Sample of U.S. Census Bureau data showing population 18 years and over 
 Therefore, for this example, the residential daytime population that may use the proposed 
safe room is estimated as 55 people. This number is then added to the school population 
to estimate the safe room maximum occupancy during the daytime. Additional sources of 
occupancy data are described in the dynamic help function of the Tornado Safe Room 
module software.  
Wind Hazard Mitigation 
 
3-14 
3.2.2 Calculating Gross and Usable Area  
There are two types of areas that must be calculated for the Tornado Safe Room module: gross 
area and usable area. The gross area is the total area of the safe room. It should be shown on the 
proposed safe room design drawings. The usable area is the gross area minus the area of 
obstructions such as columns, partitions and walls, fixed or movable objects, furniture, or other 
equipment and features placed in the safe room. These values are related to each other and are 
often calculated incorrectly. This section describes each of these values and provides detail on 
usable area, which is the area typically miscalculated. 
Most safe room subapplications contain conceptual design floor plans and use the guidelines in 
FEMA 361, Design and Construction Guidance for Community Safe Rooms (Second Edition, 
August 2008) to calculate usable area. Specific calculations for the exact usable area may be 
provided, but are typically not available at this stage in the mitigation grant process.  
An accurate calculation of the usable area is especially important for multi-use safe rooms, such 
as classroo ms and offices. The usable area should not include unused or areas that are normally 
locked such as mechanical rooms, storage closets, or offices. FEMA 361 provides the following 
guidelines to calculate the usable area: 
 Areas of concentrated furnishings or fixed seating: Areas where the furnishings cannot be 
easily moved, including bathrooms, locker rooms, weight rooms, and auditoriums with 
fixed seating. To account for the furnishings, reduce the gross floor area of such rooms 
by a minimum of 50 percent. 
 Areas of unconcentrated furnishings and without fixed seating: Areas where furniture can 
be moved, such as classrooms and offices. Reduce the gross floor area of such areas by a 
minimum of 35 percent. 
 Areas of open plan furnishings and without fixed seating: Reduce the gross floor area of 
such areas by a minimum of 15 percent. 
Refer to Table 7 below for acceptable reductions to gross area for the purpose of calculating the 
usable area for a multi-purpose safe room. 
Table 7: Typical Gross Area Reductions by Room Use 
Room Description 
Gross Area Reduction (% ) 
School Gymnasium 
15 
School Classroom 
35 
Office (unlocked) 
35 
Bathroom 
50 
Locker Room 
50 
Mechanical Room 
100 
Storage Room 
100 
NOTE: Auditorium (with fixed seating): For Occupancy, use the total seating capacity 
plus the open spaces, with proper area reduction per FEMA 361. 
Wind Hazard Mitigation 
 
3-15 
3.2.3 Calculating the Maximum Occupancy 
Once the target population has been identified and estimated, target population count must be 
checked against the maximum occupancy or capacity of the safe room to determine whether the 
safe room has adequate space or must be adjusted to meet the need. It is recommended that the 
safe room be designed (i.e., sized) to accommodate the target population based on the 
requirements of FEMA 361 (refer also to Section 3.2.2). If the safe room is sized for the target 
population in accordance with FEMA 361, the target population is entered in the Tornado Safe 
Room module as the maximum occupancy. However, if the size of the safe room is already 
determined, the analyst will need to determine the number of people that will fit in the proposed 
safe room based on FEMA 361 and compare that estimate with the target population within the 
0.5-mile radius. In this case, the target population is not used in the BCA Tornado Safe Room 
module; instead, the population that can be accommodated by the safe room is entered into the 
module. 
The minimum floor space requirements for safe room occupants are shown in Table 8.  
Table 8: Maximum Occupant Density for Tornado Community Safe Rooms 
Tornado Safe Room Occupant 
Minimum Recommended Usable Floor Area 
(sf per person) 
Standing or Seated 
5 
Wheelchair-bound 
10 
Bedridden 
30 
(Source: Table 3-1, FEMA 361, Design and Construction Guidance for Community Safe Rooms, Second Edition, 
August 2008.) 
3.2.4 Example: Determining Required Usable Area from Maximum Occupancy 
In the following example, the proposed maximum occupancy is known and the user is 
calculating the required usable area. Assuming the proposed occupancy is 350 people, calculate 
the minimum usable area required. 
1. Calculate how many wheelchair spaces are required. Using guidance in FEMA 361, one 
wheelchair space (10 sf/person) is needed for every 200 occupants. Therefore 350 occupants 
will require two wheelchair spaces. The result is a maximum occupancy of 348 standing or 
seated occupants and two wheelchair occupants.  
2. Calculate the required usable area to accommodate the proposed occupancy. 
a) (348 standing occupants) x (5 sf/person) = 1,740 sf required 
b) (Two wheelchair occupants) x (10 sf/person) = 20 sf required 
c) Required total usable area needed = 1,740 sf + 20 sf = 1,760 sf 
The next step is to calculate the gross and usable area of the safe room. Assume the building 
footprint is as shown in Figure 3.4.  
Wind Hazard Mitigation 
 
3-16 
 
Figure 3.4: Example safe room floor plan 
In this example, the total gross area is 1,780 sf, calculated by adding the area of the rooms shown 
in Figure 3.4: 
 Bathroom is 10 feet x 20 feet = 200 sf 
 Mechanical Room is 10 feet x 8 feet = 80 sf 
 Classroom is 50 feet x 30 feet = 1,500 sf 
Using the total gross area of 1,780 sf, calculate the usable area using FEMA 361 guidance as 
follows: 
 Bathroom (50% reduction) = (200 sf) x (0.50) = 100 sf 
 Mechanical Room (100% reduction) = (80 sf) x (1.0) = 80 sf 
 Classroom (35% reduction) = (1,500 sf) x (0.35) = 525 sf 
The total area reductions equal 705 sf. Therefore, the total usable area is calculated as: 
 Gross Area – Total Reductions = Total Usable Area 
 1,780 sf – 705 sf = 1,075 sf  
The final step is to compare the proposed usable area with the required usable area. For the 
example problem, the calculated usable area of 1,075 sf is less than the required usable area of 
1,760 sf and therefore not enough space is provided for the targeted population.  
The “Proposed Usable Area” of 1,075 sf would provide enough space for only 213 occupants. 
Using FEMA 361 requirements of one wheelchair space for every 200 occupants, the 213 can be 
understood to mean: 
 Total occupants = 213 people 
 2 wheelchair bound occupants x 10 sf/person = 20 sf 
 211 standing or seated occupants x 5 sf/person = 1,055 sf 
 
 
Bathroom 
10 feet x 20 feet 
 
 
Mechanical Room 
10 feet x 8 feet 
Classroom 
50 feet x 30 feet 
 
Wind Hazard Mitigation 
 
3-17 
 
For this example, there would be two possible choices:  
1. Redefine the maximum occupancy as 213 people. 
2. Redesign the safe room to a larger size to accommodate the proposed maximum occupancy 
of 350 people. Redesigning the safe room would be recommended in this example case since 
the safe room is for a school and the school population (students, teachers, staff, and visitors) 
is greater than 213.  
 
Incorporating Loss of Services for Critical Public Facilities 
 
4-1 
SECTION FOUR  INCORPORATING LOSS OF SERVICES FOR CRITICAL PUBLIC 
FACILITIES 
All FEMA BCA Version 4.5.5 modules count loss of function benefits for public facilities (i.e., 
public library or city hall) in a hazard situation where that facility can no longer provide its 
services to the general public. For most public/nonprofit sector buildings, the value of services 
lost when the building becomes unusable due to a hazard event is calculated by assuming that 
services are worth what the public pays to provide the services. This value is based on the annual 
operating budget of the agency providing the service. However, the BCA software treats critical 
facilities (i.e., fire stations, hospital emergency rooms, and police stations) differently from those 
public facilities due to their importance to life-safety/rescue services to the community in the 
aftermath of a disaster.  
While varying FEMA definitions may exist, for the purposes of the BCA software and analysis, 
critical facility service types include fire stations, hospital emergency rooms, and police stations. 
The BCA software estimates a societal cost for the loss of such critical facilities based on the 
service population and assesses the societal benefits of maintaining the critical facility in the 
aftermath of a disaster. The critical facility analysis is therefore based on the estimate of the 
population served by both the critical facility and the impact a disaster would have on that 
facility. Note that if the analysis is claiming the loss of critical services benefits, the loss of 
function value will need to be closely analyzed. In some cases claiming loss of critical services 
benefits and loss of function benefits is double counting.  
The BCA software requires specific information regarding critical facilities in order to calculate 
benefits from mitigation projects related to reduction of loss of service. The following 
information is required and is often misunderstood by users of the BCA software and is 
explained in more detail in this section: 
 Number of people served by the critical facility 
 Distance in miles between the critical facility and the alternate source of equivalent 
services 
 Type of area served 
 Number of police serving in the aftermath of a hazard event 
Fire Stations 
The analysis performed by the BCA software for the specific loss of service for a Fire Station is 
based on the cost to society of a temporary loss of function of a Fire Station with respect to 
human injuries and mortality, direct financial loss to property, and indirect losses. The analysis 
factors in the distance to the next closest fire station that would provide protection for the 
geographical area normally serviced by former station. Fire station facility types include 
firefighting, search and rescue, public shelter, and emergency medical services if they are located 
in the same facility.  
 Case 1: Loss of fire services only  
 Use only the loss of critical services function in the software 
Incorporating Loss of Services for Critical Public Facilities 
 
4-2 
 Case 2: Loss of fire services located in buildings with additional noncritical services (e.g., 
city halls, other government buildings, etc.) 
 Use the loss of critical services function for fire and the annual operating budget 
applicable to all other noncritical services lost excluding fire 
 
 
Hospital Emergency Rooms 
The analysis performed by the BCA software of the specific loss of service for a hospital is 
based on the cost to society of a temporary loss of function of a hospital’s emergency department 
with respect to extra travel time to an alternative hospital and any additional waiting time at that 
alternative hospital, with the potential cost in lives resulting from that situation. Similar to the 
Fire Station analysis, Hospital analysis factors in the distance to the next closest hospital that 
would treat people normally treated by the former hospital. 
Incorporating Loss of Services for Critical Public Facilities 
 
4-3 
 
 
 Case 1: Loss of the emergency room only 
 Use the loss of critical service function in the software 
 Case 2: Loss of services (e.g., intensive care, radiology, exam rooms, patient care wards, 
etc.) excluding the emergency room 
 Use the portion of the annual operating budget applicable to the services lost and not 
the loss of critical services function  
 Case 3: Loss of both the emergency room and other services outside of the emergency 
room 
 Use the loss of critical services function for the emergency room and the annual 
operating budget applicable to the other services lost excluding the emergency room 
Police Stations 
Police Station analysis is based on the cost to society of a temporary loss of function of a Police 
Station from the perspective of how a reduced police presence would affect the population of 
that area. This analysis is based on the difference of a normally staffed police force and that same 
reduced force available in a post-disaster situation in the identical geographic area. 
 Case 1: Loss of police services only  
 Use only the loss of critical services function in the software 
 Case 2: Loss of police services located in buildings with add itional noncritical services: 
(e.g., city halls, other government buildings, etc.)  
 Use the loss of critical services function for police and the annual operating budget 
applicable to all other noncritical services lost excluding police  
Incorporating Loss of Services for Critical Public Facilities 
 
4-4 
 
 
4.1.1 Accessing the Critical Facility Function with Flood Module   
1. On the Structure Information screen, select “No” in response to the question “Is the building 
Residential?” Note that access in the Hurricane Wind module is similar to that described 
here. 
 
Incorporating Loss of Services for Critical Public Facilities 
 
4-5 
 
4.1.2 Accessing the Critical Facility Function with DFA Module 
1. On the Type of Service screen, select Non Residential Buildings facility type for loss of 
function.  Note that access in the Earthquake modules is similar to that described here. 
 
 
4.1.3 Determining the Number of People Served by a Critical Facility 
Analyzing the societal impact of the loss of a critical facility on the population it serves is done 
by comparing the distance of the critical facility to the population it serves with the distance of 
Incorporating Loss of Services for Critical Public Facilities 
 
4-6 
that population to the next closest critical facility (with equivalent services). The analysis 
assumes the next closest critical facility would be called upon to expand their service range in the 
event of a disaster resulting in the loss of the first critical facility. The value entered for the 
population served by each critical facility is strictly the population served by that critical facility, 
not the population of an entire city or county with multiple critical facilities. The special services 
provided by each facility should be specified. 
Information regarding the number of people served by a critical facility (and the alternate critical 
facility) can be obtained from the municipality, city/community officials, facility operations 
managers, or documents such as annual reports. If these methods are unproductive, a reasonable 
alternate methodology may be used; if an alternative approach is used, a description of that 
approach must be included in the subapplication.  
Most large cities are separated into Districts and detailed population information for each District 
is generally available. However, since hospitals are typically not operated by cities, the 
population served by each may be a complicated value to determine. Cities may have multiple 
hospitals, and some hospitals serve multiple communities. Some information, such as the number 
of beds and number of doctors per patient, may be found in annual reports for the hospital or on 
the hospital Web site. Contact the hospital to obtain their estimate on per capita served. 
Community planners and/or community officials may also be able to provide these estimates.  
Example: Hospital in Montgomery County, MD  
Figure 4.1 shows an example of a District’s distribution within Montgomery County, MD, and 
the hospital within that District. In this example, only one hospital is shown on the map, and a 
user could assume the number of people served by this critical facility, outside of available 
information from the hospital itself, would be the population of District 3 (Figure 4.2). 
 
Incorporating Loss of Services for Critical Public Facilities 
 
4-7 
 
Figure 4.1: Hospital example - locations of critical facilities in Montgomery County, MD District 3 
(source: http://www.montgomerycountymd.gov/csltmpl.asp?url=/content/council/mem/district_map.asp) 
 
 
Figure 4.2: Hospital example - population for Montgomery County District 3 population 
(source: http://www.montgomerycountymd.gov/content/council/redistricting/Handouts/adjmoco_adjpop.pdf) 
 
Incorporating Loss of Services for Critical Public Facilities 
 
4-8 
4.1.4 Determining the Distance (in miles) between Critical Facilities 
Facility operations managers or municipal officials can supply information regarding the location 
of the alternate facility and the distance (in miles) between critical facilities. Distance (in miles) 
is the shortest distance travelling by vehicle along roadways. Local maps or GPS software may 
be used as documentation of the distance. Generally, mapping software can search fire stations, 
hospitals, and police stations in a geographic area to determine the location of the nearest 
alternative critical facility (Figure 4.3).  
 
Figure 4.3: Hospital example - nearest alternative critical facility 
 (source: http://www.montgomerycountymd.gov//content/gis/images/gallery/councildistrict1.pdf) 
 
 
Mapping software can also usually get directions from location A to location B along suggested 
routes of shortest travel distance or estimated time as shown in Figure 4.4, distance from hospital 
A to hospital B. 
Incorporating Loss of Services for Critical Public Facilities 
 
4-9 
 
Figure 4.4: Hospital example - distance from Hospital A to Hospital B 
Example: Hospital in Montgomery County, MD  
Using the example of the hospital in Montgomery Count y, MD, the following data can be input 
to the BCA Flood module. 
1. On the Buildings page, enter the number of people served by the hospital (197,661, per 
Figure 4.2) 
2. Enter the number of miles to the nearest alternative hospital (10.5 miles, per Figures 4.3 and 
4.4) 
3. Enter the number of people served by the alternative hospital (185,462, per Figure 4.2) 
Incorporating Loss of Services for Critical Public Facilities 
 
4-10 
 
 
4.1.5 Determining the Type of Area Served by Fire and Police Stations 
4.1.5.1 
Fire 
Users must select the type of area served by a fire station, which is categorized as urban, 
suburban, rural, or wilderness. The definition of each category is based on the “Urban Influence” 
coding system used by the United States Department of Agriculture (USDA) and the Office of 
Management and Budget (OMB). These codes take into account county population size, degree 
of urbanization, and adjacency to a metropolitan area or areas. The categories are defined as 
follows:  
 Urban: Counties with large (more than 1 million residents) or small (less than 1 million 
residents) metropolitan areas  
 Suburban: Micropolitan (with an urban core of at least 10,000 residents) counties 
adjacent to a large or small metropolitan area  
 Rural: Non-core counties adjacent to a large or small metropolitan area (with or without 
town) 
 Wilderness: Non-core counties not adjacent to micropolitan counties (with or without 
town) 
One such reference for determining the type of area served includes the USDA Data Set: Urban 
Influence Codes available at: http://www.ers.usda.gov/Data/UrbanInfluenceCodes/. 
4.1.5.2 
Police 
Users of the BCA Flood module must enter the type of area served by a police station. The user 
must select between metropolitan, city, or rural.  
Based on data tables from the Federal Bureau of Investigation (FBI 2006), crime statistics are 
sorted into three statistical areas:  
Incorporating Loss of Services for Critical Public Facilities 
 
4-11 
 Metropolitan 
 Cities outside metropolitan areas 
 Nonmetropolitan counties 
Users should select “Metropolitan” for police stations that serve a principal city or urbanized 
area with a population of at least 50,000 inhabitants. The area served includes the principal city, 
the county in which the city is located, and other adjacent counties that have, as defined by the 
OMB, a high degree of economic and social integration with the principal city and county as 
measured through commuting.  
Users should select “City” only if the area served by the police station is a city outside of a 
metropolitan area.  
Users will not generally select “Rural” as it applies to those areas that are outside of the 
metropolitan area and composed of mostly unincorporated areas. 
4.1.6 Determining the Number of Police Officers Serving the Same Area in the Aftermath of 
a Disaster 
Users must enter the number of police serving in the same area in the aftermath of a disaster. 
This value is best estimated by the operations managers, the municipality, city/community 
officials, or documents such as emergency response or continuity of operations plans. If these 
methods are unproductive, a reasonable alternate methodology can be used to determine the 
number of police officers. If an alternative methodology is used, the subapplication material 
should include all assumptions used in the calculations. 
Available Technology Aids 
 
5-1 
SECTION FIVE AVAILABLE TECHNOLOGY AIDS 
The purpose of this section is to provide information about publically available tools that can be 
used to obtain information needed to complete the DFA, Flood and Tornado modules. FEMA 
does not endorse these tools over similar products and does not require that they be used. 
The tools described in this section include Google Earth and the Hazus software. There are 
several free Google Earth utilities, including the FEMA National Flood Hazard Layer (NFHL) 
and the USGS Streamflow Data layer. There are also several useful utilities that are available 
through Google Earth if a license is purchased; these include utilities for demographics, parcel 
data, and daily traffic counts. Table 9 shows the types of information that can be obtained using 
each tool. 
Table 9: Applicability of Technology in BCA  
Data Type 
Google Earth - 
Free 
Google Earth – 
Pro License 
Hazus 
(Section 5.1.1) 
(Section 5.1.2) 
(Section 5.1.3) 
Flood Hazard Information 
X 
  
X  
Road/ Bridge Information 
X 
X 
  
Safe Room Radius 
X 
  
  
U.S. Demographics 
  
X 
  
Building Information 
  
X 
X  
 
5.1.1 Using Free Layers in Google Earth 
The documentation tools that are discussed in this section are layers of the Google Earth 
application (NFHL, USGS Streamflow Data, and Spatial Calculations) and the Google Earth Pro 
license. For Google Earth utilities, Google Earth must be installed on the user’s computer, and 
the user must have a high-speed Internet connection. Note that the screenshots shown in Section 
5.1.1 were taken from Google Earth Pro (a licensed version of the software described in Section 
5.1.2), but these functions are available on the free version of Google Earth. 
5.1.1.1 
National Flood Hazard Layer  
According to FY11 HMA Unified Guidance (FEMA, 2010a), “The required documentation 
depends upon the nature of the proposed project and may include: proposed schematics, 
drawings or sketches, photographs, maps, sections of hazard maps, a Flood Insurance Study 
(FIS), or a FIRM” and “subapplicants should identify the proposed project location on a map.”  
The FEMA NFHL is a Google Earth utility that can be used to provide data for applications 
prepared for submittal to the HMA Program. A basic knowledge of Google Earth and FEMA 
flood hazard information is recommended for users of this tool. FEMA NFHL: View Custom 
Combinations of FEMA Flood Hazard Information Using Google Earth, available at 
http://www.fema.gov/library/viewRecord.do?id=3289, is an excellent resource for the NFHL. 
Available Technology Aids 
 
5-2 
See https://hazards.fema.gov/femaportal/wps/portal/NFHLWMSkmzdownload for downloading 
instructions. 
The FEMA NFHL can be used for proposed riverine and/or coastal flooding mitigation projects 
to obtain the proposed project location, hazard type, and BFE. The NFHL also provides the 
nearest cross section or transect, which can be used to find information in the FIS. In the Map 
Service Center FIRMette, the index and FIRM panel number must be searched to find the 
proposed project location, but in the NFHL, a map pin can be attached to the proposed project 
location and the FIRM panel number can then be obtained by zooming in on the location. 
Another difference is that FIRMettes are in gray-scale, while the NFHL is in color and includes 
other landmarks (see Figure 5.1).  
 
 
Figure 5.1: Comparison of NFHL (left) and FEMA Map Service Center FIRMette (right) 
Available Technology Aids 
 
5-3 
The NFHL indicates whether a proposed project location has flood hazard data.  
1. Select the Status of Digital Flood Hazard Data Coverage “high altitudes option” in the left 
panel (Figure 5.2). 
 
Figure 5.2: NFHL high altitudes option 
Available Technology Aids 
 
5-4 
2. Zoom to a medium altitude (Figure 5.3) to view the SFHAs, other zones, floodways, 
communities, and the FIRM and LOMR boundaries. These areas, zones, and boundaries are 
important in locating the appropriate FIRM and associated FIS.  
 
Figure 5.3: NFHL medium altitude areas, zones, and boundaries 
3. Zoom to a low altitude to see BFEs, cross-sections, coastal transects, hydraulic and flood 
control structures, and areas affected by a Letter of Map Amendment (LOMA) or Letter of 
Map Revision (LOMR). 
Available Technology Aids 
 
5-5 
4. Using the NFHL allows the user to delineate the proposed project location within the SFHA, 
the associated FIRM number, the BFE, and the appropriate cross-section (or transect) to use 
when referencing the associated FIS, as shown in Figure 5.4.  
 
Figure 5.4: Proposed project location on NFHL  
5.1.1.2 
USGS Streamflow Data 
The USGS Streamflow Data layer in Google Earth may help the user find the stream gage closest 
to the proposed project location, and finding the closest stream gage may help determine a 
recurrence interval for storm event(s) related to the proposed mitigation projects location (see 
also Section 2.2.2). 
1. Users can obtain Google Earth Streamflow KML files at 
http://waterwatch.usgs.gov/new/?id=real&sid=w__kml. USGS stream gage data in a keyhole 
markup language (KML) format. 
Available Technology Aids 
 
5-6 
2. In Google Earth, select “real-time USGS.krmz” to view a real-time USGS stream gage map 
that is color-coded for flow conditions, as shown in Figure 5.5. 
 
Figure 5.5: USGS streamflow gages 
Available Technology Aids 
 
5-7 
3. Click on the stream gage symbol to displays the station number, station name, and stream 
flow and stage data, as shown in Figure 5.6. 
 
Figure 5.6: USGS station number display 
4. Click on the station number in the pop-up window to access the USGS Web site and up-to-
date information regarding all stream flow data collected at the site.  
For details regarding obtaining historical stream gage data and determining recurrence 
intervals from this data, refer to Section 2.1.2. 
******* 
Spatial Calculations 
The Google Earth Ruler feature shown in Figure 5.7 can be used to calculate and document 
required distances such as detour route and distance for use in the DFA module and a 0.5-mile 
radius for use in the Tornado Safe Room module. 
 
Figure 5.7: Google Earth Pro toolbar 
Available Technology Aids 
 
5-8 
Detour Route and Distance 
In the Value of Services: Roads/ Bridges portion of the BCA DFA module, the user must enter 
the facility description, estimated number of one-way traffic trips per day, additional time per 
one-way trip due to detour, number of additional miles (see screenshot) by providing maps 
showing the location of the road closure and the proposed detour route. The proposed location of 
the road closure and the detour route with distance can be drawn using the Google Earth “Show 
Ruler” feature in the top tool bar (see Figure 5.7) 
 
 
Calculate the proposed location of the road closure and the distance of the detour route by 
highlighting a detour path using Length: Miles. A screen shot of the detour and the Ruler pop-up 
box indicating the length of the detour can be attached as acceptable documentation (see 
Figure 5.8). 
Available Technology Aids 
 
5-9 
 
Figure 5.8: Proposed detour and distance 
Safe Room Radius 
In identifying t he Safe Room Structure type in the Tornado Safe Room module, the user is asked 
the size (radius, in miles) of the community that will use the safe room and the predominant 
structure type(s) that people will leave to go to the safe room. Users are asked to estimate a 
radius around the safe room location. A 0.5-mile radius or 5-minute walking distance is an 
acceptable default value per the FY11 HMA Unified Guidance (FEMA, 2010a).  
 
Available Technology Aids 
 
5-10 
 
 
Users are asked to provide a copy of a radius map using aerial photography showing the 
proposed safe room location and radius. Additionally, the user is asked to select the two 
predominant structure types the target population within the radius would reside in if they do not 
use the safe room. Both inputs can be documented and justified using a radius map showing 
structure detail. A radius map can be obtained using the Ruler feature in Google Earth.  
The Ruler feature may be used to draw a circle having a 0.50-mile radius from the proposed 
project’s location to determine the structures that would benefit from a safe room. Two 0.50-mile 
paths should be drawn using the Ruler feature, as shown in Figure 5.9, and a circle should be 
drawn to connect the end points using drawing software. 
Available Technology Aids 
 
5-11 
 
Figure 5.9: Using Google Earth Pro to determine 0.50-mile radius from the project location 
5.1.2 Using Google Earth Pro License 
With an investment in a Google Earth Pro license, additional inputs are available through Google 
Earth, including U.S. demographics, U.S. parcel data (which includes residential square footage 
and year built, and commercial annual revenue and other pertinent commercial information) and 
U.S. daily traffic counts. Since Web-based sources are acceptable documentation, information 
provided through the Google Earth Pro license is a potential resource.  
To access the U.S. layers, the user should click the expand symbol next to the Earth Pro (U.S.) 
folder in the Layers panel to the side of the 3D viewer and select the desired layer(s), as shown in 
Figure 5.10. 
 
Figure 5.10: Data layers available in Google Earth Pro (U.S.)  
Available Technology Aids 
 
5-12 
U.S. Demographics 
The U.S. Demographics layer can be used to determine the average household size and to 
calculate the population served (Safe Room module), as shown in Figure 5.11. 
 
Figure 5.11: U.S. demographics using Google Earth Pro 
U.S. Parcel Data 
Similarly, certain residential and commercial information can be obtained from the U.S. Parcel 
Data layer, which is considered acceptable documentation. For residential structures, the data 
layer can be used to determine residential square footage (for the Flood module) and year built 
(for the DFA module), as shown in Figure 5.12.  
Available Technology Aids 
 
5-13 
 
Figure 5.12: U.S. parcel data using Google Earth Pro 
The Automated Value Model (AVM) used by Google Earth Pro to value properties is not 
acceptable for calculating the BRV. AVMs such as that used by Google Earth are statistical 
computer programs that use real estate information such as comparable sales, property 
characteristics, tax assessments, and price trends to provide an estimate of value for a specific 
property. Acceptable documentation of the BRV includes tax records or tax cards; property 
appraisals from a building inspector or local contractor; estimates from an architect, engineer, or 
local building official; and documented data from a national cost-estimating guide. 
For commercial structures, the U.S. Parcel Data layer can be used to determine the annual 
operating budget and details such as the number of beds in a hospital.  
U.S. Daily Traffic Counts 
The U.S. Daily Traffic Counts layer available in Google Earth Pro provides an easy way to 
determine the average annual daily traffic count for a proposed road, as shown in Figure 5.13. 
Available Technology Aids 
 
5-14 
 
Figure 5.13: Daily traffic counts using Google Earth Pro  
The estimated number of one-way traffic trips per day is expressed as a traffic count that 
typically includes the total number of vehicles that pass a given point in both directions. Though 
unlikely, the average daily traffic (ADT) could be reported for some divided roadways for each 
direction separately. Such ADTs should be indicated as directional. Unless Google Earth 
indicates otherwise, users can assume the ADT is for vehicles that pass a given point in both 
directions.  
The ADT value can be entered into the BCA software for a proposed detour. For road or bridge 
losses that do not have detours, the number of daily trips should be based on the number of one-
way trips (equivalent to the ADT), and the delay time should be 12 hours per one-way trip. S uch 
losses should be documented using maps that clearly show that no detour is available. In 
addition, no additional miles due to delay can be counted. 
5.1.3 Using Hazus for Flood Analysis 
The FEMA Hazards U.S. (Hazus) model is a national model that uses standardized 
methodologies for estimating potential losses from earthquakes, floods, and hurricanes. Hazus 
uses GIS technology to compare hazard distributions and their probabilities with property 
locations to provide estimates of potential losses. Information about obtaining the Hazus 
software and user and technical manuals is available at the FEMA Web site (search for Hazus 
User or Technical Manuals for a particular version, e.g., MR2, MR5, at http://www.fema.gov/). 
Available Technology Aids 
 
5-15 
While originally developed primarily as a regional planning and disaster management tool, 
Hazus is increasingly being used in local planning, including screening mitigation grant 
applications. However, Hazus and FEMA BCA methodo logies have some fundamental 
differences, and only certain Hazus information is considered acceptable for a FEMA BCA.  
The following subsections provide information on the acceptable and unacceptable uses of Hazus 
methodologies for a FEMA BCA. 
The acceptable uses of Hazus information are (see Section 5.1.3.1): 
 Depth-damage functions (Flood module) 
 Contents values (Flood module) 
 Displacement values (Flood module) 
 Flood module loss calculations (DFA module) 
The unacceptable uses of Hazus information are (see Section 5.1.3.2): 
 Level 1 analysis 
 Level 2 and Level 3 analyses for census blocks 
 Level 2 and Level 3 analyses for user-defined facilities 
5.1.3.1 
Acceptable Uses of Hazus 
Depth-Damage  Functions 
During the development of BCA Version 4.5.5, DDFs included in Hazus Major Release 2 (MR2) 
were incorporated into the FEMA BCA Flood module for damage to building, contents, 
displacement, and loss of function. These DDFs are based on curves originally developed by the 
USACE and the Flood Insurance Administration (FIA). In addition, FEMA developed several 
other non-residential DDFs for BCA Version 4.5.5.  
In the Flood module, the user can select USACE DDFs, FIA DDFs, or enter custom DDFs (see 
Section 2) on either the Residential Structure Information or Non-Residential Structure 
Information screen. The DDFs available are based on the selections the user made on the 
previous screen (Structure Information) about structure type (residential vs. non-residential), 
number of stories, and basement type.  
Available Technology Aids 
 
5-16 
 
 
1. Users conducting a Flood module analysis typically use the default USACE Generic DDF 
rather than a FIA DDF.   
2. To use a DDF that is available in Hazus but not available in the Flood module, the user must 
select Custom as the Depth-Damage Type and enter the DDF as shown in the screenshot 
below.  
Available Technology Aids 
 
5-17 
 
 
3. User must enter a total of four DDFs (building, contents, displacement, and loss of function). 
For non-residential buildings, the same DDF can be used for displacement and loss of 
function for buildings whose occupants cannot temporarily relocate while flood repairs are 
completed, such as industrial companies. 
Contents Values  
During the development of BCA Version 4.5.5, Hazus MR2 contents values were used to 
develop default values for different building types, as listed in Table 10. 
 
 
Available Technology Aids 
 
5-18 
Table 10: Hazus MR2 Default Contents Value Based on Percentage of Structure Value 
No. 
Hazus Occupancy 
Class Code 
Hazus Occupancy Class 
Description 
Contents Value (%  of BRV) 
Residential 
1 
RES1 
Single Family Dwelling 
50 
2 
RES2 
Mobile Home 
50 
3 
RES3 
Multi Family Dwelling 
50 
4 
RES4 
Temporary Lodging 
50 
5 
RES5 
Institutional Dormitory 
50 
6 
RES6 
Nursing Home 
50 
Commercial 
7 
COM1 
Retail Trade 
100 
8 
COM2 
Wholesale Trade 
100 
9 
COM3 
Personal and Repair Services 
100 
10 
COM4 
Professional/Technical/Business 
Services 
100 
11 
COM5 
Banks 
100 
12 
COM6 
Hospital 
150 
13 
COM7 
Medical Office/Clinic 
150 
14 
COM8 
Entertainment & Recreation 
100 
15 
COM9 
Theaters 
100 
16 
COM10 
Parking 
50 
Industrial 
17 
IND1 
Heavy 
150 
18 
IND2 
Light 
150 
19 
IND3 
Food/Drugs/Chemicals 
150 
20 
IND4 
Metals/Minerals Processing 
150 
21 
IND5 
High Technology 
150 
22 
IND6 
Construction 
100 
Agriculture 
23 
AGR1 
Agriculture 
100 
Religion/Non-Profit 
24 
REL1 
Church/Membership Organization 
100 
Government 
25 
GOV1 
General Services 
100 
26 
GOV2 
Emergency Response 
150 
Education 
27 
EDU1 
Schools/Libraries 
100 
28 
EDU2 
Colleges/Universities 
150 
 
The exception to these defaults is when users select residential USACE Generic DDFs. The BCA 
software uses 100% of the BRV for the contents replacement value as the default when USACE 
Generic DDFs are selected because the content-to-structure value ratio is already incorporated in 
the contents DDF.   
When conducting a Flood module analysis, the user normally uses the default contents values 
provided by the BCA software. The default contents values are based on the DDF selection 
(residential or non-residential/primary use, number of stories, basement type, and default or 
generic). However, in some situations, the primary building use for non-residential buildings 
Available Technology Aids 
 
5-19 
does not match a Hazus occupancy class (e.g., schools). For example, the BCA software does not 
have college or university (Hazus Occupancy Class EDU2) as a non-residential option. 
Therefore, it is acceptable for the user to enter a user-defined contents value based on the Hazus 
defaults from Table 10.  
Displacement (Monthly and One-Time) Values  
For residential buildings (typically single family), the Flood module uses a default value of 
$1.44/sf/month as the default displacement cost on the Residential Structure Information screen. 
The default residential displacement cost includes both monthly (rental) and one-time 
(disruption) costs.  
 
 
Hazus provides additional acceptable default values for both monthly and one-time displacement 
costs for other residential and non-residential structure types, as shown in Table 11. 
To calculate monthly displacement costs for other residential building types (e.g., mobile home, 
apartments), the user should select the User-Entered ($/month) radio button (screenshot above) 
to override the default displacement value, and then follow these steps: 
1. From Table 11, select the most appropriate residential occupancy class and obtain the 
displacement cost (rental cost) from Column A. 
2. Multiply the displacement cost from Column A of Table 11 ($/sf/month) by the building area 
(sf) to calculate the displacement cost ($/month). 
3. Adjust this value from the 2008 value to the current year by using the consumer price index 
(www.bls.gov). 
4. Enter the cost into the field labeled User-Entered ($/month).  
Available Technology Aids 
 
5-20 
Table 11: Hazus MR3 Displacement Costs (2008 Values) 
 
 
For non-residential buildings, the Flood module does not have a default value for displacement 
costs ($/month). Therefore, a similar calculation can be performed using Table 11 and entered on 
the Non-Residential Structure Information screen shown below. 
Available Technology Aids 
 
5-21 
 
 
Column A in Table 11 shows the monthly rental costs, per sf, for various occupancy classes. 
These values may be used as FEMA standard values for the monthly displacement cost. To 
calculate monthly displacement costs for non-residential buildings, these steps should be 
followed: 
1. Select the most appropriate occupancy class and obtain the corresponding displacement cost 
(rental cost) from Column A of Table 11. 
2. Multiply the displacement cost from Column A of Table 11 ($/sf/month) by the total building 
area (in sf) to calculate the displacement cost ($/month). 
3. Adjust this value from 2008 value to current year by using the consumer price index 
(www.bls.gov). 
4. Enter the cost into the field labeled Displacement Cost ($/month) shown in the screenshot 
above. 
Note that in this non-residential building example, the total building size is 10,000 sf and the first 
floor area is 5,000 sf. The first floor area (5,000 sf) is entered in the Total Size of Building cell 
because the default building and contents DDFs for non-residential buildings apply only to first 
floor damages. However, the total building area (10,000 sf) is used to calculate displacement 
costs because the total floor area is occupied as shown below.  
Available Technology Aids 
 
5-22 
For this example the monthly displacement cost for a retail clothing building is calculated as 
follows: 
Monthly displacement cost = [rental cost ($/sf/month)] x [total area (sf)] 
= ($1.25/sf/month) x (10,000 sf) 
= $12,500/month 
Table 11 is also used to calculate one-time displacement costs. To calculate one-time 
displacement costs, also known as disruption costs, for both residential and non-residential 
structures, these steps should be followed: 
1. Determine the appropriate occupancy class from Table 11. 
2. Multiply the associated disruption cost ($/sf) from Column B of Table 11 by the total 
building area (in sf) to calculate the one-time displacement cost. 
3. Adjust this value from 2008 value to current year by using consumer price index 
(www.bls.gov).  
4. Enter the cost into the field labeled One-Time Displacement Costs ($). 
The default residential displacement cost ($1.44/sf/month), which applies to single-family 
dwellings, already incorporates one-time displacement costs. Do not include one-time 
displacement costs if the default residential displacement cost is selected. 
For the example shown in the screenshot above, the one-time displacement cost for the Retail-
Clothing building is calculated as: 
One-time displacement costs = [disruption cost ($/sf)] x [total area (sf)] 
= ($1.17/sf) x (10,000 sf) 
= $11,700 
Flood Module Loss Calculations in DFA Module  
While the previous three subsections have focused on the use of information obtained from 
Hazus to use in the Flood module, but some Hazus information is acceptable for the DFA 
module. The general approach is for the user to use the DDFs and other loss calculations in the 
Flood module to derive values that can be entered in the DFA module. See Section 2.1.4 for 
details on how to implement this method. 
5.1.3.2 
Unacceptable Uses of Hazus 
Hazus Level 1 Analysis  
The Hazus Level 1 Analysis is defined in the Hazus MR5 User Manual (available at 
http://www.fema.gov/library/viewRecord.do?id=4454) as the simplest type of Hazus analysis, 
which is based primarily on data provided with the software. The assumption is that the user is 
using (1) Hazus-based H&H study methods and (2) the built environment as modeled from 
Hazus-based General Building Stock (GBS) data, usually at the U.S. census block level.  
However, neither of the Hazus approaches for H&H and GBS is considered acceptable for BCA. 
Hazus-based H&H normally uses 30- or 10-meter resolution topographic data (USGS digital 
Available Technology Aids 
 
5-23 
elevation model data), which do not meet BCA requirements for topographic or H&H modeling. 
For the Flood module, topographic data must satisfy a minimum vertical resolution equivalent to 
2-foot contour data. H&H methods need to match the standards used for FEMA detailed flood 
studies, as described in the Guidelines and Specifications for Flood Hazard Mapping Partners 
(FEMA 2003), and specifically the lists of acceptable H&H models. See Numerical Models 
Meeting the Minimum Requirements of the National Flood Insurance Program (FEMA 2010b) 
available at http://www.fema.gov/plan/prevent/fhm/en_modl.shtm. 
Hazus Level 1 analysis census block-based GBS data also do not satisfy BCA requirements. This 
approach does not model actual buildings but uses statistical data to approximate the 
characteristics and damages across entire census blocks. This approach does not provide the level 
of detail needed for BCA. 
Hazus Level 2 and 3 Analyses for Census Blocks  
Hazus Levels 2 and 3 Analyses are defined by the Hazus MR5 User Manual as advanced 
analyses in Hazus. Level 2 is usually considered as updating Hazus datasets with better local 
data. The Level 3 analysis uses models that are external to Hazus, such as engineering and 
economic models.  
For GBS data, the Levels 2 and 3 Analyses are available for updating the default Hazus datasets 
such as the Hazus Comprehensive Data Management System tool. However, as mentioned in the 
previous section, census block-based GBS methods in Hazus are not acceptable for BCA because 
actual buildings characteristics are not modeled. BCA methods for mitigation projects such as 
acquisition and elevation are based on building-by-building analysis, and Hazus census block-
based approach are insufficient for this purpose. 
Hazus Level 2 and 3 Analysis for User-Defined Facilities  
Another Hazus approach is the User-Defined Facilities (UDF) analysis. In the UDF analysis, the 
user enters building point locations and structural information such as Hazus occupancy type, 
first floor height above grade, building and contents costs, number of stories, and foundation 
type. Hazus then uses GIS methods to query flood d epth grids and Hazus DDFs to estimate 
building and contents losses. The UDF analysis can be useful for screening large numbers of 
buildings to determine the buildings that may be the best candidates for a BCA.  
However, the fundamental differences between the Hazus and BCA approaches that make the 
Hazus UDF analysis unacceptable for BCA are as follows: 
 Topographic data requirements. BCA requires a minimum vertical detail equivalent to 2-
foot contours for mapping. Any flood depth grids derived in Hazus that do not use 
topographic data with that detail level are unacceptable for BCA.  
 H&H modeling methods. The second set of differences concern H&H modeling methods. 
Level 1 Hazus-based H&H are not considered acceptable for BCA according to FEMA’s 
Guidelines and Specifications for Flood Hazard Mapping Partners (FEMA 2003) 
because of the generalized attributes of the data.  
However, an experienced Hazus user could develop flood depth grids using Hazus Level 
2 or 3 analysis that overcome the differences in topographic data requirements and H&H 
modeling methods. The Hazus Level 2 analysis allows the user to import flood depth 
grids developed from external models, such as the FEMA-acceptable model Hydrologic 
Available Technology Aids 
 
5-24 
Engineering Centers River Analysis System (HEC-RAS). Hazus also has the Flood 
Information Tool program that allows development of flood depth grids from cross-
sectional data from external models. It is possible for a Hazus user to develop flood depth 
grids based on FEMA-acceptable models and with sufficient topographic details to 
satisfy the BCA requirements. 
 Loss calculation methods. The primary reason the Hazus UDF analysis is considered 
unacceptable for BCA is the methods used to calculate losses for each recurrence interval 
and annualized loss. Although the Hazus UDF table and Hazus depth grids can be 
configured to return the same percent building damage (based on the same flood depth 
and building DDF) as the BCA, the way that BCA uses these values to derive annualized 
loss is different than Hazus. Hazus does not have the capability to calculate annualized 
loss when user-defined depth grids are used. Hazus MR4 does include an annualized loss 
calculation, but it applies only to scenarios that use Hazus-based H&H methods.  
In addition, the Hazus annualized calculation uses extrapolated 2- and 5-year recurrence 
interval events. Even these limited annualized loss calculations were removed for Hazus 
MR5. In contrast, the BCA uses the stream bed elevation to interpolate losses for events 
below the 10-year event. See the Flood Full Data Module Methodology Report for more 
information (available as part of the BCA Resource Kit at 
http://www.bchelpline.com/BCAToolkit/resources_flood.html).  
 Demolition threshold. When building DDFs exceed the demolition threshold in the BCA 
software, the building damage is set to 100% and all other DDFs are set to their 
maximum for that structure type. Hazus does not use any demolition threshold as part of 
census block GBS or the UDF analysis.  
There is currently no way to use a Hazus UDF analysis to replace a BCA Flood module analysis. 
Although the Hazus UDF analysis could be made to match certain parts of the BCA calculations, 
there are fundamental calculation differences between the Flood module and Hazus software. If a 
user has data such as detailed structure characteristics, a HEC-RAS analysis, and detailed flood 
depth and water surface elevations grids, these data could be used for the Flood module rather 
than trying to make Hazus approximate what the BCA software does by default. 
 
 
 
 
References 
 
6-1 
SECTION SIX 
REFERENCES 
BCA Helpline. http://www.bchelpline.com/. Accessed June 2011. 
Benefit-Cost Analysis Resource Kit. Hazard: Flood. 
http://www.bchelpline.com/BCAToolkit/resources_flood.html. Accessed June 2011. 
Bureau of Labor Statistics. http://bls.gov/. Accessed _June 2011. 
Campo li, J., et al., 2001. Above and Beyond, Visualizing Changes in Small Town and Rural 
Areas.  American Planning Association Press, Chicago, IL. 
Council of Chief State School Officers, School Matters, 2011. School enrollment data. 
http://www.schooldatadirect.org/.  Accessed June 2011. 
Federal Bureau of Investigation. 2006 Uniform Crime Reporting [UCR] Program. 
http://www2.fbi.gov/ucr/cius2006/data/table_05.htmlAccessed October 2008. 
FEMA (Federal Emergency Management Agency). 2003 with updates through 2009. Guidelines 
and Specifications for Flood Hazard Mapping Partners. 
http://www.fema.gov/library/viewRecord.do?id=2206. Accessed June 2011. 
FEMA. 2006  Hazus-MH MR3 Flood Technical Manual 
FEMA. 2008. Design and Construction Guidance for Community Safe Rooms (FEMA 361). 
Second Edition, August 2008. 
FEMA. 2010a. Hazard Mitigation Assistance Unified Guidance: Hazard Mitigation Grant 
Program, Pre-Disaster Mitigation Program, Flood Mitigation Assistance Program, 
Repetitive Flood Claims Program, Severe Repetitive Loss Program, June 1, 2010 
FEMA. 2010b. Numerical Models Meeting the Minimum Requirements of the National Flood 
Insurance Program. http://www.fema.gov/plan/prevent/fhm/en_modl.shtm.  Accessed 
November 2011. 
FEMA. 2010c  Hazus-MH MR5 Flood Technical Manual 
FEMA. 2011a. Hazus FEMA’s Methodology for Estimating Potential Losses from Disasters. 
http://www.fema.gov/plan/prevent/hazus/index.shtm. Accessed June 2011. 
FEMA. 2011b. Using the National Flood Hazard Layer Web Service (WMS) in Google Earth. 
https://hazards.fema.gov/femaportal/wps/portal/NFHLWMSkmzdownload. Accessed 
June 2011. 
Lake County Illinois GIS and Mapping Division. 2010. Topographic Map Gallery. 
http://oldapps.lakecountyil.gov/gis/TopoGallery/MapsInqMapName.asp. Accessed April 
2011.  
Missouri Census Data Center. Circular Area Profiles. 
http://mcdc.missouri.edu/websas/caps.html.  Accessed _June 2011. 
Montgomery County Maryland. Montgomery County Council. 
http://www.montgomerycountymd.gov/csltmpl.asp?url=/content/council/mem/district_m
ap.asp. Accessed June 2011. 
References 
 
6-2 
Montgomery County Maryland. Montgomery County Council. Council District 1. 
http://www.montgomerycountymd.gov//content/gis/images/gallery/councildistrict1.pdf. 
Montgomery County Maryland. Montgomery County Council. Council District Population 
Change: 2000 to 2010 (adjusted) Montgomery County, Maryland. 
http://www.montgomerycountymd.gov/content/council/redistricting/Handouts/adjmoco_a
djpop.pdf. 
NOAA. Photograph used courtesy of Nick Witcraft. www.srh.noaa.gov. Accessed June 2011. 
North Carolina Chapter of the American Society for Photogrammetry and Remote Sensing 
(NCASPRS). 2010. Flood Analysis and Flood Mapping: Where is the First Floor? 
www.ncasprs.com/pub/FirstFloorElevations_ncss.pptx. Accessed April 2010.  
Spinell Homes. 2010. Models. http://www.spinellhomes.com/models. Accessed April 2010.  
U.S. Census Bureau. 2009. Community Population Estimates. 
http://factfinder.census.gov/servlet/ACSSAFFFacts?_event=&geo_id=16000US2462850
&_geoContext=01000US%7C04000US24%7C16000US2462850&_street=&_county=po
olesville&_cityTown=poolesville&_state=04000 US24&_zip=&_lang=en&_sse=on&Act
iveGeoDiv=&_useEV=&pctxt=fph&pgsl=160&_submenuId=factsheet_1&ds_name=null
&_ci_nbr=null&qr_name=null&reg=null%3Anull&_keyword=&_industry=). Accessed   
June 2011. 
U.S. Geological Survey. 2011. Google Earth Streamflow KML Files. 
http://waterwatch.usgs.gov/new/?id=real&sid=w__kml. Accessed June 2011. 
United States Department of Agriculture (USDA). Data Sets. 2010. 
http://www.ers.usda.gov/Data/. Accessed June 2011. 
 
 
