from datetime import datetime
from typing import List, Dict, Any, Optional
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Query, Path
from fastapi.responses import StreamingResponse, JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import get_db
from app.core.security import get_current_active_user
from app.models.user import User
from app.models.bca import BCRStatus
from app.services.bca_service import BCAService
from app.schemas.bca import (
    BCAAnalysisCreate,
    BCAAnalysisResponse,
    BCAAnalysisListResponse,
    BCABenefitCreate,
    BCABenefitResponse,
    BCACostCreate,
    BCACostResponse,
    BCAValidationResponse,
    BCAMethodologyResponse
)
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/bca", tags=["Benefit Cost Analysis"])

@router.post("/analyses", response_model=BCAAnalysisResponse)
async def create_analysis(
    data: BCAAnalysisCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Create new BCA analysis"""
    try:
        service = BCAService(db)
        analysis = await service.create_analysis(
            project_id=data.project_id,
            dr_number=data.dr_number,
            created_by_id=current_user.id,
            project_useful_life=data.project_useful_life,
            discount_rate=data.discount_rate,
            price_level=data.price_level
        )
        return analysis
    except Exception as e:
        logger.error(f"Error creating BCA analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/analyses/{analysis_id}", response_model=BCAAnalysisResponse)
async def get_analysis(
    analysis_id: UUID = Path(..., description="The ID of the BCA analysis"),
    db: AsyncSession = Depends(get_db),
    _: User = Depends(get_current_active_user)
):
    """Get BCA analysis by ID"""
    try:
        service = BCAService(db)
        return await service.get_analysis(analysis_id)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving BCA analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/projects/{project_id}/analyses", response_model=BCAAnalysisListResponse)
async def list_analyses_by_project(
    project_id: int = Path(..., description="The ID of the project"),
    limit: int = Query(100, description="Maximum number of items to return"),
    offset: int = Query(0, description="Number of items to skip"),
    db: AsyncSession = Depends(get_db),
    _: User = Depends(get_current_active_user)
):
    """Get all BCA analyses for a project"""
    try:
        service = BCAService(db)
        analyses, total = await service.get_analyses_by_project(
            project_id=project_id,
            limit=limit,
            offset=offset
        )
        return {
            "items": analyses,
            "total": total,
            "limit": limit,
            "offset": offset
        }
    except Exception as e:
        logger.error(f"Error listing BCA analyses: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/analyses/{analysis_id}/benefits", response_model=BCABenefitResponse)
async def add_benefit(
    analysis_id: UUID = Path(..., description="The ID of the BCA analysis"),
    data: BCABenefitCreate = ...,
    db: AsyncSession = Depends(get_db),
    _: User = Depends(get_current_active_user)
):
    """Add benefit to BCA analysis"""
    try:
        service = BCAService(db)
        return await service.add_benefit(
            analysis_id=analysis_id,
            category=data.category,
            annual_value=data.annual_value,
            documentation=data.documentation,
            assumptions=data.assumptions
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding benefit: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/analyses/{analysis_id}/costs", response_model=BCACostResponse)
async def add_cost(
    analysis_id: UUID = Path(..., description="The ID of the BCA analysis"),
    data: BCACostCreate = ...,
    db: AsyncSession = Depends(get_db),
    _: User = Depends(get_current_active_user)
):
    """Add cost to BCA analysis"""
    try:
        service = BCAService(db)
        return await service.add_cost(
            analysis_id=analysis_id,
            category=data.category,
            amount=data.amount,
            recurring=data.recurring,
            frequency=data.frequency,
            documentation=data.documentation
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding cost: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/analyses/{analysis_id}/submit", response_model=Dict[str, Any])
async def submit_for_review(
    analysis_id: UUID = Path(..., description="The ID of the BCA analysis"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Submit BCA analysis for review"""
    try:
        service = BCAService(db)
        analysis = await service.submit_for_review(analysis_id)
        return {
            "message": "Analysis submitted for review",
            "status": analysis.status,
            "analysis_id": str(analysis.id)
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error submitting analysis for review: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/analyses/{analysis_id}/approve", response_model=Dict[str, Any])
async def approve_analysis(
    analysis_id: UUID = Path(..., description="The ID of the BCA analysis"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Approve BCA analysis"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=403,
            detail="Only superusers can approve analyses"
        )
    try:
        service = BCAService(db)
        analysis = await service.approve_analysis(
            analysis_id=analysis_id,
            approved_by_id=current_user.id
        )
        return {
            "message": "Analysis approved",
            "status": analysis.status,
            "analysis_id": str(analysis.id)
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error approving analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/analyses/{analysis_id}/reject", response_model=Dict[str, Any])
async def reject_analysis(
    analysis_id: UUID = Path(..., description="The ID of the BCA analysis"),
    reason: str = Query(..., description="Reason for rejection"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Reject BCA analysis"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=403,
            detail="Only superusers can reject analyses"
        )
    try:
        service = BCAService(db)
        analysis = await service.reject_analysis(
            analysis_id=analysis_id,
            reason=reason
        )
        return {
            "message": "Analysis rejected",
            "status": analysis.status,
            "reason": reason,
            "analysis_id": str(analysis.id)
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error rejecting analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/analyses/{analysis_id}/validate", response_model=BCAValidationResponse)
async def validate_analysis(
    analysis_id: UUID = Path(..., description="The ID of the BCA analysis"),
    db: AsyncSession = Depends(get_db),
    _: User = Depends(get_current_active_user)
):
    """Validate BCA analysis against FEMA requirements"""
    try:
        service = BCAService(db)
        validation_result = await service.validate_analysis(analysis_id)
        return validation_result
    except Exception as e:
        logger.error(f"Error validating analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/analyses/{analysis_id}/export", response_class=StreamingResponse)
async def export_to_bca_toolkit(
    analysis_id: UUID = Path(..., description="The ID of the BCA analysis"),
    db: AsyncSession = Depends(get_db),
    _: User = Depends(get_current_active_user)
):
    """Export BCA analysis to FEMA BCA Toolkit format"""
    try:
        service = BCAService(db)
        content = await service.export_to_bca_toolkit(analysis_id)
        
        # Get analysis details for filename
        analysis = await service.get_analysis(analysis_id)
        
        # Generate appropriate filename
        filename = f"BCA_{analysis.dr_number}_{analysis.project_id}_{datetime.now().strftime('%Y%m%d')}.csv"
        
        # Return as downloadable CSV
        return StreamingResponse(
            iter([content]),
            media_type="text/csv",
            headers={
                "Content-Disposition": f"attachment; filename={filename}"
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting to BCA toolkit: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/projects/{project_id}/import", response_model=BCAAnalysisResponse)
async def import_from_bca_toolkit(
    project_id: int = Path(..., description="The ID of the project"),
    file: UploadFile = File(..., description="FEMA BCA Toolkit file (CSV format)"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Import BCA analysis from FEMA BCA Toolkit format"""
    try:
        # Validate file type
        if not file.filename.endswith(".csv"):
            raise HTTPException(
                status_code=400,
                detail="Only CSV files are supported for BCA Toolkit import"
            )
            
        service = BCAService(db)
        analysis = await service.import_from_bca_toolkit(
            project_id=project_id,
            created_by_id=current_user.id,
            file=file
        )
        
        return analysis
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error importing from BCA toolkit: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/statuses", response_model=List[Dict[str, str]])
async def get_bcr_statuses():
    """Get all possible BCR statuses"""
    return [{"value": status.value, "label": status.name} for status in BCRStatus]
