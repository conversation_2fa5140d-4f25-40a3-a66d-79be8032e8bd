
'use client';

import { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  CheckCircle, 
  XCircle, 
  RefreshCw, 
  Wifi, 
  WifiOff, 
  Clock 
} from 'lucide-react';

interface ConnectionStatusProps {
  status: 'connecting' | 'connected' | 'reconnecting' | 'offline';
  reconnectAttempts?: number;
  maxReconnectAttempts?: number;
  lastConnected?: Date;
  onConnect?: () => void;
  onDisconnect?: () => void;
  showControls?: boolean;
}

const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  status,
  reconnectAttempts = 0,
  maxReconnectAttempts = 5,
  lastConnected,
  onConnect,
  onDisconnect,
  showControls = true
}) => {
  const [isClient, setIsClient] = useState(false);
  
  useEffect(() => {
    setIsClient(true);
  }, []);
  const getStatusConfig = () => {
    switch (status) {
      case 'connected':
        return {
          icon: <CheckCircle className="h-4 w-4" />,
          text: 'Connected',
          color: 'text-green-500',
          badgeVariant: 'default' as const,
          badgeColor: 'bg-green-500 text-white'
        };
      case 'connecting':
        return {
          icon: <RefreshCw className="h-4 w-4 animate-spin" />,
          text: 'Connecting...',
          color: 'text-yellow-500',
          badgeVariant: 'secondary' as const,
          badgeColor: 'bg-yellow-500 text-white'
        };
      case 'reconnecting':
        return {
          icon: <RefreshCw className="h-4 w-4 animate-spin" />,
          text: `Reconnecting... (${reconnectAttempts}/${maxReconnectAttempts})`,
          color: 'text-yellow-500',
          badgeVariant: 'secondary' as const,
          badgeColor: 'bg-yellow-500 text-white'
        };
      case 'offline':
      default:
        return {
          icon: <XCircle className="h-4 w-4" />,
          text: 'Offline',
          color: 'text-red-500',
          badgeVariant: 'destructive' as const,
          badgeColor: 'bg-red-500 text-white'
        };
    }
  };

  const statusConfig = getStatusConfig();

  const formatTimestamp = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  return (
    <div className="flex items-center justify-between w-full">
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-2">
          <div className={statusConfig.color}>
            {statusConfig.icon}
          </div>
          <Badge className={statusConfig.badgeColor}>
            {statusConfig.text}
          </Badge>
        </div>
        
        {lastConnected && status === 'connected' && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Clock className="h-3 w-3" />
            <span>Connected at {isClient ? formatTimestamp(lastConnected) : '--:--:--'}</span>
          </div>
        )}
        
        {lastConnected && status !== 'connected' && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Clock className="h-3 w-3" />
            <span>Last connected: {isClient ? formatTimestamp(lastConnected) : '--:--:--'}</span>
          </div>
        )}
      </div>

      {showControls && (
        <div className="flex items-center gap-2">
          <Button
            onClick={status === 'connected' ? onDisconnect : onConnect}
            variant={status === 'connected' ? "destructive" : "default"}
            size="sm"
            disabled={status === 'connecting' || status === 'reconnecting'}
          >
            {status === 'connected' ? (
              <>
                <WifiOff className="h-4 w-4 mr-2" />
                Disconnect
              </>
            ) : (
              <>
                <Wifi className="h-4 w-4 mr-2" />
                Connect
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  );
};

export default ConnectionStatus;
