/**
 * ComplianceMax Unified Assist System
 * 
 * Master controller that provides page-aware AutoPilot and Max Assist functionality
 * Each page gets its own data sources, automation logic, and guidance content
 */

window.ComplianceMaxAssist = {
    // Auto-detect current page
    currentPage: null,
    
    // Initialize the system
    init() {
        this.detectCurrentPage();
        this.loadPageData();
        this.initializeUI();
        console.log(`🤖 ComplianceMax Assist initialized for page: ${this.currentPage}`);
    },

    // Detect which page we're on
    detectCurrentPage() {
        const body = document.body;
        const url = window.location.pathname;
        
        if (body.dataset.page) {
            this.currentPage = body.dataset.page;
        } else if (url.includes('worksheet')) {
            this.currentPage = 'worksheet';
        } else if (url.includes('emergency')) {
            this.currentPage = 'emergency';
        } else if (url.includes('cbcs')) {
            this.currentPage = 'cbcs';
        } else if (url.includes('upload') || url.includes('document')) {
            this.currentPage = 'upload';
        } else if (url.includes('dashboard')) {
            this.currentPage = 'dashboard';
        } else if (url.includes('report')) {
            this.currentPage = 'report';
        } else {
            this.currentPage = 'unknown';
        }
    },

    // Load page-specific data and configurations
    async loadPageData() {
        const pageConfig = this.pageConfigs[this.currentPage];
        if (pageConfig && pageConfig.dataLoader) {
            await pageConfig.dataLoader();
        }
    },

    // Initialize UI components
    initializeUI() {
        // Buttons are already in the page HTML, just add panel creation
        this.createPanelStyles();
    },

    // Create CSS styles for panels
    createPanelStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .assist-panel {
                position: fixed;
                top: 80px;
                right: 20px;
                width: 350px;
                background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
                border: 1px solid rgba(59, 130, 246, 0.3);
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                color: white;
                z-index: 10000;
                display: none;
                font-family: 'Lato', sans-serif;
            }

            .assist-panel-header {
                background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
                padding: 16px 20px;
                border-radius: 12px 12px 0 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .assist-panel-title {
                font-size: 18px;
                font-weight: 600;
            }

            .assist-panel-close {
                background: none;
                border: none;
                color: white;
                font-size: 20px;
                cursor: pointer;
                padding: 0;
                width: 24px;
                height: 24px;
            }

            .assist-panel-content {
                padding: 20px;
            }

            .confidence {
                font-weight: bold;
                color: #10b981;
                margin-bottom: 10px;
            }

            .description {
                margin-bottom: 15px;
                line-height: 1.5;
            }

            .actions-list {
                list-style: none;
                padding: 0;
                margin: 15px 0;
            }

            .actions-list li {
                padding: 8px 0;
                border-bottom: 1px solid rgba(255,255,255,0.1);
            }

            .actions-list li:before {
                content: '✓';
                color: #10b981;
                margin-right: 8px;
            }

            .apply-btn {
                background: linear-gradient(135deg, #059669 0%, #10b981 100%);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                cursor: pointer;
                font-weight: 600;
                width: 100%;
                margin-top: 15px;
            }

            .apply-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
            }

            .quick-actions {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
                margin-top: 15px;
            }

            .quick-action-btn {
                background: rgba(59, 130, 246, 0.2);
                color: white;
                border: 1px solid rgba(59, 130, 246, 0.3);
                padding: 8px 12px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 12px;
            }

            .quick-action-btn:hover {
                background: rgba(59, 130, 246, 0.3);
            }

            .welcome-message {
                margin-bottom: 15px;
                line-height: 1.5;
            }
        `;
        document.head.appendChild(style);
    },

    // Create AutoPilot panel
    createAutoPilotPanel() {
        const panel = document.createElement('div');
        panel.id = 'autopilot-panel';
        panel.className = 'assist-panel';
        panel.innerHTML = `
            <div class="assist-panel-header">
                <div class="assist-panel-title">AutoPilot</div>
                <button class="assist-panel-close" onclick="document.getElementById('autopilot-panel').style.display='none'">×</button>
            </div>
            <div class="assist-panel-content">
                <div class="confidence">Confidence: 85%</div>
                <div class="description">Ready to apply worksheet optimizations.</div>
                <ul class="actions-list">
                    <li>Load cost presets</li>
                    <li>Apply regional adjustments</li>
                    <li>Set default sources</li>
                </ul>
                <button class="apply-btn" onclick="ComplianceMaxAssist.applyWorksheetAutomation()">Apply AutoPilot</button>
            </div>
        `;
        document.body.appendChild(panel);
        return panel;
    },

    // Create Max Assist panel
    createMaxAssistPanel() {
        const panel = document.createElement('div');
        panel.id = 'maxassist-panel';
        panel.className = 'assist-panel';
        panel.innerHTML = `
            <div class="assist-panel-header">
                <div class="assist-panel-title">Max Assist</div>
                <button class="assist-panel-close" onclick="document.getElementById('maxassist-panel').style.display='none'">×</button>
            </div>
            <div class="assist-panel-content">
                <div class="welcome-message">I can help you fill out this worksheet with FEMA-compliant cost estimates.</div>
                <div class="quick-actions">
                    <button class="quick-action-btn">Add line item</button>
                    <button class="quick-action-btn">Set cost source</button>
                    <button class="quick-action-btn">Check totals</button>
                    <button class="quick-action-btn">Export worksheet</button>
                </div>
            </div>
        `;
        document.body.appendChild(panel);
        return panel;
    },

    // Page-specific configurations
    pageConfigs: {
        worksheet: {
            displayName: 'Worksheet',
            dataLoader: async function() {
                // Load worksheet-specific data
                await ComplianceMaxAssist.dataSources.worksheet.loadPresets();
                await ComplianceMaxAssist.dataSources.worksheet.loadCostSources();
                await ComplianceMaxAssist.dataSources.worksheet.loadRegionalData();
            },
            autoPilot: {
                confidence: 0.85,
                description: 'Auto-populate cost buckets with category-specific line items',
                actions: ['Load cost presets', 'Apply regional adjustments', 'Set default sources']
            },
            maxAssist: {
                welcomeMessage: 'I can help you fill out this worksheet with FEMA-compliant cost estimates.',
                quickActions: ['Add line item', 'Set cost source', 'Check totals', 'Export worksheet']
            }
        },

        emergency: {
            displayName: 'Emergency Intake',
            dataLoader: async function() {
                await ComplianceMaxAssist.dataSources.emergency.loadChecklists();
                await ComplianceMaxAssist.dataSources.emergency.loadCategories();
                await ComplianceMaxAssist.dataSources.emergency.loadRequiredDocs();
            },
            autoPilot: {
                confidence: 0.75,
                description: 'Auto-select category and generate required documentation checklist',
                actions: ['Detect category', 'Generate checklist', 'Set required docs']
            },
            maxAssist: {
                welcomeMessage: 'I can guide you through the emergency intake process step-by-step.',
                quickActions: ['Select category', 'Upload photos', 'Add narrative', 'Generate checklist']
            }
        },

        cbcs: {
            displayName: 'CBCS Selection',
            dataLoader: async function() {
                await ComplianceMaxAssist.dataSources.cbcs.loadBaselines();
                await ComplianceMaxAssist.dataSources.cbcs.loadStandards();
                await ComplianceMaxAssist.dataSources.cbcs.loadJustifications();
            },
            autoPilot: {
                confidence: 0.90,
                description: 'Auto-select baseline codes and generate technical justification',
                actions: ['Select baseline codes', 'Generate justification', 'Validate compliance']
            },
            maxAssist: {
                welcomeMessage: 'I can help you select the right CBCS codes and create justifications.',
                quickActions: ['Find codes', 'Generate justification', 'Check compliance', 'Export to worksheet']
            }
        },

        upload: {
            displayName: 'Document Upload',
            dataLoader: async function() {
                await ComplianceMaxAssist.dataSources.upload.loadPodRouting();
                await ComplianceMaxAssist.dataSources.upload.loadTagSuggestions();
                await ComplianceMaxAssist.dataSources.upload.loadValidationRules();
            },
            autoPilot: {
                confidence: 0.70,
                description: 'Auto-tag documents and route to appropriate processing pods',
                actions: ['Analyze documents', 'Suggest tags', 'Route to pods']
            },
            maxAssist: {
                welcomeMessage: 'I can help you organize and process your compliance documents.',
                quickActions: ['Upload files', 'Tag documents', 'Check requirements', 'Process batch']
            }
        },

        dashboard: {
            displayName: 'Dashboard',
            dataLoader: async function() {
                await ComplianceMaxAssist.dataSources.dashboard.loadProjectStatus();
                await ComplianceMaxAssist.dataSources.dashboard.loadWorkflows();
            },
            autoPilot: {
                confidence: 0.60,
                description: 'Smart workflow recommendations based on project status',
                actions: ['Analyze project state', 'Recommend next steps', 'Auto-navigate']
            },
            maxAssist: {
                welcomeMessage: 'I can help you navigate ComplianceMax and choose the right workflow.',
                quickActions: ['Start new project', 'Continue workflow', 'Generate report', 'Get help']
            }
        },

        report: {
            displayName: 'Report Generation',
            dataLoader: async function() {
                await ComplianceMaxAssist.dataSources.report.loadTemplates();
                await ComplianceMaxAssist.dataSources.report.loadValidation();
            },
            autoPilot: {
                confidence: 0.95,
                description: 'Auto-generate compliance report with all required sections',
                actions: ['Compile data', 'Generate sections', 'Validate compliance']
            },
            maxAssist: {
                welcomeMessage: 'I can help you generate a complete FEMA compliance report.',
                quickActions: ['Generate report', 'Check compliance', 'Export PDF', 'Submit to FEMA']
            }
        }
    },

    // Data source placeholders - organized by page
    dataSources: {
        worksheet: {
            async loadPresets() {
                // TODO: Load from assist/data/worksheet/presets.json
                console.log('📊 Loading worksheet presets...');
            },
            async loadCostSources() {
                // TODO: Load from assist/data/worksheet/cost-sources.json
                console.log('💰 Loading cost sources...');
            },
            async loadRegionalData() {
                // TODO: Load from assist/data/worksheet/regional-adjustments.json
                console.log('🗺️ Loading regional data...');
            }
        },

        emergency: {
            async loadChecklists() {
                // TODO: Load from assist/data/emergency/checklists.json
                console.log('📋 Loading emergency checklists...');
            },
            async loadCategories() {
                // TODO: Load from assist/data/emergency/categories.json
                console.log('🏷️ Loading emergency categories...');
            },
            async loadRequiredDocs() {
                // TODO: Load from assist/data/emergency/required-docs.json
                console.log('📄 Loading required documents...');
            }
        },

        cbcs: {
            async loadBaselines() {
                // TODO: Load from assist/data/cbcs/baselines.json
                console.log('📐 Loading CBCS baselines...');
            },
            async loadStandards() {
                // TODO: Load from assist/data/cbcs/standards.json
                console.log('📏 Loading standards...');
            },
            async loadJustifications() {
                // TODO: Load from assist/data/cbcs/justification-templates.json
                console.log('📝 Loading justification templates...');
            }
        },

        upload: {
            async loadPodRouting() {
                // TODO: Load from assist/data/upload/pod-routing.json
                console.log('🎯 Loading pod routing rules...');
            },
            async loadTagSuggestions() {
                // TODO: Load from assist/data/upload/tag-suggestions.json
                console.log('🏷️ Loading tag suggestions...');
            },
            async loadValidationRules() {
                // TODO: Load from assist/data/upload/validation-rules.json
                console.log('✅ Loading validation rules...');
            }
        },

        dashboard: {
            async loadProjectStatus() {
                // TODO: Load from assist/data/dashboard/project-status.json
                console.log('📊 Loading project status...');
            },
            async loadWorkflows() {
                // TODO: Load from assist/data/dashboard/workflows.json
                console.log('🔄 Loading workflows...');
            }
        },

        report: {
            async loadTemplates() {
                // TODO: Load from assist/data/report/templates.json
                console.log('📄 Loading report templates...');
            },
            async loadValidation() {
                // TODO: Load from assist/data/report/validation-rules.json
                console.log('✅ Loading validation rules...');
            }
        }
    }
};

    // AutoPilot functionality by page
    AutoPilot: {
        worksheet: function() {
            console.log('🤖 AutoPilot: Worksheet automation starting...');

            // Show AutoPilot panel
            ComplianceMaxAssist.showAutoPilotPanel();

            // Load and apply worksheet presets
            ComplianceMaxAssist.applyWorksheetAutomation();
        },

        emergency: function() {
            console.log('🤖 AutoPilot: Emergency intake automation starting...');
            alert('AutoPilot: Emergency intake automation\n\n• Auto-detect category\n• Generate required docs checklist\n• Set compliance requirements');
        },

        cbcs: function() {
            console.log('🤖 AutoPilot: CBCS automation starting...');
            alert('AutoPilot: CBCS code selection\n\n• Auto-select baseline codes\n• Generate technical justification\n• Validate compliance');
        }
    },

    // Max Assist functionality by page
    MaxAssist: {
        worksheet: function() {
            console.log('👤 Max Assist: Worksheet guidance starting...');

            // Show Max Assist panel
            ComplianceMaxAssist.showMaxAssistPanel();
        },

        emergency: function() {
            console.log('👤 Max Assist: Emergency guidance starting...');
            alert('Max Assist: Emergency intake guidance\n\n• Step-by-step process\n• Required documentation\n• Compliance tips');
        },

        cbcs: function() {
            console.log('👤 Max Assist: CBCS guidance starting...');
            alert('Max Assist: CBCS selection help\n\n• Code selection guidance\n• Justification templates\n• Compliance requirements');
        }
    },

    // Show AutoPilot panel
    showAutoPilotPanel() {
        const config = this.pageConfigs[this.currentPage];
        if (!config) return;

        // Create or show AutoPilot panel
        let panel = document.getElementById('autopilot-panel');
        if (!panel) {
            panel = this.createAutoPilotPanel();
        }

        // Update panel content
        panel.querySelector('.confidence').textContent = `Confidence: ${Math.round(config.autoPilot.confidence * 100)}%`;
        panel.querySelector('.description').textContent = config.autoPilot.description;

        const actionsList = panel.querySelector('.actions-list');
        actionsList.innerHTML = '';
        config.autoPilot.actions.forEach(action => {
            const li = document.createElement('li');
            li.textContent = action;
            actionsList.appendChild(li);
        });

        panel.style.display = 'block';
    },

    // Show Max Assist panel
    showMaxAssistPanel() {
        const config = this.pageConfigs[this.currentPage];
        if (!config) return;

        // Create or show Max Assist panel
        let panel = document.getElementById('maxassist-panel');
        if (!panel) {
            panel = this.createMaxAssistPanel();
        }

        // Update panel content
        panel.querySelector('.welcome-message').textContent = config.maxAssist.welcomeMessage;

        const quickActions = panel.querySelector('.quick-actions');
        quickActions.innerHTML = '';
        config.maxAssist.quickActions.forEach(action => {
            const button = document.createElement('button');
            button.textContent = action;
            button.className = 'quick-action-btn';
            quickActions.appendChild(button);
        });

        panel.style.display = 'block';
    },

    // Apply worksheet-specific automation
    applyWorksheetAutomation() {
        console.log('📊 Applying worksheet automation...');

        // Simulate adding cost line items
        setTimeout(() => {
            alert('AutoPilot Applied!\n\n✅ Added 3 cost line items\n✅ Set regional adjustments\n✅ Applied default cost sources\n\nCheck your worksheet for the new entries.');
        }, 1000);
    }
};

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    ComplianceMaxAssist.init();
});
