name: Security and UI Parity Guards

on: 
  pull_request:
    branches: [ main, develop ]
  push:
    branches: [ main, develop ]

jobs:
  security-guards:
    runs-on: ubuntu-latest
    name: Security Guards
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Make scripts executable
        run: chmod +x scripts/*.sh
        
      - name: Run security checks
        run: |
          bash scripts/security-check.sh | tee security-report.txt
          
      - name: Upload security report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: security-report
          path: security-report.txt
          
  ui-parity-guards:
    runs-on: ubuntu-latest
    name: UI Parity Guards
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Make scripts executable
        run: chmod +x scripts/*.sh
        
      - name: Run UI parity checks
        run: |
          bash scripts/ui-parity-check.sh | tee ui-parity-report.txt
          
      - name: Upload UI parity report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: ui-parity-report
          path: ui-parity-report.txt
