/* Max Assist Panel - Dark Theme */
#maxAssist {
    position: fixed;
    top: 80px;
    right: 18px;
    width: 360px;
    max-height: 70vh;
    background: #fafafa;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,.1), 0 1px 3px rgba(0,0,0,.1);
    display: none;
    flex-direction: column;
    overflow: hidden;
    z-index: 9999;
}

/* Max Assist Header */
#maxAssist .assist-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    padding: 12px 16px;
    background: linear-gradient(135deg, #2d1b69 0%, #1a365d 50%, #2d1b69 100%);
    color: #ffffff;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

#maxAssist .assist-header .title {
    font-weight: 700;
    font-size: 16px;
    color: #ffffff;
}

#maxAssist .assist-header .subtitle {
    font-size: 11px;
    opacity: 0.8;
    color: #e2e8f0;
}

#maxAssist .assist-header .close-btn {
    background: none;
    border: none;
    color: #e2e8f0;
    font-size: 18px;
    cursor: pointer;
    padding: 4px;
    opacity: 0.8;
}

#maxAssist .assist-header .close-btn:hover {
    opacity: 1;
}

/* Max Assist Content */
#maxAssist .assist-content {
    padding: 16px 20px;
    overflow: auto;
    max-height: 400px;
    background: #ffffff;
    color: #1f2937;
}

#maxAssist .assist-message {
    margin: 0 0 16px 0;
    line-height: 1.5;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    color: #1f2937;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

#maxAssist .assist-message strong {
    color: #1f2937;
    font-weight: 700;
}

#maxAssist .assist-message .intro-text {
    color: #6b7280;
}

/* Max Assist Footer */
#maxAssist .assist-footer {
    padding: 16px 20px;
    border-top: 1px solid #e5e7eb;
    display: flex;
    gap: 12px;
    background: #f9fafb;
}

#maxAssist .assist-footer input {
    flex: 1;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 12px 14px;
    font-size: 14px;
    color: #1f2937;
    background: #ffffff;
}

#maxAssist .assist-footer input:focus {
    outline: none;
    border-color: #2d1b69;
    box-shadow: 0 0 0 3px rgba(45,27,105,0.1);
}

#maxAssist .assist-footer button {
    background: linear-gradient(135deg, #2d1b69 0%, #1a365d 50%, #2d1b69 100%);
    color: #ffffff;
    border: none;
    border-radius: 6px;
    padding: 12px 20px;
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
    box-shadow: 0 4px 15px rgba(45,27,105,0.3);
}

#maxAssist .assist-footer button:hover {
    box-shadow: 0 6px 20px rgba(45,27,105,0.4);
    transform: translateY(-1px);
}

/* AutoPilot Panel */
#autoPilot {
    position: fixed;
    top: 80px;
    right: 200px;
    width: 360px;
    max-height: 65vh;
    overflow: auto;
    background: #0f172a;
    color: #fff;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,.3);
    border: 1px solid #1e293b;
    display: none;
    z-index: 9999;
}

#autoPilot .autopilot-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid #1f2937;
    background: #1e293b;
    border-radius: 12px 12px 0 0;
}

#autoPilot .autopilot-header .close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
}

#autoPilot .autopilot-content {
    padding: 16px;
}

#autoPilot .status-box {
    background: #1e40af;
    border: 1px solid #3b82f6;
    color: #93c5fd;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
}

#autoPilot .info-text {
    margin-bottom: 16px;
    color: #94a3b8;
}

/* Mobile responsive */
@media (max-width: 480px) {
    #maxAssist {
        width: calc(100vw - 36px);
        right: 18px;
        top: 80px;
    }

    #autoPilot {
        width: calc(100vw - 36px);
        right: 18px;
        top: 80px;
    }
}

/* Print hide */
@media print {
    #maxAssist, #autoPilot {
        display: none !important;
    }
}
