
'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { 
  FileText, 
  CheckCircle, 
  ArrowRight, 
  ArrowLeft,
  Upload,
  Settings,
  BarChart3
} from 'lucide-react'
import { ProjectDetailsStep } from './steps/ProjectDetailsStep'
import { DocumentUploadStep } from './steps/DocumentUploadStep'
import { PolicySelectionStep } from './steps/PolicySelectionStep'
import { ReviewSummaryStep } from './steps/ReviewSummaryStep'

interface WizardData {
  projectDetails: {
    name: string
    description: string
    location: string
    disasterType: string
    estimatedCost: number
  } | null
  documents: Array<{
    id: string
    name: string
    type: string
    size: number
    uploadedAt: Date
  }>
  selectedPolicies: string[]
}

const steps = [
  {
    id: 'project-details',
    title: 'Project Details',
    description: 'Basic information about your project',
    icon: FileText,
  },
  {
    id: 'document-upload',
    title: 'Document Upload',
    description: 'Upload required documentation',
    icon: Upload,
  },
  {
    id: 'policy-selection',
    title: 'Policy Selection',
    description: 'Select applicable policies and requirements',
    icon: Settings,
  },
  {
    id: 'review-summary',
    title: 'Review & Submit',
    description: 'Review your submission and generate compliance report',
    icon: BarChart3,
  },
]

export function ComplianceWizard() {
  const [currentStep, setCurrentStep] = useState(0)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [wizardData, setWizardData] = useState<WizardData>({
    projectDetails: null,
    documents: [],
    selectedPolicies: [],
  })

  const progress = ((currentStep + 1) / steps.length) * 100

  const updateWizardData = (key: keyof WizardData, value: unknown) => {
    setWizardData(prev => ({
      ...prev,
      [key]: value,
    }))
  }

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      // Handle successful submission
      console.log('Wizard data submitted:', wizardData)
    } catch (error) {
      console.error('Submission error:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <ProjectDetailsStep
            data={wizardData.projectDetails}
            onUpdate={(data) => updateWizardData('projectDetails', data)}
          />
        )
      case 1:
        return (
          <DocumentUploadStep
            documents={wizardData.documents}
            onUpdate={(documents) => updateWizardData('documents', documents)}
          />
        )
      case 2:
        return (
          <PolicySelectionStep
            selectedPolicies={wizardData.selectedPolicies}
            onUpdate={(policies) => updateWizardData('selectedPolicies', policies)}
          />
        )
      case 3:
        return (
          <ReviewSummaryStep
            data={wizardData}
            onSubmit={handleSubmit}
            isSubmitting={isSubmitting}
          />
        )
      default:
        return null
    }
  }

  const canProceed = () => {
    switch (currentStep) {
      case 0:
        return wizardData.projectDetails !== null
      case 1:
        return wizardData.documents.length > 0
      case 2:
        return wizardData.selectedPolicies.length > 0
      case 3:
        return true
      default:
        return false
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-white mb-4">
          FEMA PA Compliance Wizard
        </h1>
        <p className="text-white/70 text-lg">
          Step-by-step guidance for Public Assistance compliance review
        </p>
      </div>

      {/* Progress Bar */}
      <Card className="glass border-white/20">
        <CardContent className="p-6">
          <div className="flex justify-between items-center mb-4">
            <span className="text-sm font-medium text-white">
              Step {currentStep + 1} of {steps.length}
            </span>
            <span className="text-sm text-white/70">
              {Math.round(progress)}% Complete
            </span>
          </div>
          <Progress value={progress} className="h-2" />
        </CardContent>
      </Card>

      {/* Step Navigation */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {steps.map((step, index) => {
          const Icon = step.icon
          const isActive = index === currentStep
          const isCompleted = index < currentStep
          const isAccessible = index <= currentStep

          return (
            <motion.div
              key={step.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card 
                className={`cursor-pointer transition-all duration-300 ${
                  isActive 
                    ? 'glass border-blue-400 shadow-lg shadow-blue-400/20' 
                    : isCompleted
                    ? 'glass border-green-400 shadow-lg shadow-green-400/20'
                    : isAccessible
                    ? 'glass border-white/20 hover:border-white/40'
                    : 'glass border-white/10 opacity-50'
                }`}
                onClick={() => isAccessible && setCurrentStep(index)}
              >
                <CardContent className="p-4 text-center">
                  <div className={`w-12 h-12 mx-auto mb-3 rounded-full flex items-center justify-center ${
                    isActive 
                      ? 'bg-blue-500 text-white' 
                      : isCompleted
                      ? 'bg-green-500 text-white'
                      : 'bg-white/10 text-white/70'
                  }`}>
                    {isCompleted ? (
                      <CheckCircle className="w-6 h-6" />
                    ) : (
                      <Icon className="w-6 h-6" />
                    )}
                  </div>
                  <h3 className={`font-semibold text-sm mb-1 ${
                    isActive ? 'text-white' : 'text-white/80'
                  }`}>
                    {step.title}
                  </h3>
                  <p className="text-xs text-white/60">
                    {step.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          )
        })}
      </div>

      {/* Step Content */}
      <Card className="glass border-white/20">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-3">
            {React.createElement(steps[currentStep].icon, { className: "w-6 h-6" })}
            {steps[currentStep].title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {renderStepContent()}
            </motion.div>
          </AnimatePresence>
        </CardContent>
      </Card>

      {/* Navigation Buttons */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={currentStep === 0}
          className="border-white/20 text-white hover:bg-white/10"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Previous
        </Button>

        {currentStep === steps.length - 1 ? (
          <Button
            onClick={handleSubmit}
            disabled={!canProceed() || isSubmitting}
            className="bg-green-600 hover:bg-green-700"
          >
            {isSubmitting ? 'Submitting...' : 'Submit Review'}
            <CheckCircle className="w-4 h-4 ml-2" />
          </Button>
        ) : (
          <Button
            onClick={handleNext}
            disabled={!canProceed()}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Next
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        )}
      </div>
    </div>
  )
}
