(function(){
  const PAGE = (document.body.dataset.page||"").toLowerCase(); // cbcs|emergency|worksheet|upload
  const FLOW = (document.body.dataset.flow||"professional").toLowerCase();
  const STEP = Number(document.body.dataset.step||1);
  const ASSUME_KEY = "MaxAssumptions:" + (FLOW+"-"+PAGE);

  function load(url){ return fetch(url).then(r=>r.json()); }
  function words(s){ return (s||"").toLowerCase().split(/[^a-z0-9]+/).filter(Boolean); }
  function scoreKeywords(name, list){ const w=words(name); return list.reduce((a,k)=>a+(w.includes(k.toLowerCase())?1:0),0); }
  function ledger(get){ 
    if(get) return JSON.parse(localStorage.getItem(ASSUME_KEY)||"[]");
    return {
      add:(it)=>{ 
        const cur=ledger(true); 
        cur.push({...it, timestamp: new Date().toISOString()}); 
        localStorage.setItem(ASSUME_KEY,JSON.stringify(cur.slice(-10))); // keep last 10
      },
      clear:()=>localStorage.removeItem(ASSUME_KEY)
    };
  }

  // inject CSS
  (function(){ 
    const link=document.createElement('link'); 
    link.rel='stylesheet'; 
    link.href='assist/autofill.css'; 
    document.head.appendChild(link); 
  })();

  // panel UI
  function ui(){
    const fab = document.createElement('button'); 
    fab.className='ap-fab'; 
    fab.innerHTML='🤖 AutoPilot';
    
    const panel = document.createElement('div'); 
    panel.className='ap-panel'; 
    panel.style.display='none';
    panel.innerHTML = `
      <div class="ap-h">
        <b>Max AutoPilot</b>
        <button class="ap-x" title="Close">✕</button>
      </div>
      <div class="ap-b">
        <div class="conf" id="apConf"></div>
        <div id="apExplain"></div>
        <div class="assump" id="apAssump" style="display:none"></div>
        <div class="ap-btns">
          <button class="ap-apply">Apply</button>
          <button class="ap-undo">Undo</button>
        </div>
      </div>`;
    
    document.body.appendChild(fab); 
    document.body.appendChild(panel);
    
    fab.onclick=()=>{ 
      const isVisible = panel.style.display==='block';
      panel.style.display = isVisible ? 'none' : 'block'; 
    };
    
    panel.querySelector('.ap-x').onclick=()=>panel.style.display='none';
    
    return {
      set(conf, explain, assumptions){
        const confEl = panel.querySelector('#apConf');
        confEl.textContent = conf;
        
        // Add confidence styling
        const confNum = parseFloat(conf.match(/(\d+)%/)?.[1] || 0);
        confEl.className = 'conf ' + (confNum >= 70 ? 'high' : confNum >= 50 ? 'medium' : 'low');
        
        panel.querySelector('#apExplain').innerHTML = explain||"";
        const a = panel.querySelector('#apAssump');
        if(assumptions && assumptions.length){ 
          a.style.display='block'; 
          a.innerHTML = `<b>Assumptions:</b><ul>${assumptions.map(x=>`<li>${x}</li>`).join('')}</ul>`; 
        } else { 
          a.style.display='none'; 
          a.innerHTML=''; 
        }
      },
      onApply(fn){ panel.querySelector('.ap-apply').onclick = fn; },
      onUndo(fn){ panel.querySelector('.ap-undo').onclick = fn; },
      show(){ panel.style.display='block'; },
      hide(){ panel.style.display='none'; }
    };
  }

  function clickByText(rx){ 
    const el=[...document.querySelectorAll('button, a, input[type="submit"]')].find(b=>rx.test((b.textContent||b.value||""))); 
    if(el){ 
      el.click(); 
      return true; 
    } 
    return false; 
  }

  const pages = {
    cbcs: async (presets)=>{
      const names = [...document.querySelectorAll('input[type=file]')].flatMap(inp=>[...(inp.files||[])]).map(f=>f.name)
        .concat([...document.querySelectorAll('[data-file],[data-filename]')].map(x=>x.dataset.file||x.dataset.filename||''));
      
      const catScores = Object.fromEntries(['C','D','E','F','G'].map(c=>{
        const keys = presets.categoryFromFilenames[c]||[];
        return [c, names.reduce((acc,nm)=>acc+scoreKeywords(nm,keys),0)];
      }));
      
      let best='C', bestS=-1; 
      for(const [k,v] of Object.entries(catScores)){ 
        if(v>bestS){best=k;bestS=v;} 
      }
      
      const conf = Math.min(0.9, 0.2 + (bestS/3));
      const baseline = (presets.cbcsBaselines[best]||[]);
      const as = [];
      
      if(names.length) as.push(`Detected ${names.length} uploaded file(s).`);
      as.push(`Category suggestion ${best} from filename signals: ${bestS} hit(s).`);
      if(baseline.length) as.push(`Baseline CBCS codes proposed: ${baseline.slice(0,6).join(', ')}${baseline.length>6?'…':''}`);

      const apply = ()=>{
        const sel = document.querySelector('#cat, #workCategory, select[name*="cat"], select[name*="Cat"]');
        if(sel){ 
          sel.value = best; 
          sel.dispatchEvent(new Event('change',{bubbles:true})); 
        }
        
        baseline.forEach(code=>{
          const box = [...document.querySelectorAll('input[type=checkbox]')].find(cb=>{
            if(cb.value===code || cb.dataset.code===code) return true;
            const lab = cb.closest('label'); 
            return lab && (lab.textContent||'').includes(code);
          });
          if(box && !box.checked){ box.click(); }
        });
        
        clickByText(/Generate Technical Justification|Generate|Create Justification/i);
        
        ledger().add({
          action: 'CBCS AutoPilot',
          category: best,
          codes: baseline,
          confidence: conf
        });
        
        try{ 
          localStorage.setItem('ComplianceMax_Demo:last_cbcs_autopilot','1');
          localStorage.setItem('ComplianceMax_Demo:last_cbcs', JSON.stringify({cat: best, codes: baseline}));
        }catch(e){}
      };
      
      const undo = ()=>{
        baseline.forEach(code=>{
          const box = [...document.querySelectorAll('input[type=checkbox]')].find(cb=>{
            if(cb.value===code || cb.dataset.code===code) return true;
            const lab = cb.closest('label'); 
            return lab && (lab.textContent||'').includes(code);
          });
          if(box && box.checked){ box.click(); }
        });
      };
      
      return {conf, best, baseline, assumptions: as, apply, undo};
    },

    emergency: async (presets)=>{
      const txt = document.body.innerText.toLowerCase();
      const aHits = ["debris","monitoring","load ticket","chipping","stump","removal","disposal"].some(k=>txt.includes(k));
      const bHits = ["threat","emergency","imminent","life","safety","protective"].some(k=>txt.includes(k));
      
      let best = 'A';
      let conf = 0.55;
      
      if(aHits && !bHits) {
        best = 'A';
        conf = 0.75;
      } else if(bHits && !aHits) {
        best = 'B';
        conf = 0.75;
      } else if(aHits && bHits) {
        best = 'A'; // Default to A if both
        conf = 0.65;
      }
      
      const checklist = presets.emergencyChecklists[best]||[];
      const as = [
        `Recommended Category ${best} based on page content analysis.`,
        `Detection: ${aHits?'debris terms found':'no debris terms'}, ${bHits?'emergency terms found':'no emergency terms'}.`,
        `Checklist: ${checklist.join(', ')}`
      ];
      
      const apply = ()=>{
        const sel = document.querySelector('#emCat, #workCategory, select[name*="cat"], select[name*="Cat"]');
        if(sel){ 
          sel.value = best; 
          sel.dispatchEvent(new Event('change',{bubbles:true})); 
        }
        
        const req = document.querySelector('#requiredDocs, .pod-left, [data-pod="applicant"], .requirements, .checklist');
        if(req){ 
          req.insertAdjacentHTML('beforeend', `
            <div style="margin-top:12px;color:#111827;background:#e0f2fe;border:1px solid #0891b2;border-radius:8px;padding:12px;font-family:system-ui;">
              <div style="font-weight:600;color:#0e7490;margin-bottom:8px;">🤖 AutoPilot Docs (Category ${best}):</div>
              <ul style="margin:0 0 0 18px;line-height:1.4;">
                ${checklist.map(x=>`<li style="margin:4px 0;">${x}</li>`).join('')}
              </ul>
            </div>`); 
        }
        
        ledger().add({
          action: 'Emergency AutoPilot',
          category: best,
          checklist: checklist,
          confidence: conf
        });
      };
      
      const undo = ()=>{
        // Remove the inserted checklist
        const inserted = document.querySelector('[style*="AutoPilot Docs"]')?.closest('div');
        if(inserted) inserted.remove();
      };
      
      return {conf, best, assumptions: as, apply, undo};
    },

    worksheet: async (presets)=>{
      let cat = 'C';
      try{ 
        const raw = localStorage.getItem('ComplianceMax_Demo:last_cbcs'); 
        if(raw){ 
          const d=JSON.parse(raw); 
          if(d.cat) cat = d.cat; 
        } 
      }catch(e){}
      
      const seeds = presets.worksheetSeeds[cat]||[];
      const conf = seeds.length?0.75:0.5;
      const as = [`Seeding ${seeds.length} line item(s) typical for category ${cat}. Quantities provided; set rates per cost source.`];
      
      const apply = ()=>{
        seeds.forEach(s=>{
          const table = [...document.querySelectorAll('table')].find(t=>{
            const header = t.previousElementSibling?.textContent || t.querySelector('caption')?.textContent || '';
            return header.toLowerCase().includes(s.cat.toLowerCase());
          });
          
          if(!table) return;
          
          const tbody = table.querySelector('tbody')||table;
          const tr = document.createElement('tr');
          tr.innerHTML = `
            <td>${s.desc}</td>
            <td><input type="number" value="${s.qty}" step="0.01" style="width:100%;"></td>
            <td>${s.unit}</td>
            <td><input type="number" value="${s.rate}" step="0.01" style="width:100%;"></td>
            <td class="line-total">0.00</td>
            <td>
              <select style="width:100%;">
                <option${s.source==='RSMeans'?' selected':''}>RSMeans</option>
                <option${s.source==='FEMA Equipment Rates'?' selected':''}>FEMA Equipment Rates</option>
                <option${s.source==='Local Market Rate'?' selected':''}>Local Market Rate</option>
                <option>Vendor Quote</option>
                <option>FEMA Cost Codes</option>
                <option>National Construction Estimator</option>
                <option>BCIS</option>
                <option>Other</option>
              </select>
            </td>
            <td><input type="text" value="${s.ref||''}" placeholder="Book/Page, Quote #, URL" style="width:100%;"></td>`;
          tbody.appendChild(tr);
        });
        
        // Trigger recalculation
        const recalc = window.recalcTotals || window.updateTotals || window.calculateTotals;
        if(typeof recalc === 'function'){ 
          setTimeout(recalc, 100); 
        }
        
        ledger().add({
          action: 'Worksheet AutoPilot',
          category: cat,
          seedCount: seeds.length
        });
      };
      
      const undo = ()=>{
        // Could implement row removal, but keeping it simple for now
      };
      
      return {conf, cat, assumptions: as, apply, undo};
    },

    upload: async (presets)=>{
      const inputs = [...document.querySelectorAll('input[type=file]')];
      let count=0; 
      const tags = presets.tagsFromExtensions||{}; 
      const as=[];
      
      inputs.forEach(inp=>{
        const files = [...(inp.files||[])];
        files.forEach(f=>{
          const ext = (/\.[a-z0-9]+$/i.exec(f.name)||[''])[0].toLowerCase();
          const sugg = tags[ext]||[];
          if(sugg.length){ 
            as.push(`"${f.name}" → ${sugg.join(' / ')}`); 
            count++; 
          }
        });
      });
      
      const conf = count?0.65:0.4;
      
      const apply = ()=>{
        document.querySelectorAll('[data-file-row], .file-row, .upload-item').forEach(row=>{
          const fn = row.getAttribute('data-filename')||row.textContent||'';
          const ext = (/\.[a-z0-9]+$/i.exec(fn)||[''])[0].toLowerCase();
          const sugg = tags[ext]||[];
          if(!sugg.length) return;
          
          // Avoid duplicate tags
          if(row.querySelector('.ap-tags')) return;
          
          row.insertAdjacentHTML('beforeend', `
            <span class="ap-tags" style="margin-left:8px;background:#ecfeff;border:1px solid #67e8f9;color:#155e75;border-radius:6px;padding:4px 8px;font:12px system-ui;">
              Tags: ${sugg.join(', ')}
            </span>`);
        });
        
        ledger().add({
          action: 'Upload AutoPilot',
          taggedFiles: count
        });
      };
      
      const undo = ()=>{
        document.querySelectorAll('.ap-tags').forEach(tag => tag.remove());
      };
      
      return {conf, assumptions: as, apply, undo};
    }
  };

  // Initialize when DOM is ready and presets are loaded
  if(document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }

  function init() {
    if(!PAGE) return;
    
    load('assist/presets.json').then(async presets=>{
      const panel = ui();
      const pick = pages[PAGE] || null;
      if(!pick) return;
      
      const out = await pick(presets);
      const assumptions = (ledger(true)||[]).slice(-6).map(x => `${x.action} (${new Date(x.timestamp).toLocaleTimeString()})`);
      
      panel.set(`Confidence: ${(out.conf*100|0)}%`, `Ready to apply ${PAGE} optimizations.`, assumptions);
      panel.onApply(()=>{ 
        if(out.apply) {
          out.apply(); 
          panel.show(); 
          
          // Notify Max Assist
          if(window.sayToMaxAssist) {
            window.sayToMaxAssist(`🤖 AutoPilot applied ${PAGE} optimizations with ${(out.conf*100|0)}% confidence.`);
          }
          
          // Advance Sidewalk if available
          if(window.Sidewalk && typeof window.Sidewalk.advance === 'function') {
            window.Sidewalk.advance(1);
          }
        }
      });
      panel.onUndo(()=>{ 
        if(out.undo) {
          out.undo(); 
          ledger().clear(); 
          panel.show(); 
          
          if(window.sayToMaxAssist) {
            window.sayToMaxAssist(`🔄 AutoPilot changes undone for ${PAGE}.`);
          }
        }
      });
    }).catch(err => {
      console.warn('AutoPilot presets failed to load:', err);
    });
  }
})();
