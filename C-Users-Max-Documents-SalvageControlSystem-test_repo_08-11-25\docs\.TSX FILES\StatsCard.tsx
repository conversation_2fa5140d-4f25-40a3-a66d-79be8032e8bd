
'use client'

import { motion } from 'framer-motion'
import { LucideIcon } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'

interface StatsCardProps {
  title: string
  value: string | number
  icon: LucideIcon
  color: string
  bgColor: string
  change: string
  changeType: 'positive' | 'negative'
  delay?: number
}

export function StatsCard({
  title,
  value,
  icon: Icon,
  color,
  bgColor,
  change,
  changeType,
  delay = 0
}: StatsCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: delay / 1000 }}
    >
      <Card className="glass border-white/20 hover:border-white/30 transition-all duration-300">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/70 text-sm font-medium">{title}</p>
              <p className="text-2xl font-bold text-white mt-2">{value}</p>
              <div className="flex items-center mt-2">
                <span
                  className={`text-xs font-medium ${
                    changeType === 'positive' ? 'text-green-400' : 'text-red-400'
                  }`}
                >
                  {change}
                </span>
                <span className="text-white/50 text-xs ml-1">vs last month</span>
              </div>
            </div>
            <div className={`p-3 rounded-full ${bgColor} ${color}`}>
              <Icon className="w-6 h-6" />
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
