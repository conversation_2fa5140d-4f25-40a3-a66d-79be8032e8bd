bash: python3: command not found
=== CHANGE_LOG ===
js/sidewalk-hints.js | FIXED | guard inserted
cbcs_demo.html | NOOP | already ordered; no-op
compliance_workflow.html | NOOP | already ordered; no-op
comprehensive_report_system.html | NOOP | already ordered; no-op
dashboard.html | NOOP | already ordered; no-op
document_upload_system.html | NOOP | already ordered; no-op
emergency_intake.html | MOD | already ordered; added sidewalk-hints.css; added sidewalk-hints.js
landing_page.html | NOOP | already ordered; no-op
legal.html | NOOP | already ordered; no-op
mitigation_ehp_procurement_system.html | NOOP | already ordered; no-op
packet.html | NOOP | already ordered; no-op
permanent_work_intake.html | NOOP | already ordered; no-op
professional_landing.html | NOOP | already ordered; no-op
projects.html | NOOP | already ordered; no-op
report.html | MOD | already ordered; added sidewalk-hints.css; added sidewalk-hints.js
signin.html | NOOP | already ordered; no-op
signup.html | NOOP | already ordered; no-op
worksheet.html | MOD | already ordered; added sidewalk-hints.css; added sidewalk-hints.js
