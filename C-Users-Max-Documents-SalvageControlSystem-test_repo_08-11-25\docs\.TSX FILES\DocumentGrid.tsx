
'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  FileText, 
  Image, 
  File, 
  Download, 
  Trash2, 
  Eye,
  CheckCircle,
  Clock,
  AlertTriangle
} from 'lucide-react'
import { formatFileSize, formatDate } from '@/lib/utils'

interface Document {
  id: string
  name: string
  type: string
  size: number
  uploadedAt: Date
  category: string
  status: 'processing' | 'completed' | 'error'
  tags: string[]
  description?: string
}

interface DocumentGridProps {
  documents: Document[]
  selectedDocuments: Set<string>
  onSelectDocument: (id: string) => void
  onSelectAll: () => void
  onDeleteDocument: (id: string) => void
}

export function DocumentGrid({ 
  documents, 
  selectedDocuments, 
  onSelectDocument, 
  onSelectAll, 
  onDeleteDocument 
}: DocumentGridProps) {
  const getFileIcon = (type: string) => {
    if (type.includes('pdf')) return FileText
    if (type.includes('image')) return Image
    if (type.includes('word') || type.includes('document')) return FileText
    return File
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-400" />
      case 'processing':
        return <Clock className="w-4 h-4 text-yellow-400" />
      case 'error':
        return <AlertTriangle className="w-4 h-4 text-red-400" />
      default:
        return null
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'border-green-400/20'
      case 'processing':
        return 'border-yellow-400/20'
      case 'error':
        return 'border-red-400/20'
      default:
        return 'border-white/20'
    }
  }

  if (documents.length === 0) {
    return (
      <Card className="glass border-white/20">
        <CardContent className="p-12 text-center">
          <FileText className="w-16 h-16 text-white/40 mx-auto mb-4" />
          <h3 className="text-white font-medium mb-2">No documents found</h3>
          <p className="text-white/60">
            Upload documents or adjust your search filters to see results.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {/* Select All */}
      <div className="flex items-center gap-3">
        <input
          type="checkbox"
          checked={selectedDocuments.size === documents.length && documents.length > 0}
          onChange={onSelectAll}
          className="w-4 h-4 rounded border-white/20 bg-white/10 text-blue-600 focus:ring-blue-500"
        />
        <span className="text-white/70 text-sm">
          Select all ({documents.length} documents)
        </span>
      </div>

      {/* Document Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {documents.map((document) => {
          const Icon = getFileIcon(document.type)
          const isSelected = selectedDocuments.has(document.id)

          return (
            <Card
              key={document.id}
              className={`cursor-pointer transition-all duration-200 hover:scale-105 ${
                isSelected
                  ? 'glass border-blue-400 shadow-lg shadow-blue-400/20'
                  : `glass ${getStatusColor(document.status)} hover:border-white/40`
              }`}
              onClick={() => onSelectDocument(document.id)}
            >
              <CardContent className="p-4">
                <div className="space-y-3">
                  {/* Header */}
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={(e) => {
                          e.stopPropagation()
                          onSelectDocument(document.id)
                        }}
                        className="w-4 h-4 rounded border-white/20 bg-white/10 text-blue-600 focus:ring-blue-500"
                      />
                      {getStatusIcon(document.status)}
                    </div>
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          // Handle view
                        }}
                        className="text-white/60 hover:text-white hover:bg-white/10 p-1 h-auto"
                      >
                        <Eye className="w-3 h-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          // Handle download
                        }}
                        className="text-white/60 hover:text-white hover:bg-white/10 p-1 h-auto"
                      >
                        <Download className="w-3 h-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          onDeleteDocument(document.id)
                        }}
                        className="text-red-400/60 hover:text-red-400 hover:bg-red-400/10 p-1 h-auto"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>

                  {/* File Icon */}
                  <div className="flex justify-center">
                    <div className="w-16 h-16 bg-white/10 rounded-lg flex items-center justify-center">
                      <Icon className="w-8 h-8 text-white/70" />
                    </div>
                  </div>

                  {/* File Info */}
                  <div className="text-center">
                    <h3 className="text-white font-medium text-sm truncate" title={document.name}>
                      {document.name}
                    </h3>
                    <p className="text-white/60 text-xs mt-1">
                      {formatFileSize(document.size)}
                    </p>
                  </div>

                  {/* Category */}
                  <div className="text-center">
                    <span className="px-2 py-1 bg-white/10 text-white/80 text-xs rounded-full">
                      {document.category}
                    </span>
                  </div>

                  {/* Description */}
                  {document.description && (
                    <p className="text-white/60 text-xs text-center line-clamp-2">
                      {document.description}
                    </p>
                  )}

                  {/* Tags */}
                  {document.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 justify-center">
                      {document.tags.slice(0, 2).map((tag, index) => (
                        <span
                          key={index}
                          className="px-1.5 py-0.5 bg-blue-500/20 text-blue-400 text-xs rounded"
                        >
                          {tag}
                        </span>
                      ))}
                      {document.tags.length > 2 && (
                        <span className="px-1.5 py-0.5 bg-white/10 text-white/60 text-xs rounded">
                          +{document.tags.length - 2}
                        </span>
                      )}
                    </div>
                  )}

                  {/* Upload Date */}
                  <p className="text-white/50 text-xs text-center">
                    {formatDate(document.uploadedAt)}
                  </p>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
    </div>
  )
}
