
/**
 * Optimized Head Component
 * Handles CSS preloading and prevents warnings in both localhost and preview environments
 * FIXED: Removed onLoad JSX prop assignment per diagnostic analysis - replaced with useEffect hook
 */

'use client';

import Head from 'next/head';
import { useEffect, useRef } from 'react';
import { environmentUtils } from '@/lib/websocket-config';

interface OptimizedHeadProps {
  title?: string;
  description?: string;
  children?: React.ReactNode;
}

export default function OptimizedHead({ 
  title = 'ComplianceMax - FEMA PA Compliance Management',
  description = 'Comprehensive FEMA Public Assistance compliance management system',
  children 
}: OptimizedHeadProps) {
  // Fix for onLoad listener error - use ref and useEffect instead of JSX onLoad prop
  const linkRef = useRef<HTMLLinkElement>(null);

  useEffect(() => {
    const link = linkRef.current;
    if (link) {
      const handleLoad = () => {
        link.rel = 'stylesheet';
      };
      
      link.addEventListener('load', handleLoad);
      return () => link.removeEventListener('load', handleLoad);
    }
  }, []);

  return (
    <Head>
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <link rel="icon" href="/favicon.ico" />
      
      {/* FIXED: Preload critical CSS using ref-based approach to prevent React onLoad warnings */}
      <link
        ref={linkRef}
        rel="preload"
        href="/_next/static/css/app/layout.css"
        as="style"
      />
      
      {/* Preload critical fonts */}
      <link
        rel="preload"
        href="/_next/static/media/e4af272ccee01ff0-s.p.woff2"
        as="font"
        type="font/woff2"
        crossOrigin=""
      />
      
      {/* Environment-specific meta tags */}
      {environmentUtils.isPreviewEnv && (
        <>
          <meta name="robots" content="noindex, nofollow" />
          <meta name="environment" content="preview" />
        </>
      )}
      
      {environmentUtils.isDevelopment && (
        <meta name="environment" content="development" />
      )}
      
      {/* Disable telemetry in preview environments */}
      {environmentUtils.isPreviewEnv && (
        <meta name="next-telemetry" content="disabled" />
      )}
      
      {children}
    </Head>
  );
}

/**
 * CSS Preload Hook
 * Programmatically preload CSS files to prevent warnings
 * UPDATED: Enhanced with better error handling and logging
 */
export function usePreloadCSS() {
  const preloadCSS = (href: string) => {
    if (typeof document !== 'undefined') {
      const existingLink = document.querySelector(`link[href="${href}"]`);
      if (!existingLink) {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'style';
        link.href = href;
        
        // Use addEventListener instead of onload property assignment
        link.addEventListener('load', () => {
          link.rel = 'stylesheet';
          console.log(`CSS preloaded and activated: ${href}`);
        });
        
        link.addEventListener('error', (error) => {
          console.warn(`Failed to preload CSS: ${href}`, error);
        });
        
        document.head.appendChild(link);
      }
    }
  };

  return { preloadCSS };
}

/**
 * Resource Hints Component
 * Provides DNS prefetch and preconnect hints for better performance
 */
export function ResourceHints() {
  return (
    <Head>
      {/* DNS prefetch for external resources */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//fonts.gstatic.com" />
      
      {/* Preconnect to API endpoints */}
      {environmentUtils.isLocalhost && (
        <link rel="preconnect" href="http://localhost:8000" />
      )}
      
      {environmentUtils.isPreviewEnv && (
        <link rel="preconnect" href="https://f30526bfd-8001.preview.abacusai.app" />
      )}
    </Head>
  );
}
