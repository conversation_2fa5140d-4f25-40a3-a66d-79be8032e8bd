
'use client'

import { useState } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  CheckCircle, 
  AlertTriangle, 
  FileText, 
  MapPin, 
  DollarSign,
  Calendar,
  Download,
  Send,
  Eye,
  BarChart3,
  Upload
} from 'lucide-react'
import { formatFileSize } from '@/lib/utils'

interface ReviewSummaryStepProps {
  data: {
    projectDetails: {
      name: string
      description: string
      location: string
      disasterType: string
      estimatedCost: number
    } | null
    documents: Array<{
      id: string
      name: string
      type: string
      size: number
      uploadedAt: Date
    }>
    selectedPolicies: string[]
  }
  onSubmit: () => void
  isSubmitting: boolean
}

const mockPolicyTitles: Record<string, string> = {
  'stafford-act': 'Robert T. Stafford Disaster Relief and Emergency Assistance Act',
  'nepa': 'National Environmental Policy Act (NEPA)',
  'nhpa': 'National Historic Preservation Act (NHPA)',
  'esa': 'Endangered Species Act (ESA)',
  'cwa': 'Clean Water Act (CWA)',
  'uniform-act': 'Uniform Relocation Assistance Act'
}

export function ReviewSummaryStep({ data, onSubmit, isSubmitting }: ReviewSummaryStepProps) {
  const [showPreview, setShowPreview] = useState(false)

  const { projectDetails, documents, selectedPolicies } = data

  const complianceScore = calculateComplianceScore()
  const riskLevel = getRiskLevel(complianceScore)

  function calculateComplianceScore(): number {
    let score = 0
    
    // Project details completeness (30%)
    if (projectDetails) {
      score += 30
    }
    
    // Document completeness (40%)
    const requiredDocCount = 5 // Based on required documents
    const uploadedDocCount = Math.min(documents.length, requiredDocCount)
    score += (uploadedDocCount / requiredDocCount) * 40
    
    // Policy compliance (30%)
    const mandatoryPolicies = ['stafford-act', 'nepa']
    const selectedMandatory = mandatoryPolicies.filter(id => selectedPolicies.includes(id))
    score += (selectedMandatory.length / mandatoryPolicies.length) * 30
    
    return Math.round(score)
  }

  function getRiskLevel(score: number): { level: string; color: string; description: string } {
    if (score >= 90) {
      return {
        level: 'Low Risk',
        color: 'text-green-400',
        description: 'Project meets all compliance requirements'
      }
    } else if (score >= 70) {
      return {
        level: 'Medium Risk',
        color: 'text-yellow-400',
        description: 'Some compliance gaps identified'
      }
    } else {
      return {
        level: 'High Risk',
        color: 'text-red-400',
        description: 'Significant compliance issues require attention'
      }
    }
  }

  const generateReport = () => {
    // This would generate and download a PDF report
    console.log('Generating compliance report...')
  }

  return (
    <div className="space-y-6">
      {/* Compliance Score */}
      <Card className="glass border-white/20">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Compliance Assessment
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center mb-6">
            <div className="text-4xl font-bold text-white mb-2">
              {complianceScore}%
            </div>
            <div className={`text-lg font-medium ${riskLevel.color} mb-1`}>
              {riskLevel.level}
            </div>
            <p className="text-white/70 text-sm">
              {riskLevel.description}
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-white/5 rounded-lg">
              <FileText className="w-8 h-8 text-blue-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">
                {projectDetails ? '✓' : '✗'}
              </div>
              <p className="text-white/70 text-sm">Project Details</p>
            </div>
            
            <div className="text-center p-4 bg-white/5 rounded-lg">
              <Upload className="w-8 h-8 text-green-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">
                {documents.length}
              </div>
              <p className="text-white/70 text-sm">Documents Uploaded</p>
            </div>
            
            <div className="text-center p-4 bg-white/5 rounded-lg">
              <CheckCircle className="w-8 h-8 text-purple-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">
                {selectedPolicies.length}
              </div>
              <p className="text-white/70 text-sm">Policies Selected</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Project Summary */}
      {projectDetails && (
        <Card className="glass border-white/20">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <FileText className="w-5 h-5" />
              Project Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-white font-medium mb-3">Project Information</h4>
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <FileText className="w-4 h-4 text-white/60 mt-0.5" />
                    <div>
                      <p className="text-white/80 text-sm">Name</p>
                      <p className="text-white font-medium">{projectDetails.name}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3">
                    <MapPin className="w-4 h-4 text-white/60 mt-0.5" />
                    <div>
                      <p className="text-white/80 text-sm">Location</p>
                      <p className="text-white font-medium">{projectDetails.location}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3">
                    <AlertTriangle className="w-4 h-4 text-white/60 mt-0.5" />
                    <div>
                      <p className="text-white/80 text-sm">Disaster Type</p>
                      <p className="text-white font-medium">{projectDetails.disasterType}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3">
                    <DollarSign className="w-4 h-4 text-white/60 mt-0.5" />
                    <div>
                      <p className="text-white/80 text-sm">Estimated Cost</p>
                      <p className="text-white font-medium">
                        ${projectDetails.estimatedCost.toLocaleString()}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div>
                <h4 className="text-white font-medium mb-3">Description</h4>
                <p className="text-white/70 text-sm leading-relaxed">
                  {projectDetails.description}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Documents Summary */}
      <Card className="glass border-white/20">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Upload className="w-5 h-5" />
            Documents ({documents.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {documents.length > 0 ? (
            <div className="space-y-2">
              {documents.map((doc) => (
                <div key={doc.id} className="flex items-center gap-3 p-3 bg-white/5 rounded-lg">
                  <FileText className="w-4 h-4 text-white/60" />
                  <div className="flex-1 min-w-0">
                    <p className="text-white font-medium truncate">{doc.name}</p>
                    <p className="text-white/60 text-xs">
                      {formatFileSize(doc.size)} • {new Date(doc.uploadedAt).toLocaleDateString()}
                    </p>
                  </div>
                  <CheckCircle className="w-4 h-4 text-green-400" />
                </div>
              ))}
            </div>
          ) : (
            <p className="text-white/60 text-center py-4">No documents uploaded</p>
          )}
        </CardContent>
      </Card>

      {/* Selected Policies */}
      <Card className="glass border-white/20">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <CheckCircle className="w-5 h-5" />
            Selected Policies ({selectedPolicies.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {selectedPolicies.length > 0 ? (
            <div className="space-y-2">
              {selectedPolicies.map((policyId) => (
                <div key={policyId} className="flex items-center gap-3 p-3 bg-white/5 rounded-lg">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <p className="text-white font-medium">
                    {mockPolicyTitles[policyId] || policyId}
                  </p>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-white/60 text-center py-4">No policies selected</p>
          )}
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex flex-col sm:flex-row gap-4">
        <Button
          onClick={generateReport}
          variant="outline"
          className="flex-1 border-white/20 text-white hover:bg-white/10"
        >
          <Download className="w-4 h-4 mr-2" />
          Download Report
        </Button>
        
        <Button
          onClick={() => setShowPreview(!showPreview)}
          variant="outline"
          className="flex-1 border-white/20 text-white hover:bg-white/10"
        >
          <Eye className="w-4 h-4 mr-2" />
          {showPreview ? 'Hide Preview' : 'Preview Report'}
        </Button>
        
        <Button
          onClick={onSubmit}
          disabled={isSubmitting || complianceScore < 70}
          className="flex-1 bg-green-600 hover:bg-green-700"
        >
          <Send className="w-4 h-4 mr-2" />
          {isSubmitting ? 'Submitting...' : 'Submit for Review'}
        </Button>
      </div>

      {complianceScore < 70 && (
        <Card className="bg-red-500/10 border-red-400/20">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="text-white font-medium mb-1">Compliance Issues</h4>
                <p className="text-white/70 text-sm">
                  Your project has a compliance score below 70%. Please review and address 
                  the missing requirements before submitting for review.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
