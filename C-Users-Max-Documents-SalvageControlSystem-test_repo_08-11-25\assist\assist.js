(function(){
  const PAGE = (document.body.dataset.page || location.pathname.split('/').pop().replace('.html','')).toLowerCase();

  let faqs=[], flows={};
  fetch('assist/faqs.json').then(r=>r.json()).then(d=>faqs=d).catch(()=>{});
  fetch('assist/flows.json').then(r=>r.json()).then(d=>flows=d).catch(()=>{});

  const btn = document.createElement('button');
  btn.id='maxAssistBtn'; 
  btn.innerHTML='🤖 Max Assist';
  document.body.appendChild(btn);

  const panel = document.createElement('div');
  panel.id='maxAssist';
  panel.innerHTML = `
    <header>
      <div>
        <div class="title">Max Assist</div>
        <div class="subtitle">AI coach (no human handoff)</div>
      </div>
      <button onclick="this.closest('#maxAssist').style.display='none'" style="background:none;border:none;color:white;font-size:18px;cursor:pointer;">×</button>
    </header>
    <main></main>
    <footer>
      <input id="maxAssistInput" placeholder="Ask a question… (e.g., What do I need for Cat A?)"/>
      <button id="maxAssistSend">Send</button>
    </footer>`;
  document.body.appendChild(panel);

  const main = panel.querySelector('main');
  const input = panel.querySelector('#maxAssistInput');

  const say = (text, who='bot')=>{
    const div=document.createElement('div');
    div.className='msg '+(who==='you'?'you':'bot');
    div.innerHTML = text;
    main.appendChild(div);
    main.scrollTop = main.scrollHeight;
  };

  const suggestions = ()=>{
    const f = flows[PAGE] || {};
    if(!f.next || f.next.length === 0) return;
    
    const wrap=document.createElement('div');
    wrap.className='suggestions';
    wrap.innerHTML='<div class="small">Quick actions:</div>';
    
    (f.next||[]).forEach(label=>{
      const pill=document.createElement('span'); 
      pill.className='pill'; 
      pill.textContent=label;
      pill.onclick=()=>route(label);
      wrap.appendChild(pill);
    });
    main.appendChild(wrap);
  };

  const answerFromFaq = (q)=>{
    const hit = faqs.find(x => (x.page===PAGE || x.page==='any') && q.toLowerCase().includes(x.q.toLowerCase()));
    if(!hit) return null;
    const pro = `<details><summary>Pro details</summary><div class="small">${hit.a_pro}</div></details>`;
    return `<div>${hit.a_applicant}</div>${pro}`;
  };

  const route = (intent)=>{
    // Lightweight actions without backend
    switch((intent||'').toLowerCase()){
      case 'upload required docs':
        location.href = '/C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25//C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25/document_upload_system.html'; break;
      case 'go to worksheet':
      case 'stage to worksheet':
        location.href = '/C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25//C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25/worksheet.html'; break;
      case 'build report':
      case 'generate report':
        location.href = '/C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25//C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25/report.html'; break;
      case 'upload drawings/specs (pdf)':
        if(document.querySelector('input[type=file]')) {
          document.querySelector('input[type=file]').focus();
          say('📁 File upload area highlighted. Drop your PDF drawings/specs here.');
        } else {
          say('📁 Navigate to the file upload section to add your drawings/specs.');
        }
        break;
      case 'select cbcs codes':
        say('✅ Select applicable CBCS codes for your category, then click "Generate Technical Justification."');
        break;
      case 'check bucket totals':
        say('📊 Totals update live at the bottom of each bucket and as a grand total. Make sure each line has a cost source.');
        break;
      case 'new emergency intake':
        location.href = '/C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25//C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25/emergency_intake.html'; break;
      case 'new professional intake':
        location.href = '/C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25//C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25/cbcs_demo.html'; break;
      case 'open worksheet':
        location.href = '/C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25//C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25/worksheet.html'; break;
      case 'go to dashboard':
        location.href = '/C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25//C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25/dashboard.html'; break;
      default:
        say(`🔄 Action "${intent}" noted. Use the navigation or ask for specific help.`);
        break;
    }
  };

  const reply = (q)=>{
    // 1) Try FAQ
    const faq = answerFromFaq(q);
    if(faq){ say(faq); suggestions(); return; }

    // 2) Contextual guidance
    if(PAGE==='emergency_intake' && /cat(egory)?\s*a\b/i.test(q)) {
      const list = (flows.emergency_intake?.docs_required_A||[]).map(x=>`• ${x}`).join('<br>');
      say(`<div><strong>Category A (Debris): required items</strong><br>${list}</div>`); 
      suggestions(); return;
    }
    if(PAGE==='emergency_intake' && /cat(egory)?\s*b\b/i.test(q)) {
      const list = (flows.emergency_intake?.docs_required_B||[]).map(x=>`• ${x}`).join('<br>');
      say(`<div><strong>Category B (Emergency Work): required items</strong><br>${list}</div>`); 
      suggestions(); return;
    }

    // 3) Page-specific help
    const pageHelp = {
      'cbcs': 'Upload your project drawings/specs (PDF) to auto-select CBCS codes and generate technical justification.',
      'worksheet': 'Add line items with quantities, rates, and cost sources. Each non-zero line needs a documented source.',
      'report': 'Review compliance checks and download your complete FEMA PA packet.',
      'dashboard': 'Start a new project or continue working on existing submissions.',
      'emergency_intake': 'Select Category A (Debris) or B (Emergency Work) and upload required documentation.'
    };

    if(pageHelp[PAGE]) {
      say(`<div><strong>Page Help:</strong> ${pageHelp[PAGE]}</div>`);
      suggestions(); return;
    }

    // 4) Fallback deterministic message (LLM-ready hook)
    say(`<div>I couldn't find a precise answer. Try rephrasing or use a suggested step below.<br>
    <div class="small">💡 Tip: Ask about specific categories (Cat A, Cat B), cost sources, or compliance requirements.</div></div>`);
    suggestions();
  };

  // wire UI
  btn.onclick = ()=>{ 
    const isVisible = panel.style.display==='flex';
    panel.style.display = isVisible ? 'none' : 'flex'; 
    if(!isVisible) {
      input.focus();
      if(main.children.length === 0) {
        say(`<div><strong>Hi! I'm Max Assist.</strong> I'll guide you step-by-step through FEMA PA compliance. Ask anything or pick a step below.</div>`);
        suggestions();
      }
    }
  };
  
  panel.querySelector('#maxAssistSend').onclick = ()=>{
    const q = (input.value||'').trim(); if(!q) return;
    say(q,'you'); input.value=''; reply(q);
  };
  
  input.addEventListener('keydown', e=>{ 
    if(e.key==='Enter') panel.querySelector('#maxAssistSend').click(); 
  });

  // Global API for other scripts
  window.sayToMaxAssist = (msg) => {
    if(panel.style.display === 'flex') say(msg);
  };

  // Optional: Auto-open on first visit
  setTimeout(()=>{
    if(!localStorage.getItem('maxAssistSeen')) {
      btn.click();
      localStorage.setItem('maxAssistSeen', 'true');
    }
  }, 2000);
})();
