
'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  CheckCircle, 
  Circle, 
  Search, 
  Filter,
  BookOpen,
  <PERSON><PERSON><PERSON><PERSON>gle,
  Info
} from 'lucide-react'

interface Policy {
  id: string
  title: string
  description: string
  category: string
  requirements: string[]
  applicability: string
  mandatory: boolean
}

interface PolicySelectionStepProps {
  selectedPolicies: string[]
  onUpdate: (policies: string[]) => void
}

const mockPolicies: Policy[] = [
  {
    id: 'stafford-act',
    title: 'Robert T. Stafford Disaster Relief and Emergency Assistance Act',
    description: 'Federal disaster relief framework and eligibility requirements',
    category: 'Federal Law',
    requirements: [
      'Disaster declaration by President',
      'State and local government participation',
      'Cost-share requirements met'
    ],
    applicability: 'All FEMA PA projects',
    mandatory: true
  },
  {
    id: 'nepa',
    title: 'National Environmental Policy Act (NEPA)',
    description: 'Environmental review requirements for federal actions',
    category: 'Environmental',
    requirements: [
      'Environmental assessment completed',
      'Public participation if required',
      'Mitigation measures identified'
    ],
    applicability: 'Projects with environmental impact',
    mandatory: true
  },
  {
    id: 'nhpa',
    title: 'National Historic Preservation Act (NHPA)',
    description: 'Historic preservation review for federal undertakings',
    category: 'Historic Preservation',
    requirements: [
      'Section 106 consultation',
      'SHPO coordination',
      'Tribal consultation if applicable'
    ],
    applicability: 'Projects affecting historic properties',
    mandatory: false
  },
  {
    id: 'esa',
    title: 'Endangered Species Act (ESA)',
    description: 'Protection of threatened and endangered species',
    category: 'Environmental',
    requirements: [
      'Species consultation',
      'Critical habitat assessment',
      'Biological opinion if required'
    ],
    applicability: 'Projects in sensitive habitats',
    mandatory: false
  },
  {
    id: 'cwa',
    title: 'Clean Water Act (CWA)',
    description: 'Water quality protection and permitting requirements',
    category: 'Environmental',
    requirements: [
      'Section 404 permit if needed',
      'Water quality certification',
      'Stormwater management'
    ],
    applicability: 'Projects affecting waters of the US',
    mandatory: false
  },
  {
    id: 'uniform-act',
    title: 'Uniform Relocation Assistance Act',
    description: 'Relocation assistance and property acquisition requirements',
    category: 'Social',
    requirements: [
      'Relocation plan if applicable',
      'Fair market value appraisal',
      'Assistance to displaced persons'
    ],
    applicability: 'Projects requiring property acquisition',
    mandatory: false
  }
]

const categories = ['All', 'Federal Law', 'Environmental', 'Historic Preservation', 'Social']

export function PolicySelectionStep({ selectedPolicies, onUpdate }: PolicySelectionStepProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('All')
  const [filteredPolicies, setFilteredPolicies] = useState(mockPolicies)

  useEffect(() => {
    let filtered = mockPolicies

    if (selectedCategory !== 'All') {
      filtered = filtered.filter(policy => policy.category === selectedCategory)
    }

    if (searchTerm) {
      filtered = filtered.filter(policy =>
        policy.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        policy.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    setFilteredPolicies(filtered)
  }, [searchTerm, selectedCategory])

  const togglePolicy = (policyId: string) => {
    const isSelected = selectedPolicies.includes(policyId)
    if (isSelected) {
      onUpdate(selectedPolicies.filter(id => id !== policyId))
    } else {
      onUpdate([...selectedPolicies, policyId])
    }
  }

  const selectAllMandatory = () => {
    const mandatoryPolicies = mockPolicies
      .filter(policy => policy.mandatory)
      .map(policy => policy.id)
    
    const newSelection = [...new Set([...selectedPolicies, ...mandatoryPolicies])]
    onUpdate(newSelection)
  }

  const mandatoryPolicies = mockPolicies.filter(policy => policy.mandatory)
  const selectedMandatory = mandatoryPolicies.filter(policy => 
    selectedPolicies.includes(policy.id)
  ).length

  return (
    <div className="space-y-6">
      {/* Mandatory Policies Alert */}
      <Card className="bg-red-500/10 border-red-400/20">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <AlertTriangle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-white font-medium mb-1">Mandatory Policies</h4>
              <p className="text-white/70 text-sm mb-3">
                {selectedMandatory} of {mandatoryPolicies.length} mandatory policies selected. 
                All mandatory policies must be selected to proceed.
              </p>
              {selectedMandatory < mandatoryPolicies.length && (
                <Button
                  onClick={selectAllMandatory}
                  size="sm"
                  className="bg-red-600 hover:bg-red-700"
                >
                  Select All Mandatory
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Search and Filter */}
      <Card className="glass border-white/20">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60" />
              <input
                type="text"
                placeholder="Search policies..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-md text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div className="relative">
              <Filter className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60" />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="pl-10 pr-8 py-2 bg-white/10 border border-white/20 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {categories.map(category => (
                  <option key={category} value={category} className="bg-gray-800">
                    {category}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Policy List */}
      <div className="space-y-4">
        {filteredPolicies.map((policy) => {
          const isSelected = selectedPolicies.includes(policy.id)
          const isMandatory = policy.mandatory

          return (
            <Card
              key={policy.id}
              className={`cursor-pointer transition-all duration-200 ${
                isSelected
                  ? 'glass border-blue-400 shadow-lg shadow-blue-400/20'
                  : 'glass border-white/20 hover:border-white/40'
              } ${isMandatory ? 'ring-1 ring-red-400/30' : ''}`}
              onClick={() => togglePolicy(policy.id)}
            >
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 mt-1">
                    {isSelected ? (
                      <CheckCircle className="w-6 h-6 text-blue-400" />
                    ) : (
                      <Circle className="w-6 h-6 text-white/40" />
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between gap-4">
                      <div>
                        <h3 className="text-white font-medium mb-1 flex items-center gap-2">
                          {policy.title}
                          {isMandatory && (
                            <span className="px-2 py-1 bg-red-500/20 text-red-400 text-xs rounded-full">
                              Mandatory
                            </span>
                          )}
                        </h3>
                        <p className="text-white/70 text-sm mb-3">
                          {policy.description}
                        </p>
                      </div>
                      <div className="flex items-center gap-2 flex-shrink-0">
                        <span className="px-2 py-1 bg-white/10 text-white/80 text-xs rounded-full">
                          {policy.category}
                        </span>
                        <BookOpen className="w-4 h-4 text-white/60" />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <div>
                        <h4 className="text-white/80 text-sm font-medium mb-2">Requirements:</h4>
                        <ul className="space-y-1">
                          {policy.requirements.map((req, index) => (
                            <li key={index} className="text-white/60 text-sm flex items-start gap-2">
                              <div className="w-1 h-1 bg-white/40 rounded-full mt-2 flex-shrink-0" />
                              {req}
                            </li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h4 className="text-white/80 text-sm font-medium mb-2">Applicability:</h4>
                        <p className="text-white/60 text-sm">{policy.applicability}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {filteredPolicies.length === 0 && (
        <Card className="glass border-white/20">
          <CardContent className="p-8 text-center">
            <Search className="w-12 h-12 text-white/40 mx-auto mb-4" />
            <h3 className="text-white font-medium mb-2">No policies found</h3>
            <p className="text-white/60">
              Try adjusting your search terms or category filter.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Selection Summary */}
      <Card className="bg-blue-500/10 border-blue-400/20">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <Info className="w-5 h-5 text-blue-400 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-white font-medium mb-1">Selection Summary</h4>
              <p className="text-white/70 text-sm">
                {selectedPolicies.length} policies selected 
                ({selectedMandatory} mandatory, {selectedPolicies.length - selectedMandatory} optional)
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
