
'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip } from 'recharts'

interface ComplianceMetricsProps {
  timeRange: string
}

const complianceData = [
  { name: 'Compliant', value: 142, color: '#10B981' },
  { name: 'Non-Compliant', value: 8, color: '#EF4444' },
  { name: 'Under Review', value: 6, color: '#F59E0B' }
]

const policyComplianceData = [
  { policy: 'NEPA', compliance: 96, total: 125 },
  { policy: 'NHPA', compliance: 89, total: 98 },
  { policy: 'ESA', compliance: 94, total: 87 },
  { policy: 'CWA', compliance: 91, total: 76 },
  { policy: 'Stafford Act', compliance: 98, total: 156 }
]

export function ComplianceMetrics({ timeRange }: ComplianceMetricsProps) {
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 1000)
    return () => clearTimeout(timer)
  }, [timeRange])

  if (isLoading) {
    return (
      <Card className="glass border-white/20">
        <CardHeader>
          <CardTitle className="text-white">Compliance Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="animate-pulse text-white/60">Loading metrics...</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="glass border-white/20">
      <CardHeader>
        <CardTitle className="text-white">Compliance Metrics</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Overall Compliance Pie Chart */}
          <div>
            <h3 className="text-white font-medium mb-4">Overall Compliance Status</h3>
            <div className="h-48">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={complianceData}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={80}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {complianceData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip 
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,0.8)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px',
                      color: 'white'
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
            <div className="flex justify-center gap-4 mt-4">
              {complianceData.map((item, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div 
                    className="w-3 h-3 rounded-full" 
                    style={{ backgroundColor: item.color }}
                  />
                  <span className="text-white/70 text-sm">{item.name}: {item.value}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Policy Compliance Breakdown */}
          <div>
            <h3 className="text-white font-medium mb-4">Policy Compliance Rates</h3>
            <div className="space-y-3">
              {policyComplianceData.map((policy, index) => {
                const percentage = Math.round((policy.compliance / policy.total) * 100)
                return (
                  <div key={index} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-white/80 text-sm">{policy.policy}</span>
                      <span className="text-white text-sm font-medium">
                        {percentage}% ({policy.compliance}/{policy.total})
                      </span>
                    </div>
                    <div className="w-full bg-white/10 rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
