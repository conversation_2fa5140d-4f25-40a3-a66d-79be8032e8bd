# Policy resolver service for step-aware advice
# Resolves latest applicable policy documents based on event context

from typing import List, Dict, Any, Optional
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class PolicyDocument:
    """Represents a policy document with version and applicability"""
    
    def __init__(self, doc_id: str, version: str, effective_date: str, 
                 sections: Dict[str, str], url: str):
        self.doc_id = doc_id
        self.version = version
        self.effective_date = datetime.fromisoformat(effective_date)
        self.sections = sections
        self.url = url
    
    def get_section_url(self, section: str) -> str:
        """Get URL for specific section"""
        if section in self.sections:
            return f"{self.url}#{self.sections[section]}"
        return self.url

class PolicyResolver:
    """Resolves latest applicable policy documents for compliance advice"""
    
    def __init__(self):
        # Mock policy database - replace with real document pipeline integration
        self.policy_db = {
            "fema-pappg": [
                PolicyDocument(
                    doc_id="fema-pappg",
                    version="v2025",
                    effective_date="2025-01-01",
                    sections={
                        "2.1": "applicant-eligibility",
                        "3.1": "facility-eligibility", 
                        "4.1": "work-eligibility",
                        "4.2.1": "damage-inventory",
                        "5.1": "permanent-work",
                        "6.3": "codes-standards",
                        "7.1": "mitigation",
                        "8.1": "documentation",
                        "8.2": "insurance",
                        "8.4": "procurement"
                    },
                    url="https://www.fema.gov/assistance/public/applicant/policy-guidance"
                ),
                PolicyDocument(
                    doc_id="fema-pappg",
                    version="v4",
                    effective_date="2020-06-01",
                    sections={
                        "2.1": "applicant-eligibility",
                        "3.1": "facility-eligibility",
                        "4.1": "work-eligibility"
                    },
                    url="https://www.fema.gov/assistance/public/applicant/policy-guidance-v4"
                )
            ],
            "cfr-206": [
                PolicyDocument(
                    doc_id="cfr-206",
                    version="2024",
                    effective_date="2024-01-01",
                    sections={
                        "206.221": "applicant-eligibility",
                        "206.223": "emergency-work",
                        "206.226": "permanent-work"
                    },
                    url="https://www.ecfr.gov/current/title-44/chapter-I/subchapter-D/part-206"
                )
            ],
            "cfr-200": [
                PolicyDocument(
                    doc_id="cfr-200",
                    version="2024", 
                    effective_date="2024-01-01",
                    sections={
                        "200.318": "procurement-general",
                        "200.319": "competition",
                        "200.320": "procurement-methods"
                    },
                    url="https://www.ecfr.gov/current/title-2/subtitle-A/chapter-II/part-200"
                )
            ]
        }
    
    def resolve_latest_applicable(self, event_year: int, disaster_code: str) -> List[Dict[str, str]]:
        """
        Resolve latest applicable policy documents for given event context
        
        Args:
            event_year: Year of the disaster event
            disaster_code: FEMA disaster declaration number
            
        Returns:
            List of policy references with doc_id, version, section, url
        """
        try:
            # For now, return latest versions of key documents
            # In real implementation, this would query the document pipeline
            # to find versions applicable at the time of the disaster
            
            policy_refs = []
            
            # Get latest PAPPG
            pappg_docs = self.policy_db.get("fema-pappg", [])
            if pappg_docs:
                latest_pappg = max(pappg_docs, key=lambda d: d.effective_date)
                policy_refs.append({
                    "doc_id": latest_pappg.doc_id,
                    "version": latest_pappg.version,
                    "section": "latest",
                    "url": latest_pappg.url
                })
            
            # Get applicable CFR sections
            cfr_206_docs = self.policy_db.get("cfr-206", [])
            if cfr_206_docs:
                latest_cfr = max(cfr_206_docs, key=lambda d: d.effective_date)
                policy_refs.append({
                    "doc_id": latest_cfr.doc_id,
                    "version": latest_cfr.version,
                    "section": "applicable",
                    "url": latest_cfr.url
                })
            
            logger.info(f"Resolved {len(policy_refs)} policy references for {disaster_code}")
            return policy_refs
            
        except Exception as e:
            logger.error(f"Error resolving policies for {disaster_code}: {str(e)}")
            # Return fallback references
            return [
                {
                    "doc_id": "fema-pappg",
                    "version": "latest",
                    "section": "applicable",
                    "url": "https://www.fema.gov/assistance/public/applicant/policy-guidance"
                }
            ]
    
    def get_section_reference(self, doc_id: str, section: str, event_year: int = None) -> Optional[Dict[str, str]]:
        """
        Get specific section reference for a document
        
        Args:
            doc_id: Document identifier (e.g., 'fema-pappg')
            section: Section identifier (e.g., '4.2.1')
            event_year: Year for version resolution
            
        Returns:
            Policy reference dict or None if not found
        """
        try:
            docs = self.policy_db.get(doc_id, [])
            if not docs:
                return None
            
            # Get latest or event-appropriate version
            if event_year:
                # Filter to versions effective before/during event year
                applicable_docs = [d for d in docs if d.effective_date.year <= event_year]
                if applicable_docs:
                    doc = max(applicable_docs, key=lambda d: d.effective_date)
                else:
                    doc = docs[0]  # Fallback to oldest
            else:
                doc = max(docs, key=lambda d: d.effective_date)  # Latest
            
            return {
                "doc_id": doc.doc_id,
                "version": doc.version,
                "section": section,
                "url": doc.get_section_url(section)
            }
            
        except Exception as e:
            logger.error(f"Error getting section reference {doc_id}§{section}: {str(e)}")
            return None
    
    def search_policies(self, query: str, event_year: int = None) -> List[Dict[str, str]]:
        """
        Search policy documents for relevant sections
        
        Args:
            query: Search query
            event_year: Year for version filtering
            
        Returns:
            List of matching policy references
        """
        # Stub implementation - in real system would use document pipeline search
        results = []
        
        # Simple keyword matching for demo
        query_lower = query.lower()
        
        for doc_id, docs in self.policy_db.items():
            for doc in docs:
                if event_year and doc.effective_date.year > event_year:
                    continue
                    
                # Check if query matches document or sections
                if (query_lower in doc_id.lower() or 
                    any(query_lower in section.lower() for section in doc.sections.keys())):
                    
                    results.append({
                        "doc_id": doc.doc_id,
                        "version": doc.version,
                        "section": "search-result",
                        "url": doc.url
                    })
        
        return results[:5]  # Limit results
    
    def get_document_metadata(self, doc_id: str, version: str = None) -> Optional[Dict[str, Any]]:
        """
        Get metadata for a specific document version
        
        Args:
            doc_id: Document identifier
            version: Specific version (latest if None)
            
        Returns:
            Document metadata dict or None
        """
        try:
            docs = self.policy_db.get(doc_id, [])
            if not docs:
                return None
            
            if version:
                doc = next((d for d in docs if d.version == version), None)
            else:
                doc = max(docs, key=lambda d: d.effective_date)
            
            if not doc:
                return None
            
            return {
                "doc_id": doc.doc_id,
                "version": doc.version,
                "effective_date": doc.effective_date.isoformat(),
                "sections": list(doc.sections.keys()),
                "url": doc.url
            }
            
        except Exception as e:
            logger.error(f"Error getting document metadata {doc_id}:{version}: {str(e)}")
            return None

# Global instance
policy_resolver = PolicyResolver()
