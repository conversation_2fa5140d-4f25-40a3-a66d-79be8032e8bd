---
description: Repository Information Overview
alwaysApply: true
---

# ComplianceMax Information

## Summary
ComplianceMax is a FEMA Public Assistance compliance system designed to streamline disaster recovery documentation and ensure compliance with FEMA regulations. The system includes various wizards, forms, and tools to help users navigate the complex requirements of FEMA's Public Assistance program.

## Structure
- **landing_page.html**: Main entry point
- **costs/**: Contains emergency work and cost-related pages
- **docs/**: Documentation, audit reports, and reference materials
- **js/**: JavaScript libraries and utilities
- **partials/**: Reusable HTML components
- **pods/**: Pod configuration and registry
- **scripts/**: Utility scripts for deployment and maintenance
- **ui/**: CSS and UI components
- **.github/workflows/**: GitHub Pages deployment configuration

## Language & Runtime
**Language**: JavaScript, HTML, CSS
**Build System**: Static site with GitHub Pages deployment
**Package Manager**: None specified

## Dependencies
No formal dependency management system is used. The project relies on:
- Native JavaScript
- HTML/CSS
- GitHub Pages for deployment

## Build & Installation
```bash
# Run locally with Python HTTP server
python -m http.server 8080

# Access at
http://localhost:8080/landing_page.html
```

## GitHub Pages Deployment
**Workflow**: .github/workflows/pages.yml
**Configuration**: 
- Runs linkcheck and podcheck scripts
- Adds version stamping
- Deploys from repository root
- Uses GitHub Pages environment

## Main Components

### Navigation System
**Files**: nav.js, footer.js, partials/nav.html, partials/footer.html
**Features**: Base-relative path support, consistent navigation across pages

### Pod System
**Files**: pods/registry.json, js/cmx_pod_guard.min.js
**Features**: Runtime guards for pod components, pod registry for requirements

### Wizard System
**Files**: earthquake_wizard.html, js/cmx_wizard_guard.min.js
**Features**: Interactive wizards for compliance workflows, runtime guards

### Document Management
**Files**: js/document_pod_router.js
**Features**: Document upload and management with pod guards

## Deployment Stabilization
The repository includes several features to ensure stable GitHub Pages deployment:
- Pod guards for runtime checks of required elements
- Wizard guards for runtime checks of wizard components
- CI gates (linkcheck.js, podcheck.js) to prevent broken deployments
- Base path normalization for consistent navigation
- Version stamping with build timestamp and commit SHA