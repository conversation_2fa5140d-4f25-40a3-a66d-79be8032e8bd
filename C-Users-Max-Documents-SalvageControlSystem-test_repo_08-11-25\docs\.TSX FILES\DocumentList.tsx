
'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  FileText, 
  Image, 
  File, 
  Download, 
  Trash2, 
  Eye,
  CheckCircle,
  Clock,
  AlertTriangle
} from 'lucide-react'
import { formatFileSize, formatDate } from '@/lib/utils'

interface Document {
  id: string
  name: string
  type: string
  size: number
  uploadedAt: Date
  category: string
  status: 'processing' | 'completed' | 'error'
  tags: string[]
  description?: string
}

interface DocumentListProps {
  documents: Document[]
  selectedDocuments: Set<string>
  onSelectDocument: (id: string) => void
  onSelectAll: () => void
  onDeleteDocument: (id: string) => void
}

export function DocumentList({ 
  documents, 
  selectedDocuments, 
  onSelectDocument, 
  onSelectAll, 
  onDeleteDocument 
}: DocumentListProps) {
  const getFileIcon = (type: string) => {
    if (type.includes('pdf')) return FileText
    if (type.includes('image')) return Image
    if (type.includes('word') || type.includes('document')) return FileText
    return File
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-400" />
      case 'processing':
        return <Clock className="w-4 h-4 text-yellow-400" />
      case 'error':
        return <AlertTriangle className="w-4 h-4 text-red-400" />
      default:
        return null
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Completed'
      case 'processing':
        return 'Processing'
      case 'error':
        return 'Error'
      default:
        return 'Unknown'
    }
  }

  if (documents.length === 0) {
    return (
      <Card className="glass border-white/20">
        <CardContent className="p-12 text-center">
          <FileText className="w-16 h-16 text-white/40 mx-auto mb-4" />
          <h3 className="text-white font-medium mb-2">No documents found</h3>
          <p className="text-white/60">
            Upload documents or adjust your search filters to see results.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="glass border-white/20">
      <CardContent className="p-0">
        {/* Header */}
        <div className="p-4 border-b border-white/20">
          <div className="flex items-center gap-3">
            <input
              type="checkbox"
              checked={selectedDocuments.size === documents.length && documents.length > 0}
              onChange={onSelectAll}
              className="w-4 h-4 rounded border-white/20 bg-white/10 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-white/70 text-sm">
              Select all ({documents.length} documents)
            </span>
          </div>
        </div>

        {/* Document List */}
        <div className="divide-y divide-white/10">
          {documents.map((document) => {
            const Icon = getFileIcon(document.type)
            const isSelected = selectedDocuments.has(document.id)

            return (
              <div
                key={document.id}
                className={`p-4 hover:bg-white/5 transition-colors cursor-pointer ${
                  isSelected ? 'bg-blue-500/10' : ''
                }`}
                onClick={() => onSelectDocument(document.id)}
              >
                <div className="flex items-center gap-4">
                  {/* Checkbox */}
                  <input
                    type="checkbox"
                    checked={isSelected}
                    onChange={(e) => {
                      e.stopPropagation()
                      onSelectDocument(document.id)
                    }}
                    className="w-4 h-4 rounded border-white/20 bg-white/10 text-blue-600 focus:ring-blue-500"
                  />

                  {/* File Icon */}
                  <div className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Icon className="w-5 h-5 text-white/70" />
                  </div>

                  {/* File Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between gap-4">
                      <div className="min-w-0">
                        <h3 className="text-white font-medium truncate">
                          {document.name}
                        </h3>
                        <div className="flex items-center gap-4 mt-1">
                          <span className="text-white/60 text-sm">
                            {formatFileSize(document.size)}
                          </span>
                          <span className="text-white/60 text-sm">
                            {formatDate(document.uploadedAt)}
                          </span>
                          <span className="px-2 py-1 bg-white/10 text-white/80 text-xs rounded-full">
                            {document.category}
                          </span>
                        </div>
                        {document.description && (
                          <p className="text-white/60 text-sm mt-1 line-clamp-1">
                            {document.description}
                          </p>
                        )}
                        {document.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {document.tags.map((tag, index) => (
                              <span
                                key={index}
                                className="px-1.5 py-0.5 bg-blue-500/20 text-blue-400 text-xs rounded"
                              >
                                {tag}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>

                      {/* Status and Actions */}
                      <div className="flex items-center gap-3 flex-shrink-0">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(document.status)}
                          <span className="text-white/70 text-sm">
                            {getStatusText(document.status)}
                          </span>
                        </div>

                        <div className="flex gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              // Handle view
                            }}
                            className="text-white/60 hover:text-white hover:bg-white/10"
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              // Handle download
                            }}
                            className="text-white/60 hover:text-white hover:bg-white/10"
                          >
                            <Download className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              onDeleteDocument(document.id)
                            }}
                            className="text-red-400/60 hover:text-red-400 hover:bg-red-400/10"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
