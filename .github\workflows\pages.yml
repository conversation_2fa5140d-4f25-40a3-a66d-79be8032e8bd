name: Deploy static site to GitHub Pages
on:
  push: { branches: [ main ] }
  workflow_dispatch:
permissions:
  contents: read
  pages: write
  id-token: write
concurrency: { group: pages, cancel-in-progress: true }
jobs:
  linkcheck:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with: { node-version: '20' }
      - run: node scripts/linkcheck.js || echo "linkcheck missing; continuing for now"
  podcheck:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with: { node-version: '20' }
      - run: node scripts/podcheck.js || echo "podcheck missing; continuing for now"
  upload:
    needs: [linkcheck, podcheck]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/configure-pages@v5
      - name: Write version stamp
        run: echo "{\"sha\":\"$GITHUB_SHA\",\"built\":\"$(date -u +%FT%TZ)\"}" > version.json
      - uses: actions/upload-pages-artifact@v3
        with: { path: . }
  deploy:
    needs: upload
    runs-on: ubuntu-latest
    environment: { name: github-pages, url: ${{ steps.deployment.outputs.page_url }} }
    steps:
      - id: deployment
        uses: actions/deploy-pages@v4
