
'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { FileText, Upload, CheckCircle, Clock } from 'lucide-react'

interface DocumentAnalyticsProps {
  timeRange: string
}

const documentStats = [
  { type: 'PDF', count: 456, percentage: 36.6 },
  { type: 'Word', count: 298, percentage: 23.9 },
  { type: 'Excel', count: 187, percentage: 15.0 },
  { type: 'Images', count: 234, percentage: 18.8 },
  { type: 'Other', count: 72, percentage: 5.7 }
]

const processingStats = {
  totalUploaded: 1247,
  processed: 1189,
  processing: 42,
  failed: 16,
  avgProcessingTime: '2.3 minutes'
}

export function DocumentAnalytics({ timeRange }: DocumentAnalyticsProps) {
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 800)
    return () => clearTimeout(timer)
  }, [timeRange])

  if (isLoading) {
    return (
      <Card className="glass border-white/20">
        <CardHeader>
          <CardTitle className="text-white">Document Analytics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="animate-pulse text-white/60">Loading document data...</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="glass border-white/20">
      <CardHeader>
        <CardTitle className="text-white">Document Analytics</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Processing Status */}
          <div>
            <h3 className="text-white font-medium mb-4">Processing Status</h3>
            <div className="grid grid-cols-2 gap-3">
              <div className="p-3 bg-white/5 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-white/80 text-sm">Processed</span>
                </div>
                <p className="text-white font-bold text-lg">{processingStats.processed}</p>
              </div>
              
              <div className="p-3 bg-white/5 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <Clock className="w-4 h-4 text-yellow-400" />
                  <span className="text-white/80 text-sm">Processing</span>
                </div>
                <p className="text-white font-bold text-lg">{processingStats.processing}</p>
              </div>
              
              <div className="p-3 bg-white/5 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <Upload className="w-4 h-4 text-blue-400" />
                  <span className="text-white/80 text-sm">Total</span>
                </div>
                <p className="text-white font-bold text-lg">{processingStats.totalUploaded}</p>
              </div>
              
              <div className="p-3 bg-white/5 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <span className="w-4 h-4 bg-red-400 rounded-full" />
                  <span className="text-white/80 text-sm">Failed</span>
                </div>
                <p className="text-white font-bold text-lg">{processingStats.failed}</p>
              </div>
            </div>
            
            <div className="mt-4 p-3 bg-blue-500/10 rounded-lg">
              <p className="text-blue-400 text-sm">
                Average processing time: {processingStats.avgProcessingTime}
              </p>
            </div>
          </div>

          {/* Document Types */}
          <div>
            <h3 className="text-white font-medium mb-4">Document Types</h3>
            <div className="space-y-3">
              {documentStats.map((stat, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-white/80 text-sm">{stat.type}</span>
                    <span className="text-white text-sm font-medium">
                      {stat.count} ({stat.percentage}%)
                    </span>
                  </div>
                  <div className="w-full bg-white/10 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${stat.percentage}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 gap-3">
            <div className="p-3 bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg border border-green-400/20">
              <div className="flex items-center gap-2">
                <FileText className="w-4 h-4 text-green-400" />
                <span className="text-white/80 text-sm">Success Rate</span>
              </div>
              <p className="text-white font-bold text-lg mt-1">
                {Math.round((processingStats.processed / processingStats.totalUploaded) * 100)}%
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
