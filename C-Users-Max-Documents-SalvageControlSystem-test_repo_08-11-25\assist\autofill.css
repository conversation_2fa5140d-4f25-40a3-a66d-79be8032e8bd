.ap-fab{
  position:fixed;
  right:18px;
  bottom:180px;
  z-index:1000;
  background:#2563eb;
  color:#fff;
  border:none;
  border-radius:999px;
  padding:10px 14px;
  font:600 13px system-ui;
  box-shadow:0 6px 16px rgba(0,0,0,.2);
  cursor:pointer;
  transition:all 0.3s ease;
}

.ap-fab:hover{
  background:#1d4ed8;
  transform:translateY(-2px);
  box-shadow:0 8px 20px rgba(0,0,0,.3);
}

.ap-panel{
  position:fixed;
  right:18px;
  bottom:240px;
  width:360px;
  max-height:65vh;
  overflow:auto;
  background:#0f172a;
  color:#fff;
  border-radius:12px;
  box-shadow:0 10px 30px rgba(0,0,0,.3);
  border:1px solid #1e293b;
}

.ap-h{
  display:flex;
  align-items:center;
  justify-content:space-between;
  padding:12px 16px;
  border-bottom:1px solid #1f2937;
  background:#1e293b;
  border-radius:12px 12px 0 0;
}

.ap-h b{
  font:600 14px system-ui;
  color:#f1f5f9;
}

.ap-x{
  background:transparent;
  border:0;
  color:#94a3b8;
  cursor:pointer;
  font-size:18px;
  padding:4px;
  border-radius:4px;
  transition:all 0.2s ease;
}

.ap-x:hover{
  background:#374151;
  color:#f1f5f9;
}

.ap-b{
  padding:16px;
  font:13px/1.5 system-ui;
}

.ap-b .conf{
  font-size:12px;
  color:#9ca3af;
  margin-bottom:12px;
  padding:6px 10px;
  background:#111827;
  border-radius:6px;
  border:1px solid #1f2937;
}

.ap-b .assump{
  background:#111827;
  border:1px solid #1f2937;
  border-radius:8px;
  padding:12px;
  margin-top:12px;
  line-height:1.4;
}

.ap-b .assump b{
  color:#60a5fa;
  font-weight:600;
}

.ap-b .assump ul{
  margin:8px 0 0 16px;
  color:#d1d5db;
}

.ap-b .assump li{
  margin:4px 0;
}

.ap-btns{
  display:flex;
  gap:8px;
  margin-top:16px;
}

.ap-btns button{
  flex:1;
  border:0;
  border-radius:8px;
  padding:10px 12px;
  font:600 13px system-ui;
  cursor:pointer;
  transition:all 0.2s ease;
}

.ap-apply{
  background:#16a34a;
  color:#fff;
}

.ap-apply:hover{
  background:#15803d;
  transform:translateY(-1px);
}

.ap-undo{
  background:#374151;
  color:#fff;
}

.ap-undo:hover{
  background:#4b5563;
  transform:translateY(-1px);
}

/* Mobile responsive */
@media (max-width: 480px) {
  .ap-panel{
    width:calc(100vw - 32px);
    right:18px;
    bottom:240px;
  }

  .ap-fab{
    right:18px;
    bottom:180px;
  }
}

/* Print hide */
@media print{
  .ap-fab,.ap-panel{
    display:none!important;
  }
}

/* Confidence indicator colors */
.ap-b .conf.high{
  background:#064e3b;
  border-color:#059669;
  color:#6ee7b7;
}

.ap-b .conf.medium{
  background:#451a03;
  border-color:#d97706;
  color:#fbbf24;
}

.ap-b .conf.low{
  background:#450a0a;
  border-color:#dc2626;
  color:#fca5a5;
}
