
/**
 * ComplianceMax Dashboard Component
 * Main dashboard interface for FEMA PA compliance management
 */

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, CardTitle, CardContent } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Progress } from './ui/progress';
import { 
  FileText, 
  CheckCircle, 
  AlertTriangle, 
  Clock, 
  Upload,
  BarChart3,
  Users,
  DollarSign
} from 'lucide-react';

const Dashboard = () => {
  const [dashboardData, setDashboardData] = useState({
    summary: {
      totalProjects: 0,
      activeProjects: 0,
      completedProjects: 0,
      totalValue: 0
    },
    recentActivity: [],
    complianceStatus: {
      compliant: 0,
      pending: 0,
      nonCompliant: 0
    },
    upcomingDeadlines: []
  });

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      // Simulate API calls - replace with actual API endpoints
      const mockData = {
        summary: {
          totalProjects: 24,
          activeProjects: 8,
          completedProjects: 16,
          totalValue: 12500000
        },
        recentActivity: [
          {
            id: 1,
            type: 'document_upload',
            description: 'Damage assessment uploaded for Project PW-001',
            timestamp: '2024-06-21T10:30:00Z',
            status: 'success'
          },
          {
            id: 2,
            type: 'compliance_check',
            description: 'Environmental review completed for Project PW-003',
            timestamp: '2024-06-21T09:15:00Z',
            status: 'warning'
          },
          {
            id: 3,
            type: 'intake_submitted',
            description: 'New intake request submitted - Emergency Debris Removal',
            timestamp: '2024-06-21T08:45:00Z',
            status: 'info'
          }
        ],
        complianceStatus: {
          compliant: 18,
          pending: 4,
          nonCompliant: 2
        },
        upcomingDeadlines: [
          {
            id: 1,
            project: 'PW-001 - Road Repair',
            deadline: '2024-06-25',
            task: 'Environmental Review',
            daysRemaining: 4
          },
          {
            id: 2,
            project: 'PW-005 - Building Restoration',
            deadline: '2024-06-28',
            task: 'Cost Estimate Review',
            daysRemaining: 7
          }
        ]
      };

      setDashboardData(mockData);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getActivityIcon = (type) => {
    switch (type) {
      case 'document_upload':
        return <Upload className="h-4 w-4" />;
      case 'compliance_check':
        return <CheckCircle className="h-4 w-4" />;
      case 'intake_submitted':
        return <FileText className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusBadgeVariant = (status) => {
    switch (status) {
      case 'success':
        return 'default';
      case 'warning':
        return 'secondary';
      case 'error':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">ComplianceMax Dashboard</h1>
          <p className="text-gray-600">FEMA PA Compliance Management System</p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline">
            <FileText className="h-4 w-4 mr-2" />
            New Intake
          </Button>
          <Button>
            <Upload className="h-4 w-4 mr-2" />
            Upload Documents
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Projects</p>
                <p className="text-2xl font-bold text-gray-900">{dashboardData.summary.totalProjects}</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <BarChart3 className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Projects</p>
                <p className="text-2xl font-bold text-gray-900">{dashboardData.summary.activeProjects}</p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-gray-900">{dashboardData.summary.completedProjects}</p>
              </div>
              <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Value</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(dashboardData.summary.totalValue)}
                </p>
              </div>
              <div className="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Compliance Status */}
        <Card>
          <CardHeader>
            <CardTitle>Compliance Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium">Compliant</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">{dashboardData.complianceStatus.compliant}</span>
                  <Progress 
                    value={(dashboardData.complianceStatus.compliant / dashboardData.summary.totalProjects) * 100} 
                    className="w-20"
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm font-medium">Pending Review</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">{dashboardData.complianceStatus.pending}</span>
                  <Progress 
                    value={(dashboardData.complianceStatus.pending / dashboardData.summary.totalProjects) * 100} 
                    className="w-20"
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <span className="text-sm font-medium">Non-Compliant</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">{dashboardData.complianceStatus.nonCompliant}</span>
                  <Progress 
                    value={(dashboardData.complianceStatus.nonCompliant / dashboardData.summary.totalProjects) * 100} 
                    className="w-20"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dashboardData.recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">
                      {activity.description}
                    </p>
                    <p className="text-xs text-gray-500">
                      {new Date(activity.timestamp).toLocaleString()}
                    </p>
                  </div>
                  <Badge variant={getStatusBadgeVariant(activity.status)}>
                    {activity.status}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Upcoming Deadlines */}
      <Card>
        <CardHeader>
          <CardTitle>Upcoming Deadlines</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {dashboardData.upcomingDeadlines.map((deadline) => (
              <div key={deadline.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <p className="font-medium text-gray-900">{deadline.project}</p>
                  <p className="text-sm text-gray-600">{deadline.task}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">
                    {new Date(deadline.deadline).toLocaleDateString()}
                  </p>
                  <Badge variant={deadline.daysRemaining <= 3 ? 'destructive' : 'secondary'}>
                    {deadline.daysRemaining} days
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Dashboard;
