# Cost Estimation Methodology

## Data Sources

This cost estimation system uses only verified, real data sources from the repository:

### Equipment Rates
- **Primary Source**: `costs/fema_schedule-of-equipment-rates_2025.csv`
- **Backup Sources**: 
  - `costs/fema_schedule-of-equipment-rates_2023.txt`
  - `costs/fema_schedule-of-equipment-rates_2021.txt`
- **Content**: Official FEMA Schedule of Equipment Rates with 700+ equipment items
- **Format**: Cost code, equipment description, manufacturer, capacity, hourly rate
- **Usage**: Direct lookup by equipment code or search by description

### Engineering Cost Factors
- **Source**: `costs/public-assistance-cost-estimating-tool-for-engineering-and-design-services_12-21-2015.txt`
- **Content**: Engineering and design service percentage curves
- **Usage**: Calculate engineering costs as percentage of construction costs based on project size

### Project Structure Reference
- **Source**: `costs/public-assistance-cost-estimating-tool_spreadsheet_12-5-2012.xlsx`
- **Content**: FEMA project worksheet structure and cost categories
- **Usage**: Organize costs into proper FEMA categories (A-G)

## Cost Calculation Flow

### 1. Equipment Costs (Real Data)
```javascript
// Load FEMA equipment rates
const engine = new FEMAEquipmentEngine();
await engine.loadRates();

// Calculate equipment cost
const cost = engine.calculateEquipmentCost(
    '8010',  // FEMA equipment code
    40,      // hours
    2        // quantity
);
// Returns: rate per hour × hours × quantity
```

### 2. Labor Costs (User-Provided)
- **Requirement**: User must provide actual labor rates in `labor_tables.json`
- **Sources**: Davis-Bacon wages, local union rates, market surveys
- **Calculation**: Rate per hour × hours × crew size × overhead factors
- **Validation**: System blocks export if labor tables are empty

### 3. Material Costs (User-Provided)
- **Requirement**: User must provide actual material prices in `material_prices.json`
- **Sources**: Local suppliers, RSMeans, recent bids
- **Calculation**: Unit price × quantity × waste factor
- **Validation**: System blocks export if material tables are empty

### 4. Engineering Costs (Real Data)
- **Source**: FEMA engineering cost curves
- **Calculation**: Construction cost × engineering percentage (varies by project size)
- **Range**: 8-15% for typical projects, higher for complex projects

## Safety Gates

### Data Validation
- **Equipment**: Verified against FEMA official rates
- **Labor**: Must be user-provided (no fake data)
- **Materials**: Must be user-provided (no fake data)
- **Engineering**: Calculated from FEMA curves

### Sanity Checks
- **Quantity Validation**: Flag if quantities seem unreasonable
- **Cost per SF**: Compare against typical ranges
- **Labor/Equipment Ratio**: Flag unusual ratios
- **Total Project Cost**: Require confirmation for very high/low totals

### Export Blocking
- System prevents export if:
  - Labor tables contain only placeholder data
  - Material tables contain only placeholder data
  - Quantities are missing or zero
  - User hasn't confirmed sanity check warnings

## File Versions and Updates

### Equipment Rates
- **2025**: Primary source (most current)
- **2023**: Backup for comparison
- **2021**: Historical reference
- **Update Frequency**: Annually by FEMA

### Engineering Factors
- **2015**: Current methodology (still valid)
- **Update Frequency**: Infrequent (methodology stable)

### Project Structure
- **2012**: Reference structure (still applicable)
- **Update Frequency**: Follows FEMA form updates

## Quality Assurance

### Data Integrity
- All equipment rates verified against FEMA official publications
- No fabricated or estimated data used
- Clear separation between real data and user-provided data

### Calculation Accuracy
- Equipment costs: Direct multiplication (rate × hours × quantity)
- Engineering costs: Interpolated from FEMA curves
- Total costs: Sum of verified components only

### User Responsibility
- Labor rates: User must provide current, location-specific rates
- Material prices: User must provide current, local prices
- Overhead factors: User must specify actual project overhead

## Compliance

### FEMA Requirements
- Equipment rates comply with FEMA Schedule of Equipment Rates
- Engineering percentages follow FEMA cost estimating guidelines
- Project structure matches FEMA worksheet format

### Audit Trail
- All calculations traceable to source data
- Equipment codes linked to FEMA official rates
- User-provided data clearly identified
- Methodology documented and version-controlled

## Limitations

### What This System Provides
- ✅ Real FEMA equipment rates
- ✅ FEMA engineering cost factors
- ✅ Proper cost calculation methodology
- ✅ Data validation and sanity checks

### What Users Must Provide
- ❗ Current labor rates for their location
- ❗ Current material prices for their location
- ❗ Project-specific overhead factors
- ❗ Accurate quantity takeoffs

### Not Included
- ❌ Fabricated labor rates
- ❌ Estimated material prices
- ❌ Generic overhead percentages
- ❌ Quantity estimation (user responsibility)
