name: UI guard

on:
  push:
    branches: [ main, stabilize/* ]
  pull_request:

jobs:
  scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Block inline/override positioning on nav/sidewalk (ignores comments)
        shell: bash
        run: |
          set -euo pipefail
          banned='(#universal-nav|\\.sidewalk[^\\s{]*)[^{]*\\{[^}]*(position\\s*:|top\\s*:|z-index\\s*:)[^}]*\\}'
          found=0
          while IFS= read -r -d '' f; do
            body="$(sed -E ':a;N;$!ba;s/<!--(.|\n)*?-->//g' "$f")"
            if echo "$body" | grep -nE "$banned" >/dev/null; then
              echo "::error file=$f::for nav/sidewalk, position/top/z-index must be defined only in ui/stack.css"
              echo "$body" | grep -nE "$banned" || true
              found=1
            fi
          done < <(find . -type f -name '*.html' -print0)
          test "$found" -eq 0
