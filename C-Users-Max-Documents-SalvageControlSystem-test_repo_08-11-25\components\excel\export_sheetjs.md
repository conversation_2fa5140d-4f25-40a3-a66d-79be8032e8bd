# Excel Export Implementation Guide

## Overview

This document outlines the approach for implementing Excel (.xlsx) export functionality using SheetJS, with strict guards to prevent export of estimates containing fabricated cost data.

## Architecture

### Core Components

1. **Export Guard**: Validates all cost data before allowing export
2. **SheetJS Integration**: Handles .xlsx file generation
3. **FEMA Worksheet Structure**: Matches official FEMA Form 90-91 format
4. **Data Validation**: Ensures only real cost data is exported

### Export Guard Requirements

The export system MUST block generation of .xlsx files until:

- ✅ Labor rates provided in `components/cost/labor_tables.json`
- ✅ Material prices provided in `components/cost/material_prices.json`
- ✅ Equipment rates loaded from FEMA official data
- ✅ All cost calculations validated
- ✅ User confirms any validation warnings

## Implementation Phases

### Phase 1: Guard Implementation (Current)
```javascript
class ExcelExportGuard {
    async canExport(costData) {
        // Check for placeholder data
        if (this.hasPlaceholderData()) {
            throw new Error('Cannot export: Unit cost libraries not provided');
        }
        
        // Validate cost data
        const validation = await this.validateCosts(costData);
        if (!validation.valid) {
            throw new Error('Cannot export: Cost validation failed');
        }
        
        return true;
    }
}
```

### Phase 2: SheetJS Integration (Future PR)
```javascript
import * as XLSX from 'xlsx';

class FEMAWorksheetExporter {
    generateWorkbook(costData) {
        // Create workbook with FEMA structure
        const wb = XLSX.utils.book_new();
        
        // Add worksheets
        this.addProjectInfoSheet(wb, costData);
        this.addLaborCostsSheet(wb, costData);
        this.addMaterialCostsSheet(wb, costData);
        this.addEquipmentCostsSheet(wb, costData);
        this.addOtherCostsSheet(wb, costData);
        this.addSummarySheet(wb, costData);
        
        return wb;
    }
}
```

### Phase 3: FEMA Worksheet Structure
Based on `costs/public-assistance-cost-estimating-tool_spreadsheet_12-5-2012.xlsx`:

1. **Project Information Sheet**
   - Applicant details
   - Project description
   - Damage assessment
   - Work category classification

2. **Labor Costs Sheet**
   - Trade classifications
   - Hourly rates (user-provided)
   - Hours worked
   - Overhead factors
   - Total labor costs

3. **Material Costs Sheet**
   - Material descriptions
   - Unit prices (user-provided)
   - Quantities
   - Waste factors
   - Total material costs

4. **Equipment Costs Sheet**
   - FEMA equipment codes
   - Official FEMA rates
   - Hours of operation
   - Total equipment costs

5. **Other Costs Sheet**
   - Engineering costs (FEMA percentage curves)
   - Permits and fees
   - Contingencies
   - Total other costs

6. **Summary Sheet**
   - Cost category totals
   - Grand total
   - Cost per unit metrics
   - Validation status

## Data Sources

### Real Data (Automated)
- **Equipment Rates**: `costs/fema_schedule-of-equipment-rates_2025.csv`
- **Engineering Factors**: `costs/public-assistance-cost-estimating-tool-for-engineering-and-design-services_12-21-2015.txt`

### User-Provided Data (Required)
- **Labor Rates**: Must be provided in `components/cost/labor_tables.json`
- **Material Prices**: Must be provided in `components/cost/material_prices.json`

### Validation Rules
- No placeholder data allowed in export
- All rates must be current and location-specific
- Quantities must be reasonable
- Cost ratios must pass sanity checks

## Export Blocking Logic

```javascript
async function attemptExport(costData) {
    try {
        // Guard check
        const guard = new ExcelExportGuard();
        await guard.canExport(costData);
        
        // Generate workbook
        const exporter = new FEMAWorksheetExporter();
        const workbook = exporter.generateWorkbook(costData);
        
        // Download file
        XLSX.writeFile(workbook, 'FEMA_Cost_Estimate.xlsx');
        
    } catch (error) {
        // Show blocking message
        alert(`Export blocked: ${error.message}\n\nPlease provide required cost data before exporting.`);
    }
}
```

## File Structure

```
components/excel/
├── export_sheetjs.md          # This documentation
├── export_guard.js            # Export validation and blocking
├── fema_worksheet_exporter.js # SheetJS-based workbook generation
├── worksheet_templates.js     # FEMA worksheet structure definitions
└── validation_rules.js        # Cost validation rules
```

## Dependencies

### Required for Full Implementation
- `xlsx` (SheetJS): Excel file generation
- `file-saver`: Client-side file download

### Installation (Future)
```bash
npm install xlsx file-saver
```

## Testing Strategy

### Unit Tests
- Export guard validation
- Worksheet structure generation
- Data transformation accuracy

### Integration Tests
- End-to-end export flow
- File format validation
- FEMA worksheet compliance

### Manual Tests
- Open generated .xlsx in Excel
- Verify FEMA worksheet structure
- Validate cost calculations
- Test with various cost scenarios

## Security Considerations

### Data Validation
- All user inputs sanitized
- No script injection in Excel cells
- File size limits enforced

### Export Restrictions
- Only validated cost data exported
- No placeholder or fabricated data
- Audit trail maintained

## Future Enhancements

### Advanced Features
- Multiple export formats (PDF, CSV)
- Custom worksheet templates
- Batch export capabilities
- Integration with FEMA systems

### Performance Optimizations
- Streaming for large datasets
- Client-side caching
- Progressive loading

## Compliance

### FEMA Requirements
- Matches official worksheet structure
- Uses approved cost calculation methods
- Maintains audit trail
- Supports required documentation

### Quality Assurance
- All calculations traceable
- Data sources documented
- Validation rules enforced
- Export blocking prevents bad data
