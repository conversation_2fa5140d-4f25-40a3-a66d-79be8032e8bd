
'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  AlertTriangle, 
  Info, 
  AlertCircle, 
  XCircle,
  Clock,
  ExternalLink,
  Settings,
  RefreshCw
} from 'lucide-react';

/**
 * Template component for creating new data source displays
 * Copy this file and customize for your specific data source
 */

// Define your data source specific types
interface YourDataSourceMetadata {
  // Define the structure of your metadata
  customField1: string;
  customField2: number;
  customField3?: boolean;
}

interface YourDataSourceCardProps {
  dataPoint: {
    id: string;
    source: string;
    category: string;
    timestamp: string;
    title: string;
    description: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    metadata: YourDataSourceMetadata;
    raw_data?: any;
  };
  className?: string;
  showMetadata?: boolean;
  showActions?: boolean;
  onViewDetails?: (dataPoint: any) => void;
  onRefresh?: () => void;
  onConfigure?: () => void;
}

// Customize these configurations for your data source
const severityConfig = {
  low: {
    icon: Info,
    color: 'text-blue-500',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    badgeVariant: 'secondary' as const
  },
  medium: {
    icon: AlertCircle,
    color: 'text-yellow-500',
    bgColor: 'bg-yellow-50',
    borderColor: 'border-yellow-200',
    badgeVariant: 'outline' as const
  },
  high: {
    icon: AlertTriangle,
    color: 'text-orange-500',
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200',
    badgeVariant: 'destructive' as const
  },
  critical: {
    icon: XCircle,
    color: 'text-red-500',
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    badgeVariant: 'destructive' as const
  }
};

export const YourDataSourceCard: React.FC<YourDataSourceCardProps> = ({
  dataPoint,
  className = '',
  showMetadata = false,
  showActions = true,
  onViewDetails,
  onRefresh,
  onConfigure
}) => {
  const severityInfo = severityConfig[dataPoint.severity];
  const IconComponent = severityInfo.icon;
  const timestamp = new Date(dataPoint.timestamp);
  const timeAgo = getTimeAgo(timestamp);

  // Custom logic for your data source
  const customValue = dataPoint.metadata.customField2;
  const isCustomCondition = dataPoint.metadata.customField3;

  return (
    <Card className={`${severityInfo.bgColor} ${severityInfo.borderColor} border-l-4 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <IconComponent className={`h-6 w-6 ${severityInfo.color}`} />
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900">
                {dataPoint.title}
              </h3>
              <div className="flex items-center space-x-2 mt-1">
                <Badge variant={severityInfo.badgeVariant} className="text-xs">
                  {dataPoint.severity.toUpperCase()}
                </Badge>
                <span className="text-xs text-gray-600">
                  {dataPoint.source}
                </span>
              </div>
            </div>
          </div>
          
          {/* Custom status indicator */}
          <div className="text-right">
            <div className="text-lg font-bold text-gray-900">
              {customValue}
            </div>
            <div className="text-xs text-gray-500">
              Custom Metric
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <p className="text-gray-700 text-sm mb-3">
          {dataPoint.description}
        </p>
        
        {/* Custom data visualization */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="bg-white bg-opacity-50 rounded-lg p-3">
            <div className="text-sm font-medium text-gray-900">
              {dataPoint.metadata.customField1}
            </div>
            <div className="text-xs text-gray-500">Custom Field 1</div>
          </div>
          
          <div className="bg-white bg-opacity-50 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${isCustomCondition ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-sm font-medium text-gray-900">
                {isCustomCondition ? 'Active' : 'Inactive'}
              </span>
            </div>
            <div className="text-xs text-gray-500">Status</div>
          </div>
        </div>
        
        {/* Custom progress bar or chart */}
        <div className="mb-4">
          <div className="flex justify-between text-xs text-gray-600 mb-1">
            <span>Progress</span>
            <span>{Math.round((customValue / 100) * 100)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full ${severityInfo.color.replace('text-', 'bg-')}`}
              style={{ width: `${Math.min((customValue / 100) * 100, 100)}%` }}
            ></div>
          </div>
        </div>
        
        {/* Metadata display */}
        {showMetadata && (
          <div className="bg-white bg-opacity-50 rounded-lg p-3 mb-3">
            <h4 className="text-xs font-semibold text-gray-600 mb-2">Additional Details</h4>
            <div className="grid grid-cols-1 gap-1">
              {Object.entries(dataPoint.metadata).map(([key, value]) => (
                <div key={key} className="flex justify-between text-xs">
                  <span className="font-medium text-gray-600">{key}:</span>
                  <span className="text-gray-800">
                    {typeof value === 'boolean' ? (value ? 'Yes' : 'No') : String(value)}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Actions */}
        {showActions && (
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-1 text-xs text-gray-500">
              <Clock className="h-3 w-3" />
              <span>{timeAgo}</span>
            </div>
            
            <div className="flex items-center space-x-2">
              {onRefresh && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onRefresh}
                  className="h-6 w-6 p-0"
                >
                  <RefreshCw className="h-3 w-3" />
                </Button>
              )}
              
              {onConfigure && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onConfigure}
                  className="h-6 w-6 p-0"
                >
                  <Settings className="h-3 w-3" />
                </Button>
              )}
              
              {onViewDetails && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onViewDetails(dataPoint)}
                  className="h-6 w-6 p-0"
                >
                  <ExternalLink className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Utility function to calculate time ago
function getTimeAgo(date: Date): string {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (diffInSeconds < 60) {
    return `${diffInSeconds}s ago`;
  } else if (diffInSeconds < 3600) {
    return `${Math.floor(diffInSeconds / 60)}m ago`;
  } else if (diffInSeconds < 86400) {
    return `${Math.floor(diffInSeconds / 3600)}h ago`;
  } else {
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
  }
}

// Export as default
export default YourDataSourceCard;

/**
 * CUSTOMIZATION CHECKLIST:
 * 
 * 1. Rename the component and file to match your data source
 * 2. Update the YourDataSourceMetadata interface with your actual metadata structure
 * 3. Customize the severityConfig if needed (colors, icons, etc.)
 * 4. Replace custom fields (customField1, customField2, etc.) with your actual data fields
 * 5. Update the custom visualization section with your specific charts/displays
 * 6. Modify the progress bar logic to match your data
 * 7. Add any additional actions or buttons specific to your data source
 * 8. Update the styling and colors to match your data source theme
 * 9. Add any data source specific validation or error handling
 * 10. Update the component documentation and prop types
 * 
 * INTEGRATION STEPS:
 * 
 * 1. Import your new component in the dashboard
 * 2. Add a case for your data source category in the rendering logic
 * 3. Test with sample data
 * 4. Add to the component exports in index.ts
 * 5. Write unit tests for your component
 * 6. Update the documentation
 */
