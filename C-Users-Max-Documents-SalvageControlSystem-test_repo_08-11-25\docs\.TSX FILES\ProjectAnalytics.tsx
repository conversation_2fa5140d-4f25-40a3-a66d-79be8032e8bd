
'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts'

interface ProjectAnalyticsProps {
  timeRange: string
}

const projectStatusData = [
  { status: 'Completed', count: 142, color: '#10B981' },
  { status: 'In Progress', count: 8, color: '#3B82F6' },
  { status: 'Under Review', count: 6, color: '#F59E0B' },
  { status: 'On Hold', count: 2, color: '#EF4444' }
]

const monthlyProjectData = [
  { month: 'Jan', completed: 18, started: 22 },
  { month: 'Feb', completed: 22, started: 19 },
  { month: 'Mar', completed: 25, started: 28 },
  { month: 'Apr', completed: 19, started: 21 },
  { month: 'May', completed: 28, started: 24 },
  { month: 'Jun', completed: 30, started: 26 }
]

export function ProjectAnalytics({ timeRange }: ProjectAnalyticsProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [viewMode, setViewMode] = useState<'status' | 'timeline'>('status')

  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 1200)
    return () => clearTimeout(timer)
  }, [timeRange])

  if (isLoading) {
    return (
      <Card className="glass border-white/20">
        <CardHeader>
          <CardTitle className="text-white">Project Analytics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="animate-pulse text-white/60">Loading project data...</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="glass border-white/20">
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="text-white">Project Analytics</CardTitle>
          <div className="flex gap-2">
            <button
              onClick={() => setViewMode('status')}
              className={`px-3 py-1 rounded text-xs ${
                viewMode === 'status' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-white/10 text-white/70 hover:bg-white/20'
              }`}
            >
              Status
            </button>
            <button
              onClick={() => setViewMode('timeline')}
              className={`px-3 py-1 rounded text-xs ${
                viewMode === 'timeline' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-white/10 text-white/70 hover:bg-white/20'
              }`}
            >
              Timeline
            </button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {viewMode === 'status' ? (
          <div className="space-y-4">
            <h3 className="text-white font-medium">Project Status Distribution</h3>
            <div className="h-48">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={projectStatusData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis 
                    dataKey="status" 
                    stroke="rgba(255,255,255,0.7)"
                    fontSize={12}
                  />
                  <YAxis 
                    stroke="rgba(255,255,255,0.7)"
                    fontSize={12}
                  />
                  <Tooltip 
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,0.8)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px',
                      color: 'white'
                    }}
                  />
                  <Bar 
                    dataKey="count" 
                    fill="#3B82F6"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
            <div className="grid grid-cols-2 gap-4">
              {projectStatusData.map((item, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: item.color }}
                    />
                    <span className="text-white/80 text-sm">{item.status}</span>
                  </div>
                  <span className="text-white font-medium">{item.count}</span>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <h3 className="text-white font-medium">Project Timeline</h3>
            <div className="h-48">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={monthlyProjectData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis 
                    dataKey="month" 
                    stroke="rgba(255,255,255,0.7)"
                    fontSize={12}
                  />
                  <YAxis 
                    stroke="rgba(255,255,255,0.7)"
                    fontSize={12}
                  />
                  <Tooltip 
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,0.8)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px',
                      color: 'white'
                    }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="completed" 
                    stroke="#10B981" 
                    strokeWidth={3}
                    dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
                    name="Completed"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="started" 
                    stroke="#3B82F6" 
                    strokeWidth={3}
                    dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
                    name="Started"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
            <div className="flex justify-center gap-6">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full" />
                <span className="text-white/70 text-sm">Completed Projects</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full" />
                <span className="text-white/70 text-sm">Started Projects</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
