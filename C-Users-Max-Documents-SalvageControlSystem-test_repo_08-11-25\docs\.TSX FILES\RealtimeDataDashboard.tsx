
'use client';

import { useState, useCallback, useEffect } from 'react';
import { Card, CardHeader } from '@/components/ui/card';
import { 
  AlertTriangle, 
  Shield, 
  Newspaper, 
  Activity 
} from 'lucide-react';

import { useWebSocket } from './hooks/useWebSocket';
import { ConnectionStatus, DataCategoryCard, AlertsList } from './components';

// Types
interface DataCategory {
  category: 'disaster' | 'compliance' | 'security' | 'news';
  data: any;
  lastUpdate: Date;
  icon: React.ComponentType<any>;
  color: string;
}

interface WebSocketMessage {
  type: string;
  category?: string;
  data: any;
  timestamp: string;
  client_id?: string;
}

interface RealtimeDataDashboardProps {
  clientId?: string;
  channel?: string;
  className?: string;
}

const RealtimeDataDashboard: React.FC<RealtimeDataDashboardProps> = ({
  clientId = 'dashboard-client',
  channel = 'general',
  className = ''
}) => {
  // State management
  const [dataCategories, setDataCategories] = useState<Map<string, DataCategory>>(new Map());
  
  // Initialize data categories after component mounts to avoid hydration issues
  useEffect(() => {
    const now = new Date();
    setDataCategories(new Map([
      ['disaster', {
        category: 'disaster',
        data: null,
        lastUpdate: now,
        icon: AlertTriangle,
        color: 'text-red-500'
      }],
      ['compliance', {
        category: 'compliance',
        data: null,
        lastUpdate: now,
        icon: Shield,
        color: 'text-blue-500'
      }],
      ['security', {
        category: 'security',
        data: null,
        lastUpdate: now,
        icon: Activity,
        color: 'text-yellow-500'
      }],
      ['news', {
        category: 'news',
        data: null,
        lastUpdate: now,
        icon: Newspaper,
        color: 'text-green-500'
      }]
    ]));
  }, []);

  const [alerts, setAlerts] = useState<any[]>([]);
  const [lastConnected, setLastConnected] = useState<Date | undefined>();

  // Constants
  const WS_BASE_URL = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000';
  const wsUrl = `${WS_BASE_URL}/ws/realtime`;

  // WebSocket message handler
  const handleMessage = useCallback((event: MessageEvent) => {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      console.log('Received WebSocket message:', message);

      // Handle different message types
      if (message.type === 'alert') {
        setAlerts(prev => [
          { ...message.data, timestamp: message.timestamp },
          ...prev.slice(0, 19) // Keep last 20 alerts
        ]);
      } else if (message.category && ['disaster', 'compliance', 'security', 'news'].includes(message.category)) {
        setDataCategories(prev => {
          const updated = new Map(prev);
          const categoryData = updated.get(message.category!);
          if (categoryData) {
            updated.set(message.category!, {
              ...categoryData,
              data: message.data,
              lastUpdate: new Date(message.timestamp)
            });
          }
          return updated;
        });
      }
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
    }
  }, []);

  // WebSocket connection handlers
  const handleOpen = useCallback(() => {
    console.log('WebSocket connected to:', wsUrl);
    setLastConnected(new Date());
  }, [wsUrl]);

  const handleClose = useCallback((event: CloseEvent) => {
    console.log('WebSocket disconnected:', event.code, event.reason);
  }, []);

  const handleError = useCallback((error: Event) => {
    console.error('WebSocket error:', error);
  }, []);

  // Use WebSocket hook
  const {
    connect,
    disconnect,
    readyState,
    isConnected,
    isConnecting,
    reconnectAttempts
  } = useWebSocket({
    url: wsUrl,
    onOpen: handleOpen,
    onMessage: handleMessage,
    onClose: handleClose,
    onError: handleError,
    reconnectAttempts: 5,
    reconnectDelay: [1000, 2000, 5000, 10000, 30000]
  });

  // Get connection status
  const getConnectionStatus = () => {
    if (isConnected) return 'connected';
    if (isConnecting) return 'connecting';
    if (reconnectAttempts > 0) return 'reconnecting';
    return 'offline';
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Connection Status Header */}
      <Card>
        <CardHeader>
          <ConnectionStatus
            status={getConnectionStatus()}
            reconnectAttempts={reconnectAttempts}
            maxReconnectAttempts={5}
            lastConnected={lastConnected}
            onConnect={connect}
            onDisconnect={disconnect}
            showControls={true}
          />
        </CardHeader>
      </Card>

      {/* Data Categories Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {Array.from(dataCategories.values()).map((categoryData) => (
          <DataCategoryCard
            key={categoryData.category}
            category={categoryData.category}
            data={categoryData.data}
            lastUpdate={categoryData.lastUpdate}
            icon={categoryData.icon}
            color={categoryData.color}
            isLoading={isConnecting}
          />
        ))}
      </div>

      {/* Live Alerts */}
      <AlertsList
        alerts={alerts}
        maxItems={20}
        className="w-full"
      />
    </div>
  );
};

export default RealtimeDataDashboard;
