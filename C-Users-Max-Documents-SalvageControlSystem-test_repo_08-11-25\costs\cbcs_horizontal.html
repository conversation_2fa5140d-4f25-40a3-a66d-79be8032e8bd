<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CBCS Professional Analysis Service - ComplianceMax V74</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            font-size: 16px;
            color: white;
        }
        
        .container {
            width: 100vw;
            height: 100vh;
            display: grid;
            grid-template-rows: 80px 1fr 60px;
            gap: 0;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            z-index: 100;
        }
        
        .header h1 {
            color: white;
            font-size: 28px;
            font-weight: 700;
        }
        
        .header .subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            margin-left: 15px;
        }
        
        .badges {
            display: flex;
            gap: 15px;
        }
        
        .badge {
            padding: 8px 16px;
            border-radius: 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .badge.policy { background: #e74c3c; }
        .badge.database { background: #27ae60; }
        .badge.cbcs { background: #f39c12; }
        
        .main-content {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 0;
            overflow: hidden;
        }
        
        .column {
            padding: 25px;
            overflow-y: auto;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            height: 100%;
        }
        
        .column:last-child {
            border-right: none;
        }
        
        .column h3 {
            color: white;
            font-size: 20px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #3498db;
            font-weight: 700;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            font-weight: 700;
            margin-bottom: 8px;
            font-size: 15px;
            color: white;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            backdrop-filter: blur(10px);
        }
        
        /* Fix dropdown text visibility - Dark text on white background */
        .form-control option {
            background: white;
            color: #1e293b;
            padding: 8px;
            font-weight: 600;
        }
        
        .form-control option:hover {
            background: #3498db;
            color: white;
        }
        
        /* Ensure select dropdown shows dark text */
        select.form-control {
            color: white;
        }
        
        select.form-control option {
            background: white;
            color: #1e293b;
        }
        
        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        
        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.3);
            background: rgba(255, 255, 255, 0.15);
        }
        
        .auto-populate-btn {
            background: #f39c12;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            font-size: 14px;
            margin-top: 10px;
            transition: background 0.3s;
        }
        
        .auto-populate-btn:hover {
            background: #e67e22;
        }
        
        .cbcs-codes {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            max-height: 400px;
            overflow-y: auto;
            backdrop-filter: blur(10px);
        }
        
        .cbcs-codes h4 {
            margin: 0 0 15px 0;
            font-size: 16px;
            color: white;
            border-bottom: 2px solid rgba(255, 255, 255, 0.3);
            padding-bottom: 8px;
            font-weight: 700;
        }
        
        .code-item {
            display: flex;
            align-items: flex-start;
            padding: 10px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
            border-radius: 6px;
            margin-bottom: 8px;
            background: rgba(255, 255, 255, 0.05);
        }
        
        .code-item:hover {
            background: rgba(52, 152, 219, 0.2);
        }
        
        .code-item.selected {
            background: rgba(39, 174, 96, 0.3);
            border: 2px solid #27ae60;
        }
        
        .code-item.mandatory {
            background: rgba(255, 193, 7, 0.3);
            border: 2px solid #ffc107;
        }
        
        .code-item input {
            margin-right: 12px;
            margin-top: 3px;
            transform: scale(1.3);
        }
        
        .code-item label {
            cursor: pointer;
            line-height: 1.4;
            flex: 1;
            color: white;
        }
        
        .code-title {
            font-weight: 700;
            color: white;
            display: block;
            margin-bottom: 4px;
        }
        
        .code-desc {
            color: rgba(255, 255, 255, 0.7);
            font-size: 13px;
        }
        
        .code-category {
            background: #3498db;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            margin-left: 8px;
        }
        
        .upload-zone {
            border: 3px dashed rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            background: rgba(255, 255, 255, 0.05);
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        
        .upload-zone:hover {
            border-color: #3498db;
            background: rgba(52, 152, 219, 0.1);
        }
        
        .upload-zone .icon {
            font-size: 36px;
            margin-bottom: 10px;
            color: rgba(255, 255, 255, 0.7);
        }
        
        .upload-text {
            font-size: 16px;
            color: white;
            font-weight: 600;
        }
        
        .hazard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-top: 15px;
        }
        
        .hazard-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
            font-weight: 600;
            background: rgba(255, 255, 255, 0.05);
            color: white;
        }
        
        .hazard-item:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: #3498db;
        }
        
        .hazard-item.selected {
            background: rgba(39, 174, 96, 0.3);
            border-color: #27ae60;
        }
        
        .hazard-item input {
            margin-right: 10px;
            transform: scale(1.2);
        }
        
        .footer {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 30px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #27ae60;
            color: white;
        }
        
        .btn-primary:hover {
            background: #219a52;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .status-text {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
        }
        
        .auto-populate-indicator {
            background: #f39c12;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 8px;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .status-message {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            text-align: left;
        }
        
        .status-icon {
            font-size: 2rem;
            min-width: 3rem;
        }
        
        .status-text {
            flex: 1;
        }
        
        .code-category {
            margin-bottom: 1.5rem;
        }
        
        .code-category h4 {
            color: #3b82f6;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }
        
        .code-item {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            margin-bottom: 0.5rem;
            border-left: 3px solid transparent;
        }
        
        .code-item.mandatory {
            border-left-color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
        }
        
        .code-item.optional {
            border-left-color: #3b82f6;
            background: rgba(59, 130, 246, 0.1);
        }
        
        .code-title {
            font-weight: 600;
            color: white;
            margin-bottom: 0.25rem;
        }
        
        .code-description {
            font-size: 0.9rem;
            color: #94a3b8;
            margin-bottom: 0.25rem;
        }
        
        .code-status {
            font-size: 0.8rem;
            font-weight: 600;
            color: #3b82f6;
        }
        
        .mandatory .code-status {
            color: #ef4444;
        }
        
        .no-codes {
            padding: 1rem;
            text-align: center;
            color: #6b7280;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div>
                <h1>Professional Compliance Analysis Service</h1>
                <span class="subtitle">Complete this comprehensive intake form. Our compliance experts will analyze your project and provide a detailed report within 2-3 business days.</span>
            </div>
            <div style="display: flex; align-items: center; gap: 20px;">
                <div class="badges">
                    <span class="badge policy">DRRA 1235b</span>
                    <span class="badge database">53,048 Policies</span>
                    <span class="badge cbcs">Auto-Populate</span>
                </div>
                <nav style="display: flex; gap: 15px;">
                    <a href="/C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25/dashboard" style="color: white; text-decoration: none; padding: 8px 16px; background: rgba(255,255,255,0.1); border-radius: 6px; transition: background 0.3s;">🏠 Home</a>
                    <a href="/C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25/emergency" style="color: white; text-decoration: none; padding: 8px 16px; background: rgba(255,255,255,0.1); border-radius: 6px; transition: background 0.3s;">🚨 Emergency</a>
                    <a href="/C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25/cbcs" style="color: white; text-decoration: none; padding: 8px 16px; background: rgba(255,255,255,0.1); border-radius: 6px; transition: background 0.3s;">🏗️ CBCS</a>
                </nav>
            </div>
        </div>
        
        <div class="main-content">
            <!-- Column 1: Project Information -->
            <div class="column">
                <h3>📋 Project Information</h3>
                
                <div class="form-group">
                    <label class="form-label">Project Name *</label>
                    <input type="text" class="form-control" id="projectName" placeholder="Enter project name" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">FEMA Work Category *</label>
                    <select class="form-control" id="workCategory" required>
                        <option value="">Select Category</option>
                        <option value="A">Category A - Debris Removal</option>
                        <option value="B">Category B - Emergency Protective Measures</option>
                        <option value="C">Category C - Roads and Bridges</option>
                        <option value="D">Category D - Water Control Facilities</option>
                        <option value="E">Category E - Buildings</option>
                        <option value="F">Category F - Utilities</option>
                        <option value="G">Category G - Parks and Recreation</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Facility Type *</label>
                    <select class="form-control" id="facilityType" required>
                        <option value="">Select Facility Type</option>
                        <option value="Bridge">Bridge</option>
                        <option value="Road">Road/Highway</option>
                        <option value="Building">Building/Structure</option>
                        <option value="Water Treatment">Water Treatment Facility</option>
                        <option value="Wastewater">Wastewater Treatment</option>
                        <option value="Electric">Electric Power System</option>
                        <option value="Critical">Critical Facility</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Estimated Project Cost *</label>
                    <input type="number" class="form-control" id="estimatedCost" placeholder="Enter cost in USD" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Damage Date *</label>
                    <input type="date" class="form-control" id="damageDate" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">FEMA Disaster Declaration Number</label>
                    <input type="text" class="form-control" id="disasterNumber" placeholder="e.g., FEMA-4XXX-DR">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Detailed Work Description *</label>
                    <textarea class="form-control" id="workDescription" rows="4" placeholder="Provide detailed description of the work to be performed, including scope, materials, and specifications" required></textarea>
                    <button type="button" class="auto-populate-btn" onclick="autoPopulateCodes()">🔄 Auto-Populate CBCS Codes</button>
                </div>
            </div>
            
            <!-- Column 2: Regulatory Considerations -->
            <div class="column">
                <h3>⚖️ Regulatory Considerations</h3>
                
                <div class="form-group">
                    <label class="form-label">Environmental Concerns</label>
                    <div class="hazard-grid">
                        <label class="hazard-item">
                            <input type="checkbox" name="environmental" value="wetlands">
                            Wetlands/Water bodies nearby
                        </label>
                        <label class="hazard-item">
                            <input type="checkbox" name="environmental" value="endangered">
                            Endangered species habitat
                        </label>
                        <label class="hazard-item">
                            <input type="checkbox" name="environmental" value="floodplain">
                            Located in floodplain
                        </label>
                        <label class="hazard-item">
                            <input type="checkbox" name="environmental" value="contamination">
                            Potential contamination issues
                        </label>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Historic Preservation</label>
                    <div class="hazard-grid">
                        <label class="hazard-item">
                            <input type="checkbox" name="historic" value="listed">
                            Listed on National Register
                        </label>
                        <label class="hazard-item">
                            <input type="checkbox" name="historic" value="eligible">
                            Eligible for National Register
                        </label>
                        <label class="hazard-item">
                            <input type="checkbox" name="historic" value="district">
                            Located in historic district
                        </label>
                        <label class="hazard-item">
                            <input type="checkbox" name="historic" value="over50">
                            Structure over 50 years old
                        </label>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Project Specifics</label>
                    <div class="hazard-grid">
                        <label class="hazard-item">
                            <input type="checkbox" name="specifics" value="seismic">
                            Seismic design required
                        </label>
                        <label class="hazard-item">
                            <input type="checkbox" name="specifics" value="wind">
                            High wind area
                        </label>
                        <label class="hazard-item">
                            <input type="checkbox" name="specifics" value="flood">
                            Flood-resistant design
                        </label>
                        <label class="hazard-item">
                            <input type="checkbox" name="specifics" value="accessibility">
                            ADA compliance required
                        </label>
                    </div>
                </div>
                
                <div class="upload-zone" onclick="document.getElementById('fileInput').click()">
                    <div class="icon">📁</div>
                    <div class="upload-text">Upload Supporting Documents</div>
                    <div style="font-size: 14px; color: #7f8c8d; margin-top: 5px;">
                        PDF, DWG, DOC, XLS (Max 50MB each)
                    </div>
                    <input type="file" id="fileInput" multiple style="display: none;" accept=".pdf,.dwg,.doc,.docx,.xls,.xlsx">
                </div>
            </div>
            
            <!-- Column 3: CBCS Codes Selection -->
            <div class="column">
                <div class="codes-section">
                    <h3>📋 CBCS Codes Selection</h3>
                    <div id="cbcs-status" class="status-message">
                        <div class="status-icon">⏳</div>
                        <div class="status-text">
                            <strong>Awaiting Project Details</strong><br>
                            Complete the project information to see applicable CBCS codes
                        </div>
                    </div>
                    
                    <div id="cbcs-codes-container" style="display: none;">
                        <!-- AASHTO Standards -->
                        <div class="code-category">
                            <h4>AASHTO Standards</h4>
                            <div id="aashto-codes" class="code-group">
                                <!-- Will be populated dynamically -->
                            </div>
                        </div>
                        
                        <!-- Structural Standards -->
                        <div class="code-category">
                            <h4>Structural Standards</h4>
                            <div id="structural-codes" class="code-group">
                                <!-- Will be populated dynamically -->
                            </div>
                        </div>
                        
                        <!-- Safety & Fire Protection -->
                        <div class="code-category">
                            <h4>Safety & Fire Protection</h4>
                            <div id="safety-codes" class="code-group">
                                <!-- Will be populated dynamically -->
                            </div>
                        </div>
                        
                        <!-- Environmental Standards -->
                        <div class="code-category">
                            <h4>Environmental Standards</h4>
                            <div id="environmental-codes" class="code-group">
                                <!-- Will be populated dynamically -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <div class="status-text">
                Phase 7 Complete • 53,048 FEMA policy records • CBCS integration active
            </div>
            <div>
                <button type="button" class="btn btn-secondary" onclick="window.location.href="/C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25/dashboard"">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitIntake()">Submit for Analysis</button>
            </div>
        </div>
    </div>
    
    <script>
        / Auto-populate CBCS codes based on project details
        async function autoPopulateCodes() {
            const facilityType = document.getElementById('facilityType').value;
            const workCategory = document.getElementById('workCategory').value;
            const workDescription = document.getElementById('workDescription').value;
            const estimatedCost = parseFloat(document.getElementById('estimatedCost').value) || 0;
            
            if (!facilityType || !workDescription) {
                alert('Please fill in Facility Type and Work Description first');
                return;
            }
            
            document.getElementById('loading').style.display = 'block';
            document.querySelector('.auto-populate-indicator').style.display = 'none';
            
            try {
                const response = await fetch('/api/cbcs/auto-populate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        facilityType,
                        workCategory,
                        workDescription,
                        estimatedCost
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    / Clear all existing selections
                    document.querySelectorAll('input[name="cbcs_codes"]').forEach(checkbox => {
                        checkbox.checked = false;
                        checkbox.closest('.code-item').classList.remove('selected');
                    });
                    
                    / Apply auto-selected codes
                    data.auto_selected_codes.forEach(code => {
                        const checkbox = document.querySelector(`input[value="${code.code}"]`);
                        if (checkbox) {
                            checkbox.checked = true;
                            checkbox.closest('.code-item').classList.add('selected');
                        }
                    });
                    
                    document.querySelector('.auto-populate-indicator').style.display = 'inline-block';
                    
                    alert(`Auto-populated ${data.total_codes} codes (${data.mandatory_count} mandatory)\n\n${data.justification}`);
                } else {
                    alert('Error auto-populating codes: ' + data.error);
                }
            } catch (error) {
                console.error('Auto-populate error:', error);
                alert('Error auto-populating codes. Please try again.');
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        }
        
        / Handle checkbox selections
        document.addEventListener('change', function(e) {
            if (e.target.name === 'cbcs_codes') {
                const codeItem = e.target.closest('.code-item');
                if (e.target.checked) {
                    codeItem.classList.add('selected');
                } else {
                    codeItem.classList.remove('selected');
                }
            }
        });
        
        / Submit intake
        async function submitIntake() {
            const formData = {
                projectName: document.getElementById('projectName').value,
                workCategory: document.getElementById('workCategory').value,
                facilityType: document.getElementById('facilityType').value,
                estimatedCost: document.getElementById('estimatedCost').value,
                damageDate: document.getElementById('damageDate').value,
                disasterNumber: document.getElementById('disasterNumber').value,
                workDescription: document.getElementById('workDescription').value,
                selectedCodes: Array.from(document.querySelectorAll('input[name="cbcs_codes"]:checked')).map(cb => cb.value),
                environmental: Array.from(document.querySelectorAll('input[name="environmental"]:checked')).map(cb => cb.value),
                historic: Array.from(document.querySelectorAll('input[name="historic"]:checked')).map(cb => cb.value),
                specifics: Array.from(document.querySelectorAll('input[name="specifics"]:checked')).map(cb => cb.value),
                start_wizard: true
            };
            
            / Validate required fields
            if (!formData.projectName || !formData.workCategory || !formData.facilityType || 
                !formData.estimatedCost || !formData.damageDate || !formData.workDescription) {
                alert('Please fill in all required fields marked with *');
                return;
            }
            
            try {
                const response = await fetch('/api/intake/cbcs', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    alert(`CBCS Intake submitted successfully!\n\nIntake ID: ${data.intake_id}\n\nRedirecting to compliance analysis...`);
                    window.location.href="/C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25/professional-intake";
                } else {
                    alert('Error submitting intake: ' + data.message);
                }
            } catch (error) {
                console.error('Submit error:', error);
                alert('Error submitting intake. Please try again.');
            }
        }
        
        / Auto-populate when facility type or cost changes
        document.getElementById('facilityType').addEventListener('change', function() {
            if (this.value && document.getElementById('workDescription').value) {
                autoPopulateCodes();
            }
        });
        
        document.getElementById('estimatedCost').addEventListener('blur', function() {
            if (this.value && document.getElementById('facilityType').value && document.getElementById('workDescription').value) {
                autoPopulateCodes();
            }
        });
        
        / CBCS Auto-Population Logic
        function evaluateCBCSCodes() {
            const projectData = {
                facilityType: document.getElementById('facilityType')?.value,
                workCategory: document.getElementById('workCategory')?.value,
                estimatedCost: parseFloat(document.getElementById('estimatedCost')?.value) || 0,
                projectDescription: document.getElementById('workDescription')?.value?.toLowerCase() || '',
                floodplain: document.querySelector('input[name="environmental"][value="floodplain"]')?.checked,
                seismic: document.querySelector('input[name="environmental"][value="seismic"]')?.checked,
                historic: document.querySelector('input[name="environmental"][value="historic"]')?.checked
            };
            
            / Check if we have enough data to evaluate
            if (!projectData.facilityType || !projectData.workCategory) {
                showCBCSStatus('⏳', 'Awaiting Project Details', 'Complete the project information to see applicable CBCS codes');
                return;
            }
            
            showCBCSStatus('🔄', 'Evaluating Requirements', 'Analyzing project details to determine applicable codes...');
            
            / Simulate API call delay
            setTimeout(() => {
                const applicableCodes = determineCBCSCodes(projectData);
                displayCBCSCodes(applicableCodes);
            }, 1000);
        }
        
        function determineCBCSCodes(data) {
            const codes = {
                aashto: [],
                structural: [],
                safety: [],
                environmental: []
            };
            
            / AASHTO Standards (for bridges, roads, highways)
            if (data.facilityType.includes('bridge') || data.facilityType.includes('road') || data.workCategory === 'C') {
                codes.aashto.push({
                    id: 'AASHTO_LRFD_Bridge',
                    title: 'AASHTO LRFD Bridge Design Specifications',
                    description: 'Load and Resistance Factor Design for highway bridges',
                    mandatory: true
                });
                codes.aashto.push({
                    id: 'AASHTO_Highway_Geometric',
                    title: 'AASHTO Policy on Geometric Design of Highways',
                    description: 'Highway and roadway geometric design standards',
                    mandatory: true
                });
            }
            
            / Structural Standards (for buildings, high-cost projects)
            if (data.facilityType.includes('building') || data.estimatedCost > 100000) {
                codes.structural.push({
                    id: 'ASCE_7_16',
                    title: 'ASCE/SEI 7-16 Minimum Design Loads',
                    description: 'Wind, seismic, snow, and other load requirements',
                    mandatory: true
                });
                codes.structural.push({
                    id: 'IBC_Current',
                    title: 'International Building Code (Current Edition)',
                    description: 'Comprehensive building safety standards',
                    mandatory: true
                });
            }
            
            / Concrete/Steel for structural projects
            if (data.projectDescription.includes('concrete') || data.estimatedCost > 500000) {
                codes.structural.push({
                    id: 'ACI_318_19',
                    title: 'ACI 318-19 Building Code Requirements for Reinforced Concrete',
                    description: 'Concrete design and construction standards',
                    mandatory: false
                });
            }
            
            if (data.projectDescription.includes('steel') || data.facilityType.includes('bridge')) {
                codes.structural.push({
                    id: 'AISC_360_19',
                    title: 'AISC 360-19 Specification for Structural Steel Buildings',
                    description: 'Steel construction standards and specifications',
                    mandatory: false
                });
            }
            
            / Safety & Fire Protection (always applicable for buildings)
            if (data.facilityType.includes('building') || data.facilityType.includes('school') || data.facilityType.includes('hospital')) {
                codes.safety.push({
                    id: 'NFPA_1141',
                    title: 'NFPA 1141 Fire Protection Infrastructure',
                    description: 'Fire safety and protection standards for public buildings',
                    mandatory: true
                });
                codes.safety.push({
                    id: 'NFPA_NEC',
                    title: 'NFPA 70 National Electrical Code',
                    description: 'Electrical safety standards and requirements',
                    mandatory: true
                });
            }
            
            / Environmental Standards (based on conditions)
            if (data.floodplain || data.projectDescription.includes('flood')) {
                codes.environmental.push({
                    id: 'ASCE_24_14',
                    title: 'ASCE 24-14 Flood Resistant Design and Construction',
                    description: 'Standards for construction in flood-prone areas',
                    mandatory: true
                });
            }
            
            if (data.seismic || data.estimatedCost > 1000000) {
                codes.environmental.push({
                    id: 'ASCE_41_17',
                    title: 'ASCE 41-17 Seismic Evaluation and Retrofit',
                    description: 'Seismic evaluation and retrofit standards',
                    mandatory: data.estimatedCost > 1000000
                });
            }
            
            if (data.historic) {
                codes.environmental.push({
                    id: 'SECTION_106',
                    title: 'Section 106 Historic Preservation Requirements',
                    description: 'Historic property preservation compliance',
                    mandatory: true
                });
            }
            
            return codes;
        }
        
        function showCBCSStatus(icon, title, description) {
            const statusDiv = document.getElementById('cbcs-status');
            const codesContainer = document.getElementById('cbcs-codes-container');
            
            statusDiv.innerHTML = `
                <div class="status-icon">${icon}</div>
                <div class="status-text">
                    <strong>${title}</strong><br>
                    ${description}
                </div>
            `;
            statusDiv.style.display = 'flex';
            codesContainer.style.display = 'none';
        }
        
        function displayCBCSCodes(codes) {
            const statusDiv = document.getElementById('cbcs-status');
            const codesContainer = document.getElementById('cbcs-codes-container');
            
            / Hide status, show codes
            statusDiv.style.display = 'none';
            codesContainer.style.display = 'block';
            
            / Populate each category
            populateCodeCategory('aashto-codes', codes.aashto);
            populateCodeCategory('structural-codes', codes.structural);
            populateCodeCategory('safety-codes', codes.safety);
            populateCodeCategory('environmental-codes', codes.environmental);
        }
        
        function populateCodeCategory(containerId, codes) {
            const container = document.getElementById(containerId);
            if (!codes || codes.length === 0) {
                container.innerHTML = '<div class="no-codes">No applicable codes for this category</div>';
                return;
            }
            
            container.innerHTML = codes.map(code => `
                <div class="code-item ${code.mandatory ? 'mandatory' : 'optional'}">
                    <input type="checkbox" id="${code.id}" name="cbcs_codes" value="${code.id}" ${code.mandatory ? 'checked disabled' : ''}>
                    <label for="${code.id}">
                        <div class="code-title">${code.title}</div>
                        <div class="code-description">${code.description}</div>
                        <div class="code-status">${code.mandatory ? 'MANDATORY' : 'RECOMMENDED'}</div>
                    </label>
                </div>
            `).join('');
        }
        
        / Add event listeners to trigger CBCS evaluation
        document.addEventListener('DOMContentLoaded', function() {
            const triggerFields = ['facilityType', 'workCategory', 'estimatedCost', 'projectDescription'];
            
            triggerFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.addEventListener('change', evaluateCBCSCodes);
                    field.addEventListener('input', debounce(evaluateCBCSCodes, 500));
                }
            });
            
            / Also listen for environmental checkboxes
            document.querySelectorAll('input[name="environmental"]').forEach(checkbox => {
                checkbox.addEventListener('change', evaluateCBCSCodes);
            });
        });
        
        / Debounce function to avoid too many API calls
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>
</body>
</html> 