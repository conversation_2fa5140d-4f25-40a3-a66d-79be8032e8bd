<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emergency Intake - ComplianceMax</title>
    <link rel="stylesheet" href="ui/nav.css">
    <link rel="stylesheet" href="ui/tokens.css">
    <link rel="stylesheet" href="ui/stack.css">
    <link rel="stylesheet" href="ui/depth-theme.css">
    <link rel="stylesheet" href="ui/footer.css">
    <link rel="stylesheet" href="ui/sidewalk-hints.css">
    <link rel="stylesheet" href="print.css" media="print">
    <script defer src="nav.js"></script>
    <script defer src="footer.js"></script>
    <script defer src="fema_workbook_integration.js"></script>
    <script defer src="js/sidewalk-hints.js"></script>
    <style>
        /* Emergency-specific styles - using depth-theme base with dark theme */

        /* Dark theme override */
        body {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%) !important;
            color: #ffffff !important;
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        /* Fix navigation layering and centering */
        .cmx-brand-header {
            z-index: 1000 !important;
            position: relative !important;
            text-align: center;
            color: #ffffff !important;
            padding: 10px 0; /* Reduce padding to minimize white space */
        }

        .cmx-topnav {
            z-index: 1001 !important;
            position: relative !important;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 5px 0; /* Reduce padding to minimize white space */
        }

        /* Center navigation items */
        .nav-items {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
        }

        /* Fix white backgrounds on cards */
        .main-content {
            background: rgba(255, 255, 255, 0.95) !important;
            color: #333 !important;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        /* Fix text readability - ensure dark text on light backgrounds */
        .emergency-container, .emergency-form-container {
            background: rgba(255, 255, 255, 0.95) !important;
            color: #333 !important;
        }

        .emergency-container * {
            color: #333 !important;
        }

        .emergency-form-container * {
            color: #333 !important;
        }

        /* Ensure all text elements are readable */
        p, span, div, label, h1, h2, h3, h4, h5, h6 {
            color: #333 !important;
        }

        /* Fix specific text elements */
        .emergency-notice, .emergency-notice * {
            color: #856404 !important;
            background: #fff3cd !important;
        }

        /* Fix category and priority text */
        .category-title, .category-subtitle {
            color: #333 !important;
        }

        .priority-option * {
            color: #333 !important;
        }

        /* Emergency alert styling */
        .emergency-alert {
            background: linear-gradient(135deg, #dc3545, #c82333) !important;
            color: #ffffff !important;
        }

        /* Fix category cards */
        .category-card {
            background: #ffffff !important;
            color: #333 !important;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* Fix status indicators */
        .status-indicator {
            background: #ffffff !important;
            color: #333 !important;
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        /* Fix form elements */
        .form-group label {
            color: #333 !important;
        }

        .form-control {
            background: #ffffff !important;
            color: #333 !important;
            border: 1px solid #ddd;
        }

        /* Fix buttons */
        .btn {
            border: none;
            border-radius: 6px;
            font-weight: 500;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997) !important;
            color: #ffffff !important;
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3) !important;
            color: #ffffff !important;
        }

        /* Fix text readability */
        .text-muted {
            color: #666 !important;
        }

        .emergency-container {
            max-width: 1200px;
            margin: 20px auto 0; /* Fix excessive white space - remove negative margin */
            padding: 20px;
        }

        .emergency-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 20px 30px;
            text-align: center;
            position: relative;
            border-radius: 15px;
            margin-top: 20px; /* Fix excessive white space - reduce from 200px to 20px */
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
        }

        .emergency-header::before {
            content: '🚨';
            position: absolute;
            top: 10px;
            left: 30px;
            font-size: 2em;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
        }

        .back-btn {
            position: absolute;
            top: 20px;
            right: 30px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .emergency-title {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .emergency-subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .emergency-form-container {
            background: white;
            border-radius: 15px;
            padding: 25px; /* Reduce padding from 40px to 25px */
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .emergency-notice {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 6px;
            padding: 12px; /* Reduce padding from 15px to 12px */
            margin-bottom: 20px; /* Reduce margin from 25px to 20px */
            text-align: center;
        }

        .emergency-notice h3 {
            color: #856404;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .emergency-notice p {
            color: #856404;
            margin: 0;
        }

        /* Use depth-theme form styles */

        .priority-section {
            background: #f8f9fa !important;
            border-radius: 12px;
            padding: 15px !important; /* Reduce padding to minimize white space */
            margin-bottom: 15px !important; /* Reduce margin to minimize white space */
            border: 1px solid #e9ecef;
            color: #333 !important;
        }

        .category-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px !important; /* Further reduce gap to minimize white space */
            margin-top: 10px !important; /* Further reduce margin to minimize white space */
        }

        .category-card {
            background: white;
            border: 3px solid #ddd;
            border-radius: 15px;
            padding: 20px; /* Reduce padding from 30px to 20px */
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .category-card:hover {
            border-color: #dc3545;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(220, 53, 69, 0.2);
        }

        .category-card.selected {
            border-color: #dc3545;
            background: #fff5f5;
            box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
        }

        .category-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .category-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .category-subtitle {
            color: #666;
            font-size: 1em;
        }

        .priority-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 8px !important; /* Further reduce gap to minimize white space */
            margin-top: 10px !important; /* Further reduce margin to minimize white space */
            margin-bottom: 10px !important; /* Add bottom margin control */
        }

        .priority-option {
            background: white;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 12px; /* Reduce padding from 15px to 12px */
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .priority-option:hover {
            border-color: #dc3545;
            transform: translateY(-2px);
        }

        .priority-option.selected {
            border-color: #dc3545;
            background: #fff5f5;
        }

        .priority-icon {
            font-size: 2em;
            margin-bottom: 10px;
            display: block;
        }

        .compliance-pods {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px !important; /* Further reduce gap to minimize white space */
            margin: 10px 0 !important; /* Further reduce margin to minimize white space */
        }

        .compliance-pod {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #dc3545;
        }

        .pod-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #dc3545;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .pod-content {
            color: #333;
            line-height: 1.6;
        }

        .pod-list {
            list-style: none;
            padding: 0;
        }

        .pod-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .pod-list li:last-child {
            border-bottom: none;
        }

        .pod-icon {
            color: #dc3545;
            font-weight: bold;
        }

        .submit-section {
            text-align: center;
            padding-top: 15px !important; /* Reduce padding to minimize white space */
            border-top: 2px solid #eee;
            margin-top: 15px !important; /* Reduce margin to minimize white space */
        }

        .submit-btn {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
            text-decoration: none;
            display: inline-block;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        @media (max-width: 768px) {
            .category-grid {
                grid-template-columns: 1fr;
            }
            .compliance-pods {
                grid-template-columns: 1fr;
            }
            .priority-options {
                grid-template-columns: 1fr;
            }
            .emergency-header {
                padding: 20px;
            }
            .emergency-form-container {
                padding: 20px;
            }
            .emergency-container {
                padding: 20px 10px;
            }
        }


    </style>

</head>
<body data-page="emergency" data-flow="emergency" data-step="1">
    <div id="universal-nav" class="site-nav">
        <!-- Brand header section -->
        <div class="cmx-brand-header">
            <h1 class="brand-title">COMPLIANCEMAX</h1>
            <div class="brand-subtitle">Federal Public Assistance Compliance Automation</div>
        </div>

        <!-- Horizontal navigation bar -->
        <div class="cmx-topnav">
            <div class="nav-items">
                <nav class="horizontal-nav">
                    <a href="landing_page.html" class="nav-item">🏠 Home</a>
                    <a href="dashboard.html" class="nav-item">⊞ Dashboard</a>
                    <a href="emergency_intake.html" class="nav-item active">△ Emergency</a>
                    <a href="cbcs_demo.html" class="nav-item">🛡 CBCS</a>
                    <a href="worksheet.html" class="nav-item">📄 Worksheet</a>
                    <a href="report.html" class="nav-item">📊 Report</a>
                    <a href="document_upload_system.html" class="nav-item">↑ Upload</a>
                    <a href="legal.html" class="nav-item">⚖ Legal</a>
                </nav>

                <!-- AI Assistant buttons -->
                <div class="ai-buttons">
                    <button id="maxassist-header-btn" class="ai-btn maxassist-btn">
                        <span>Max Assist</span>
                        <span class="ai-icon">👤</span>
                    </button>
                    <button id="autopilot-header-btn" class="ai-btn autopilot-btn">
                        <span>AutoPilot</span>
                        <span class="ai-icon">✈</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

<main id="main" class="emergency-container">
    <div class="emergency-header">
        <a href="landing_page.html" class="back-btn">← Back</a>
        <div class="emergency-title">Emergency Intake</div>
        <div class="emergency-subtitle">Fast-Track FEMA PA Request</div>
    </div>

    <div class="emergency-form-container">
            <div class="emergency-notice">
                <h3>⚡ Emergency Fast-Track Process</h3>
                <p>This streamlined intake is for immediate disaster response. Complete information will be collected in follow-up phases.</p>
            </div>

            <form id="emergencyForm">
                <!-- STEP 1: Category Selection (Top Priority) -->
                <div class="priority-section">
                    <h3>Emergency Work Category <span class="required">*</span></h3>
                    <p style="color: #666; margin-bottom: 20px;">Select the category that best describes your emergency work to customize the intake process</p>
                    <div class="category-grid">
                        <div class="category-card" data-category="A">
                            <div class="category-icon">🗑️</div>
                            <div class="category-title">Category A</div>
                            <div class="category-subtitle">Debris Removal</div>
                        </div>
                        <div class="category-card" data-category="B">
                            <div class="category-icon">🛡️</div>
                            <div class="category-title">Category B</div>
                            <div class="category-subtitle">Emergency Protective Measures</div>
                        </div>
                    </div>
                    <input type="hidden" id="workCategory" name="workCategory" required>
                </div>

                <!-- STEP 2: Category-Driven Questions (Hidden until category selected) -->
                <div id="categoryQuestions" style="display: none;">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="applicantName">Applicant Name <span class="required">*</span></label>
                            <input type="text" id="applicantName" name="applicantName" required>
                        </div>

                        <div class="form-group">
                            <label for="contactPhone">Emergency Contact <span class="required">*</span></label>
                            <input type="tel" id="contactPhone" name="contactPhone" required>
                        </div>

                        <div class="form-group">
                            <label for="disasterNumber">Disaster Number</label>
                            <input type="text" id="disasterNumber" name="disasterNumber" placeholder="DR-XXXX-XX">
                        </div>

                        <div class="form-group">
                            <label for="incidentDate">Incident Date <span class="required">*</span></label>
                            <input type="date" id="incidentDate" name="incidentDate" required>
                        </div>

                        <div class="form-group full-width">
                            <label for="facilityName">Damaged Facility/Infrastructure <span class="required">*</span></label>
                            <input type="text" id="facilityName" name="facilityName" placeholder="e.g., Main Street Bridge, City Hall, Water Treatment Plant" required>
                        </div>

                        <!-- Category-specific questions will be inserted here -->
                        <div id="categorySpecificQuestions"></div>

                        <div class="form-group full-width">
                            <label for="damageDescription">Brief Damage Description <span class="required">*</span></label>
                            <textarea id="damageDescription" name="damageDescription" placeholder="Describe the damage and immediate safety concerns..." required></textarea>
                        </div>
                    </div>
                </div>

                <div class="priority-section">
                    <h3>Emergency Priority Level <span class="required">*</span></h3>
                    <div class="priority-options">
                        <div class="priority-option" data-priority="critical">
                            <span class="priority-icon">🔴</span>
                            <strong>Critical</strong><br>
                            <small>Life safety threat</small>
                        </div>
                        <div class="priority-option" data-priority="urgent">
                            <span class="priority-icon">🟡</span>
                            <strong>Urgent</strong><br>
                            <small>Essential services</small>
                        </div>
                        <div class="priority-option" data-priority="standard">
                            <span class="priority-icon">🟢</span>
                            <strong>Standard</strong><br>
                            <small>Normal processing</small>
                        </div>
                    </div>
                    <input type="hidden" id="priorityLevel" name="priorityLevel" required>
                </div>

                <!-- STEP 3: Compliance Pods (Shown after category selection) -->
                <div id="compliancePods" style="display: none;">
                    <h3 style="color: #253464; margin-bottom: 20px;">📋 Documentation Requirements</h3>
                    <div class="compliance-pods">
                        <div class="compliance-pod">
                            <div class="pod-title">
                                📄 Required from Applicant
                            </div>
                            <div class="pod-content">
                                <ul class="pod-list" id="applicantDocs">
                                    <!-- Populated based on category selection -->
                                </ul>
                            </div>
                        </div>
                        <div class="compliance-pod">
                            <div class="pod-title">
                                📚 System Analysis Resources
                            </div>
                            <div class="pod-content">
                                <ul class="pod-list" id="systemDocs">
                                    <!-- Populated based on category selection -->
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="estimatedCost">Estimated Damage Cost (if known)</label>
                    <input type="number" id="estimatedCost" name="estimatedCost" placeholder="$" min="0" step="1000">
                </div>

                <!-- FEMA Project Workbook Integration (Cost Capture Only) -->
                <div id="emergency-workbook-section" style="margin: 30px 0; display: none;">
                    <div style="background: #f0f9ff; border: 2px solid #3b82f6; border-radius: 12px; padding: 20px; margin-bottom: 20px;">
                        <h3 style="color: #1d4ed8; margin-bottom: 15px;">📋 FEMA Project Workbook Template</h3>
                        <p style="color: #1e40af; margin-bottom: 15px;">
                            Cost capture required for this emergency work. Complete the FEMA Project Workbook Template for cost analysis and documentation.
                        </p>
                        <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 6px; padding: 10px; margin: 15px 0;">
                            <strong>⚠️ Cost Capture Triggered:</strong> Emergency work with estimated cost over $10,000 requires detailed cost documentation.
                        </div>
                        <button type="button" class="submit-btn" onclick="openEmergencyWorkbook()" style="background: #3b82f6;">
                            📊 Open FEMA Workbook Template
                        </button>
                    </div>
                </div>

                <div class="submit-section">
                    <div style="display: flex; gap: 15px; justify-content: center; margin-bottom: 20px;">
                        <a href="document_upload_system.html" class="submit-btn" style="text-decoration: none; display: inline-block; background: #28a745;">
                            📄 Upload Documents
                        </a>
                        <button type="submit" class="submit-btn" id="submitBtn" disabled>
                            🚀 Submit Emergency Request
                        </button>
                    </div>
                    <p style="margin-top: 15px; color: #666; font-size: 0.9em;">
                        Upload supporting documents for AI analysis, then submit your emergency request.<br>
                        You will receive a confirmation email and case number within 15 minutes.
                    </p>
                </div>
            </form>
        </div>

    <script>
        // Category A and B descriptions
        const categoryDescriptions = {
            'A': `Category A - Debris Removal Emergency Work

This category covers the removal of debris from public roads, public property, and private property when determined to be in the public interest. Emergency debris removal work includes:

• Clearance of debris from public roads and rights-of-way to allow passage of emergency vehicles
• Removal of debris from public property that poses an immediate threat to life, public health, or safety
• Removal of debris from private property when determined to be in the public interest
• Temporary storage and disposal of collected debris at approved sites
• Documentation and monitoring of debris removal operations

Required Documentation:
- Detailed descriptions of debris removal activities
- Photographs/video of debris before and after removal
- Maps of affected areas showing debris locations
- Labor records and equipment usage logs
- Procurement documents for contracted services`,

            'B': `Category B - Emergency Protective Measures

This category covers emergency work performed to eliminate or reduce immediate threats to life, public health, or safety, or to eliminate or reduce immediate threats of significant damage to improved public or private property. Emergency protective measures include:

• Sandbagging, diking, and other flood control measures
• Emergency repairs to damaged facilities
• Provision of emergency power, water, ice, and other commodities
• Search and rescue operations
• Emergency medical care and mass care
• Security and traffic control
• Demolition or stabilization of damaged structures that pose immediate threats

Required Documentation:
- Building safety evaluations and structural assessments
- Emergency work labor records and timekeeping
- Equipment usage logs and rental agreements
- Procurement documents for emergency services and supplies
- Photographs documenting emergency conditions and response actions`
        };

        // Compliance pods data
        const compliancePods = {
            'A': {
                applicant: [
                    '📸 Photographs/video of debris before removal',
                    '🗺️ Maps showing debris locations and affected areas',
                    '📋 Detailed descriptions of debris removal activities',
                    '⏰ Labor records and timekeeping documentation',
                    '🚛 Equipment usage logs and rental agreements',
                    '📄 Procurement documents for contracted services',
                    '📍 GPS coordinates of debris collection sites',
                    '⚖️ Weight tickets and disposal receipts'
                ],
                system: [
                    '📖 FEMA PAPPG v5.0 - Debris Removal Guidelines',
                    '📚 FEMA Debris Monitoring Guide',
                    '🏛️ Corps of Engineers debris coordination protocols',
                    '📋 Public Assistance 9500-Series Policies',
                    '🗑️ Debris Management Plans and procedures',
                    '⚖️ Disposal site regulations and permits',
                    '📊 Debris estimating formulas and cost data',
                    '🔍 FEMA Appeals Database - Debris removal cases',
                    '📜 Environmental compliance requirements (EHP)'
                ]
            },
            'B': {
                applicant: [
                    '🏗️ Building safety evaluations and assessments',
                    '📋 Emergency work labor records and timekeeping',
                    '🚛 Equipment usage logs and rental agreements',
                    '📄 Procurement documents for emergency services',
                    '📸 Photos documenting emergency conditions',
                    '🛡️ Documentation of life safety measures taken',
                    '⚡ Emergency power/utilities restoration records',
                    '🚧 Traffic control and security documentation'
                ],
                system: [
                    '📖 FEMA PAPPG v5.0 - Emergency Protective Measures',
                    '🛡️ Emergency Work Policy (DRRA Section 1241)',
                    '🏗️ Building safety evaluation guidelines',
                    '⚡ Life safety measures guidance documents',
                    '🚨 Emergency services coordination protocols',
                    '📋 Structural assessment standards',
                    '🔍 FEMA Appeals Database - Emergency work cases',
                    '📜 Environmental compliance (EHP) triggers',
                    '⚖️ 2 CFR 200 procurement requirements'
                ]
            }
        };

        // Category selection
        document.querySelectorAll('.category-card').forEach(card => {
            card.addEventListener('click', function() {
                // Remove selection from all category cards
                document.querySelectorAll('.category-card').forEach(c => c.classList.remove('selected'));
                this.classList.add('selected');

                const category = this.dataset.category;
                document.getElementById('workCategory').value = category;

                // Show category-driven questions
                document.getElementById('categoryQuestions').style.display = 'block';

                // Show and populate compliance pods
                document.getElementById('compliancePods').style.display = 'block';
                populateCompliancePods(category);

                // Add category-specific questions
                addCategorySpecificQuestions(category);

                validateForm();
            });
        });

        function populateCompliancePods(category) {
            const applicantList = document.getElementById('applicantDocs');
            const systemList = document.getElementById('systemDocs');

            // Clear existing content
            applicantList.innerHTML = '';
            systemList.innerHTML = '';

            // Populate applicant requirements
            compliancePods[category].applicant.forEach(item => {
                const li = document.createElement('li');
                li.innerHTML = `<span class="pod-icon">✓</span> ${item}`;
                applicantList.appendChild(li);
            });

            // Populate system analysis resources
            compliancePods[category].system.forEach(item => {
                const li = document.createElement('li');
                li.innerHTML = `<span class="pod-icon">📋</span> ${item}`;
                systemList.appendChild(li);
            });
        }

        function addCategorySpecificQuestions(category) {
            const container = document.getElementById('categorySpecificQuestions');
            container.innerHTML = '';

            if (category === 'A') {
                container.innerHTML = `
                    <div class="form-group">
                        <label for="debrisType">Type of Debris <span class="required">*</span></label>
                        <select id="debrisType" name="debrisType" required>
                            <option value="">Select debris type...</option>
                            <option value="vegetative">Vegetative Debris (trees, branches)</option>
                            <option value="construction">Construction & Demolition Debris</option>
                            <option value="mixed">Mixed Debris</option>
                            <option value="hazardous">Hazardous Materials</option>
                            <option value="vehicles">Vehicles & Vessels</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="debrisLocation">Debris Location</label>
                        <input type="text" id="debrisLocation" name="debrisLocation" placeholder="Public roads, public property, private property">
                    </div>
                `;
            } else if (category === 'B') {
                container.innerHTML = `
                    <div class="form-group">
                        <label for="emergencyType">Type of Emergency Measure <span class="required">*</span></label>
                        <select id="emergencyType" name="emergencyType" required>
                            <option value="">Select emergency type...</option>
                            <option value="sandbagging">Sandbagging/Flood Control</option>
                            <option value="emergency_repairs">Emergency Facility Repairs</option>
                            <option value="emergency_power">Emergency Power/Utilities</option>
                            <option value="search_rescue">Search and Rescue</option>
                            <option value="security">Security/Traffic Control</option>
                            <option value="demolition">Emergency Demolition</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="lifeSafetyThreat">Life Safety Threat Description</label>
                        <textarea id="lifeSafetyThreat" name="lifeSafetyThreat" placeholder="Describe the immediate threat to life, public health, or safety..."></textarea>
                    </div>
                `;
            }
        }

        // Priority selection
        document.querySelectorAll('.priority-option[data-priority]').forEach(option => {
            option.addEventListener('click', function() {
                // Remove selection from priority options only
                document.querySelectorAll('.priority-option[data-priority]').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                document.getElementById('priorityLevel').value = this.dataset.priority;
                validateForm();
            });
        });

        // Form validation
        function validateForm() {
            let requiredFields = ['workCategory'];

            // Only validate other fields if category is selected
            if (document.getElementById('workCategory').value) {
                requiredFields = requiredFields.concat([
                    'applicantName', 'contactPhone', 'incidentDate',
                    'facilityName', 'damageDescription', 'priorityLevel'
                ]);

                // Add category-specific required fields
                const category = document.getElementById('workCategory').value;
                if (category === 'A') {
                    requiredFields.push('debrisType');
                } else if (category === 'B') {
                    requiredFields.push('emergencyType');
                }

                // Show FEMA Workbook section only when cost capture is needed
                const estimatedCost = parseFloat(document.getElementById('estimatedCost')?.value || 0);
                const workbookSection = document.getElementById('emergency-workbook-section');

                if (workbookSection && estimatedCost > 10000) {
                    workbookSection.style.display = 'block';
                    console.log(`💰 Cost capture triggered: $${estimatedCost.toLocaleString()}`);
                } else if (workbookSection) {
                    workbookSection.style.display = 'none';
                }
            }

            const allFilled = requiredFields.every(field => {
                const element = document.getElementById(field);
                return element && element.value.trim() !== '';
            });

            const submitBtn = document.getElementById('submitBtn');
            if (submitBtn) {
                submitBtn.disabled = !allFilled;
            }
        }

        // Open Emergency FEMA Workbook
        function openEmergencyWorkbook() {
            // Collect emergency intake data
            const emergencyData = {
                applicantName: document.getElementById('applicantName')?.value,
                projectTitle: `Emergency Work - ${document.getElementById('facilityName')?.value}`,
                category: document.getElementById('workCategory')?.value === 'A' ? 'Category A - Debris Removal' : 'Category B - Emergency Protective Measures',
                incidentDate: document.getElementById('incidentDate')?.value,
                projectLocation: document.getElementById('facilityLocation')?.value,
                damageDescription: document.getElementById('damageDescription')?.value,
                estimatedCost: document.getElementById('estimatedCost')?.value,
                priorityLevel: document.getElementById('priorityLevel')?.value,
                emergencyType: document.getElementById('emergencyType')?.value || document.getElementById('debrisType')?.value
            };

            // Open workbook in new window/modal
            const workbookWindow = window.open('', 'FEMAWorkbook', 'width=1200,height=800,scrollbars=yes,resizable=yes');
            const workbookHTML = '<!DOCTYPE html><html><head><title>FEMA Emergency Work Workbook</title><script src="fema_workbook_integration.js"><\/script></head><body style="font-family: Arial, sans-serif; padding: 20px;"><h1>🚨 FEMA Emergency Work Project Workbook</h1><div id="emergency-workbook-container"></div><script>document.addEventListener("DOMContentLoaded", function() { const container = document.getElementById("emergency-workbook-container"); container.innerHTML = femaWorkbook.generateWorkbookHTML(); femaWorkbook.populateWorkbook({}); switchWorkbookTab("project-info"); });<\/script></body></html>';
            workbookWindow.document.write(workbookHTML);
            workbookWindow.document.close();
        }
    </script>

    <script>

        // Add event listeners to required fields
        document.addEventListener('input', validateForm);
        document.addEventListener('change', validateForm);

        // Form submission
        document.getElementById('emergencyForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Simulate submission
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.innerHTML = '⏳ Submitting...';
            submitBtn.disabled = true;

            // Get form data and update project
            const cat = document.getElementById('workCategory').value;
            const pid = localStorage.getItem('cmx:lastProjectId') || cmx.newProject({
                type: 'Emergency',
                category: cat,
                title: cat === 'A' ? 'Debris Removal Emergency Work' : 'Emergency Protective Measures'
            });

            // Update project with applicant data
            try {
                const p = cmx.getProject(pid);
                if (p) {
                    p.applicant = {
                        name: document.getElementById('applicantName')?.value || '',
                        phone: document.getElementById('contactPhone')?.value || '',
                        incident: document.getElementById('incidentDate')?.value || '',
                        facility: document.getElementById('facilityName')?.value || '',
                        damage: document.getElementById('damageDescription')?.value || '',
                        priority: document.getElementById('priorityLevel')?.value || '',
                        estimatedCost: document.getElementById('estimatedCost')?.value || 0
                    };
                    p.status = 'Submitted';
                    cmx.updateProject(pid, p);
                    cmx.activity(pid, `Emergency intake submitted (Category ${cat}) - Case: EM-${Date.now()}`);
                }
            } catch (error) {
                console.error('Error updating project:', error);
            }

            setTimeout(() => {
                alert('🎯 Emergency intake submitted successfully!\n\nCase Number: EM-' + Date.now() + '\n\nYou will be contacted within 2 hours for follow-up information.');
                window.location.href = 'dashboard.html';
            }, 2000);
        });

        // Initial validation
        validateForm();

        // Hook up project creation for Emergency Intake
        document.querySelectorAll('.category-card').forEach(card => {
            const originalHandler = card.onclick;
            card.addEventListener('click', function() {
                const category = this.dataset.category;
                const title = category === 'A' ? 'Debris Removal Emergency Work' : 'Emergency Protective Measures';
                const pid = cmx.newProject({
                    type: "Emergency",
                    category: category,
                    title: title
                });
                cmx.activity(pid, `Emergency intake started (Category ${category})`);
            });
        });
    </script>

<script>
window.cmx = window.cmx || {};
(function () {
  const KEY = "cmx:projects:v1";
  const actKey = id => `cmx:activity:${id}`;
  const now = () => new Date().toISOString();

  function load() { try { return JSON.parse(localStorage.getItem(KEY)) || {}; } catch { return {}; } }
  function save(all) { localStorage.setItem(KEY, JSON.stringify(all)); }

  cmx.newProject = function newProject(init) {
    const id = (init?.id) || ("PW-" + Date.now());
    const all = load();
    all[id] = {
      id,
      createdAt: now(),
      type: init?.type || "CBCS",
      category: init?.category || null,
      title: init?.title || "Untitled Project",
      applicant: init?.applicant || {},
      cbcs: { selected: [], justification: "", evidence: [] },
      uploads: [], // {name,type,size,tag}
      costs: { labor: [], equipment: [], materials: [], contracts: [], insurance: [] },
      compliance: { ehp: {}, mitigation: {}, procurement: {} },
      status: "Draft"
    };
    save(all);
    localStorage.setItem("cmx:lastProjectId", id);
    return id;
  };

  cmx.getProject = function (id) { return load()[id || localStorage.getItem("cmx:lastProjectId")] || null; };
  cmx.updateProject = function (id, patch) {
    const all = load(); if (!all[id]) return;
    all[id] = { ...all[id], ...patch };
    save(all);
  };
  cmx.push = function (id, path, item) { // e.g., path "uploads"
    const all = load(); const p = all[id]; if (!p) return;
    (p[path] ||= []).push(item); save(all);
  };
  cmx.activity = function (id, msg) {
    const arr = JSON.parse(localStorage.getItem(actKey(id)) || "[]");
    arr.unshift({ ts: now(), msg }); // newest first
    localStorage.setItem(actKey(id), JSON.stringify(arr.slice(0, 100)));
  };
})();
</script>

    <!-- MAX ASSIST + AUTOPILOT INTEGRATION -->
    <link rel="stylesheet" href="assist/assist.css">
    <script src="assist/assist.js" defer></script>
    <script src="assist/autofill.js" defer></script>

</main>
<div id="cmx-footer"></div>

<script>
// Sign out function
function signOut() {
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('userEmail');
    localStorage.removeItem('userName');
    localStorage.removeItem('userOrganization');
    localStorage.removeItem('userSubscription');
    window.location.href = 'landing_page.html';
}

// Check authentication on page load
document.addEventListener('DOMContentLoaded', function() {
    const isAuthenticated = localStorage.getItem('isAuthenticated');
    if (!isAuthenticated || isAuthenticated !== 'true') {
        window.location.href = 'landing_page.html';
        return;
    }
    console.log('✅ Emergency Work Intake loaded - User authenticated');
});
</script>
</body>
</html>
