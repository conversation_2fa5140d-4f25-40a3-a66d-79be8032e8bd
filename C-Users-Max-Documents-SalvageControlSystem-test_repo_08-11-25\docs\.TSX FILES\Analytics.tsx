
'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { Button } from '../../../components/ui/button'
import {
  TrendingUp,
  TrendingDown,
  BarChart3, 
  PieChart, 
  Download,
  Calendar,
  Filter,
  RefreshCw
} from 'lucide-react'
import { ComplianceMetrics } from './ComplianceMetrics'
import { ProjectAnalytics } from './ProjectAnalytics'
import { DocumentAnalytics } from './DocumentAnalytics'
import { TrendAnalysis } from './TrendAnalysis'

interface AnalyticsData {
  overview: {
    totalProjects: number
    completedProjects: number
    complianceRate: number
    avgProcessingTime: number
    totalDocuments: number
    riskScore: number
  }
  trends: {
    complianceRateChange: number
    projectVolumeChange: number
    processingTimeChange: number
  }
}

const timeRanges = [
  { label: 'Last 7 days', value: '7d' },
  { label: 'Last 30 days', value: '30d' },
  { label: 'Last 90 days', value: '90d' },
  { label: 'Last 6 months', value: '6m' },
  { label: 'Last year', value: '1y' }
]

export function Analytics() {
  const [data, setData] = useState<AnalyticsData | null>(null)
  const [selectedTimeRange, setSelectedTimeRange] = useState('30d')
  const [isLoading, setIsLoading] = useState(true)
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())

  useEffect(() => {
    loadAnalyticsData()
  }, [selectedTimeRange])

  const loadAnalyticsData = async () => {
    setIsLoading(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    setData({
      overview: {
        totalProjects: 156,
        completedProjects: 142,
        complianceRate: 94.2,
        avgProcessingTime: 3.2,
        totalDocuments: 1247,
        riskScore: 12
      },
      trends: {
        complianceRateChange: 2.1,
        projectVolumeChange: 15.3,
        processingTimeChange: -8.7
      }
    })
    
    setIsLoading(false)
    setLastUpdated(new Date())
  }

  const exportReport = () => {
    // Simulate report export
    console.log('Exporting analytics report...')
  }

  if (isLoading || !data) {
    return (
      <div className="space-y-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">Analytics Dashboard</h1>
            <p className="text-white/70 mt-2">Comprehensive compliance analytics and insights</p>
          </div>
        </div>
        
        {/* Loading Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="glass border-white/20">
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-white/20 rounded w-3/4 mb-4" />
                  <div className="h-8 bg-white/20 rounded w-1/2 mb-2" />
                  <div className="h-3 bg-white/20 rounded w-2/3" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white">Analytics Dashboard</h1>
          <p className="text-white/70 mt-2">
            Comprehensive compliance analytics and insights
          </p>
          <p className="text-white/50 text-sm mt-1">
            Last updated: {lastUpdated.toLocaleString()}
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3">
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value)}
            className="px-4 py-2 bg-white/10 border border-white/20 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {timeRanges.map(range => (
              <option key={range.value} value={range.value} className="bg-gray-800">
                {range.label}
              </option>
            ))}
          </select>
          
          <Button
            onClick={loadAnalyticsData}
            variant="outline"
            className="border-white/20 text-white hover:bg-white/10"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          
          <Button
            onClick={exportReport}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="glass border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">Total Projects</p>
                <p className="text-2xl font-bold text-white">{data.overview.totalProjects}</p>
                <div className="flex items-center mt-2">
                  <TrendingUp className="w-4 h-4 text-green-400 mr-1" />
                  <span className="text-green-400 text-sm">
                    +{data.trends.projectVolumeChange}%
                  </span>
                </div>
              </div>
              <BarChart3 className="w-8 h-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">Compliance Rate</p>
                <p className="text-2xl font-bold text-white">{data.overview.complianceRate}%</p>
                <div className="flex items-center mt-2">
                  <TrendingUp className="w-4 h-4 text-green-400 mr-1" />
                  <span className="text-green-400 text-sm">
                    +{data.trends.complianceRateChange}%
                  </span>
                </div>
              </div>
              <PieChart className="w-8 h-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">Avg Processing Time</p>
                <p className="text-2xl font-bold text-white">{data.overview.avgProcessingTime}d</p>
                <div className="flex items-center mt-2">
                  <TrendingDown className="w-4 h-4 text-green-400 mr-1" />
                  <span className="text-green-400 text-sm">
                    {data.trends.processingTimeChange}%
                  </span>
                </div>
              </div>
              <Calendar className="w-8 h-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">Risk Score</p>
                <p className="text-2xl font-bold text-white">{data.overview.riskScore}</p>
                <div className="flex items-center mt-2">
                  <span className="text-green-400 text-sm">Low Risk</span>
                </div>
              </div>
              <div className="w-8 h-8 bg-green-400 rounded-full flex items-center justify-center">
                <span className="text-white text-xs font-bold">!</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Analytics Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ComplianceMetrics timeRange={selectedTimeRange} />
        <ProjectAnalytics timeRange={selectedTimeRange} />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <TrendAnalysis timeRange={selectedTimeRange} />
        </div>
        <DocumentAnalytics timeRange={selectedTimeRange} />
      </div>
    </div>
  )
}
