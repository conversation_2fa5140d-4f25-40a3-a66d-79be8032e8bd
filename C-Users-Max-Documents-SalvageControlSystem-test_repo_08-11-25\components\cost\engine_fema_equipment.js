// FEMA Equipment Rates Engine - Real Data Only
// Uses actual FEMA Schedule of Equipment Rates 2025

class FEMAEquipmentEngine {
    constructor() {
        this.equipmentRates = new Map();
        this.loaded = false;
        this.loadPromise = null;
    }

    // Load FEMA equipment rates from CSV
    async loadRates() {
        if (this.loadPromise) return this.loadPromise;
        
        this.loadPromise = this._loadRatesInternal();
        return this.loadPromise;
    }

    async _loadRatesInternal() {
        const csvPaths = [
            'costs/fema_schedule-of-equipment-rates_2025.csv',
            '/costs/fema_schedule-of-equipment-rates_2025.csv'
        ];

        for (const path of csvPaths) {
            try {
                console.log(`🔍 Loading FEMA equipment rates from ${path}...`);
                const response = await fetch(path);
                
                if (!response.ok) {
                    console.warn(`⚠️ Failed to load from ${path}: ${response.status}`);
                    continue;
                }

                const csvText = await response.text();
                this._parseCSV(csvText);
                this.loaded = true;
                
                console.log(`✅ Loaded ${this.equipmentRates.size} FEMA equipment rates`);
                return;
                
            } catch (error) {
                console.warn(`⚠️ Error loading from ${path}:`, error);
                continue;
            }
        }

        throw new Error('Failed to load FEMA equipment rates from any path');
    }

    _parseCSV(csvText) {
        const lines = csvText.split('\n');
        const headers = lines[0].split(',').map(h => h.trim());
        
        // Expected headers: Cost Code, Equipment, Manufacturer, Specification, Capacity or Size, HP, Notes, Unit, 2025 Rates
        const codeIndex = headers.findIndex(h => h.includes('Cost Code'));
        const equipmentIndex = headers.findIndex(h => h.includes('Equipment'));
        const manufacturerIndex = headers.findIndex(h => h.includes('Manufacturer'));
        const capacityIndex = headers.findIndex(h => h.includes('Capacity'));
        const unitIndex = headers.findIndex(h => h.includes('Unit'));
        const rateIndex = headers.findIndex(h => h.includes('2025 Rates'));

        for (let i = 1; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line) continue;

            const fields = this._parseCSVLine(line);
            if (fields.length < 8) continue;

            const code = fields[codeIndex]?.trim();
            const equipment = fields[equipmentIndex]?.trim();
            const manufacturer = fields[manufacturerIndex]?.trim();
            const capacity = fields[capacityIndex]?.trim();
            const unit = fields[unitIndex]?.trim();
            const rateStr = fields[rateIndex]?.trim();

            if (!code || !equipment || !rateStr) continue;

            // Parse rate (remove $ and convert to number)
            const rate = parseFloat(rateStr.replace(/[$,]/g, ''));
            if (isNaN(rate)) continue;

            this.equipmentRates.set(code, {
                code,
                equipment,
                manufacturer,
                capacity,
                unit: unit || 'hour',
                ratePerHour: rate,
                description: `${equipment} ${manufacturer ? '- ' + manufacturer : ''} ${capacity ? '(' + capacity + ')' : ''}`.trim()
            });
        }
    }

    _parseCSVLine(line) {
        const fields = [];
        let current = '';
        let inQuotes = false;
        
        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            
            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                fields.push(current);
                current = '';
            } else {
                current += char;
            }
        }
        
        fields.push(current);
        return fields;
    }

    // Lookup equipment by code
    async getEquipmentByCode(code) {
        if (!this.loaded) {
            await this.loadRates();
        }
        
        return this.equipmentRates.get(code) || null;
    }

    // Search equipment by name/description
    async searchEquipment(query) {
        if (!this.loaded) {
            await this.loadRates();
        }

        const results = [];
        const queryLower = query.toLowerCase();

        for (const [code, equipment] of this.equipmentRates) {
            if (equipment.equipment.toLowerCase().includes(queryLower) ||
                equipment.description.toLowerCase().includes(queryLower) ||
                (equipment.manufacturer && equipment.manufacturer.toLowerCase().includes(queryLower))) {
                results.push(equipment);
            }
        }

        return results.slice(0, 20); // Limit to 20 results
    }

    // Get all equipment codes
    async getAllCodes() {
        if (!this.loaded) {
            await this.loadRates();
        }
        
        return Array.from(this.equipmentRates.keys()).sort();
    }

    // Get equipment by category (based on code ranges)
    async getEquipmentByCategory(category) {
        if (!this.loaded) {
            await this.loadRates();
        }

        const results = [];
        let codePrefix = '';

        // FEMA equipment code categories
        switch (category.toLowerCase()) {
            case 'air_compressor':
                codePrefix = '801';
                break;
            case 'crane':
                codePrefix = '802';
                break;
            case 'dozer':
                codePrefix = '803';
                break;
            case 'excavator':
                codePrefix = '804';
                break;
            case 'generator':
                codePrefix = '805';
                break;
            case 'loader':
                codePrefix = '806';
                break;
            case 'truck':
                codePrefix = '807';
                break;
            default:
                return results;
        }

        for (const [code, equipment] of this.equipmentRates) {
            if (code.startsWith(codePrefix)) {
                results.push(equipment);
            }
        }

        return results;
    }

    // Calculate equipment cost
    calculateEquipmentCost(code, hours, quantity = 1) {
        const equipment = this.equipmentRates.get(code);
        if (!equipment) {
            throw new Error(`Equipment code ${code} not found`);
        }

        const totalCost = equipment.ratePerHour * hours * quantity;
        
        return {
            equipment: equipment.description,
            code,
            ratePerHour: equipment.ratePerHour,
            hours,
            quantity,
            totalCost,
            unit: equipment.unit
        };
    }

    // Get sample data for testing
    async getSampleEquipment(count = 5) {
        if (!this.loaded) {
            await this.loadRates();
        }

        const allCodes = Array.from(this.equipmentRates.keys());
        const sampleCodes = allCodes.slice(0, count);
        
        return sampleCodes.map(code => this.equipmentRates.get(code));
    }
}

// Global instance
window.FEMAEquipmentEngine = window.FEMAEquipmentEngine || new FEMAEquipmentEngine();

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FEMAEquipmentEngine;
}
