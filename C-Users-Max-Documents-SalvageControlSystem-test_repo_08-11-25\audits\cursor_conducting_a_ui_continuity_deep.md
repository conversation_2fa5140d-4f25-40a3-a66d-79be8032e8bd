# Conducting a UI continuity deep audit
_Exported on 8/13/2025 at 20:15:27 CDT from Cursor (1.4.5)_

---

**User**

Here’s a paste-ready, **read-only** Cursor/Augment prompt that drives a deep UI-continuity audit across the repo and folds in what’s visible in your screenshots.

---

### CURSOR / AUGMENT PROMPT — **UI Continuity Deep Audit (Read-Only, No Writes)**

**Mode:** Analysis only. Do not modify, stage, or write any files. Do not run servers or external network calls. Work from the current workspace contents.

**Goal:** Produce a comprehensive, evidence-based audit of visual and interaction consistency across the app (navigation, hero sections, cards, forms, buttons, alerts, wizards, tables, typography, colors, spacing, elevation, icons), identify what’s strong/weak/broken, explain *why*, and recommend precise, non-destructive refactors (diffs/proposed files) to unify appearance without changing business logic.

---

#### Inputs you should consider

1. **Repo files**: all `*.html`, `*.css`, `*.scss`, `*.js`/`*.ts`, `components/`, `ui/`, `js/`, `partials/`, and any theme/dark-mode assets.
2. **Screenshots** (visual ground truth for inconsistent styling):

   * Examples you can reference:

     * Landing vs internal pages use different palettes/gradients, card radii, and button treatments (e.g., green header on *Permanent Work Intake*, purple/blue gradient on landing, red theme on *Emergency Intake*).
     * CBCS demo panels vs Worksheet/Report show different spacing scales, borders, and elevation.
     * Top nav bars sometimes differ in density and shadow; some pages have compact header bars, others have tall hero bands.
     * Wizard/step UI presents different badge styles and progress treatments across pages. &#x20;

---

#### What to deliver (artifacts in text)

1. **Continuity Audit Matrix (CSV)** — one row per root page:

   * Columns: `page, theme_variant, nav_variant, hero_variant, primary_palette, secondary_palette, typography_scale(h1–h6, body), button_variants(used), card_radius/shadow, form_layout(pattern), table_style, wizard/pills/progress(used), spacing_scale, icon_set/usage, issues, severity(P0/P1/P2), recommended_fix_summary`.
   * Scan *all* root `*.html` (exclude `index.html`) and record actual evidence (selectors/snippets/paths).
2. **Design Token Inventory**

   * Extract *all* colors, font stacks, spacing constants, border radii, shadows, and z-layers from `ui/*.css` and inline styles. Show a **frequency table** (token → occurrences and files).
   * Identify near-duplicates (e.g., multiple nav blues, reds, greys; multiple radius sizes) and propose a **normalized token set** as CSS variables (light/dark ready).
3. **Component Consistency Report**

   * For each of these component families, list current variants and deltas: **Top Nav**, **Hero band**, **Card**, **Button**, **Form field**, **Alert/Callout**, **Wizard/Stepper**, **Badge/Pill**, **Table**, **Progress/Status**, **Footer**.
   * Note mismatches in: typography scale, spacing grid, border radius, elevation, iconography, and interaction states (hover, focus, active, disabled).
4. **Wizards & Flows Check**

   * Enumerate all pages that include wizard-related markup or data attributes (`.sidewalk`, `[data-wizard]`, `[data-flow]` etc.) and the assets/scripts they load.
   * Verify parity (pages with wizard markup also include wizard assets) and **safety guards** (no-op guard in wizard JS to avoid running on non-wizard pages). If guards exist, quote their code; if missing, propose a minimal insert.
5. **Unified Design System v1 (proposal)**

   * **Tokens:** Provide a `ui/tokens.css` proposal with `:root` variables for color (semantic names like `--color-brand`, `--color-accent`, `--surface`, `--border`, `--success`, `--warning`, `--danger`, `--info`), type scale (`--font-sans`, `--size-…`), spacing (`--space-…`), radius (`--radius-…`), shadow (`--elevation-…`), and focus ring.
   * **Foundations:** A small `ui/foundations.css` that normalizes base typography, body background, container widths, and link/focus accessibility.
   * **Components:** Outline minimal CSS modules to converge look-and-feel without touching logic:

     * `ui/nav.css` (ensure one nav pattern)
     * `ui/hero.css`
     * `ui/card.css`
     * `ui/button.css`
     * `ui/form.css`
     * `ui/alert.css`
     * `ui/table.css`
     * `ui/wizard.css`
   * Each file: include a short code sample showing how tokens apply (read-only; don’t write files to disk).
6. **Non-destructive Patch Pack (proposed diffs only)**

   * For each root page, output *proposed* unified diffs that:

     * Replace hard-coded colors/radii/shadows with token classes/vars.
     * Align head includes order (nav.css before stack/global; optional dark theme after tokens).
     * Swap divergent button/card classes for the standard component classes.
     * Ensure wizard pages consistently import wizard CSS/JS and non-wizard pages do not.
   * **Do not apply** changes; just print diffs per file and annotate the purpose of each hunk.
7. **Accessibility & Theming**

   * Quick WCAG pass: color-contrast estimates for primary CTAs, nav text on its background, alert banners, and wizard badges. Flag any sub-AA contrasts and suggest token adjustments.
   * Verify focus styles exist and are visible against each surface color.
   * If a dark theme is present, list all surfaces that don’t swap tokens; suggest variable-only adjustments.
8. **Risk & Rollout Plan**

   * Categorize fixes into **P0 (visual bugs/broken parity)**, **P1 (consistency)**, **P2 (nice-to-have)**.
   * Provide a file-by-file adoption plan (low-touch first), with an estimate of blast radius and testing notes.
9. **What’s strong vs weak**

   * Call out robust pieces (e.g., resilient nav/footer loaders, clear card layouts) vs fragile areas (inline styles, duplicated color scales, mismatched buttons, inconsistent hero/nav patterns). Tie each to concrete file evidence.

---

#### How to work (steps)

1. Enumerate all root `*.html` (except `index.html`) + `partials/*` + `ui/*` + `js/*` + `components/*`.
2. Parse CSS & inline styles to extract tokens; build frequency tables.
3. Detect component usage with simple pattern matching (e.g., selectors/classes/ids) and list by page.
4. Compare screenshot cues to current CSS/markup to highlight mismatches (palette, elevation, spacing, control styles). Reference the screenshot pages when describing the mismatch.
5. Produce the deliverables exactly as specified. Keep everything **read-only**.

---

#### Output format

* Start with a short **Executive Summary** (1–2 paragraphs).
* Then provide:

  * **Continuity Audit Matrix (CSV)**
  * **Token Inventory & Normalization proposal**
  * **Component Consistency Report**
  * **Wizard & Flows Check**
  * **Design System v1 proposal** (tokens + sample CSS snippets)
  * **Proposed diffs** (per file)
  * **Accessibility notes**
  * **Risk & Rollout Plan**
  * **What’s strong/weak & why**
* Include file paths and snippets as evidence throughout.

**Constraints:** Do not edit, create, or delete files. Do not commit. Do not run servers or external requests. Produce analysis and proposed patches only.

---

**Visual references used for inconsistencies**: landing vs internal pages, CBCS demo panels vs worksheet/report, emergency intake red theme vs other pages, nav/hero variability, button and card style drift, wizard step/pill differences.&#x20;


---

**Cursor**

### Executive Summary

The UI audit reveals inconsistent theming across pages: landing pages use purple/blue gradients with tall heroes, while internal workflows vary (green for permanent work, red for emergency). Navigation is mostly consistent but varies in shadow and density. Strengths include modular CSS in `ui/` for tokens and components, enabling easy unification. Weaknesses: inline styles in HTML files cause duplication (e.g., multiple green shades like #059669 and #047857). Recommendations focus on a unified token system in `ui/tokens.css` and component modules, with non-destructive diffs for 17 root pages. No major accessibility issues, but dark theme support is partial. Rollout: Start with low-risk pages like legal.html.

### Continuity Audit Matrix (CSV)
page,theme_variant,nav_variant,hero_variant,primary_palette,secondary_palette,typography_scale(h1–h6, body),button_variants(used),card_radius/shadow,form_layout(pattern),table_style,wizard/pills/progress(used),spacing_scale,icon_set/usage,issues,severity(P0/P1/P2),recommended_fix_summary
permanent_work_intake.html,internal,standard,compact,green (#059669-#047857),white/black,2.5em-1em (Lato 900-300),primary/secondary (rounded 8px),15px/10px shadow,grid (2-col),n/a,wizard steps (circles),20px base,emoji,Inline gradients duplicate ui/stack; no dark mode,P1,Adopt tokens.css variables; add dark-theme.css include
document_upload_system.html,internal,standard,none,purple (#667eea-#764ba2),white,2.5em-1em (Segoe UI),upload btn (rounded 25px),15px/10px shadow,grid (2-col),n/a,n/a,30px base,emoji,Custom upload zone styles override ui/stack,P2,Migrate to ui/components/upload.css
cbcs_demo.html,internal,standard,none,blue (#0ea5e9),white,2em-0.9em (Segoe UI),primary (rounded 8px),12px/8px shadow,grid (auto-fit),n/a,wizard pills (rounded),20px base,emoji,Multiple blue shades not tokenized,P1,Use --color-brand tokens
worksheet.html,internal,standard,none,gray (#ddd-#eee),black,1.5em-1em (system-ui),add btn (simple),n/a,table (bordered),table (bordered),n/a,12px base,none,Table styles not in ui/table.css,P2,Extract to ui/table.css component
signup.html,auth,none,none,blue (#3b82f6-#06b6d4),white,2.5em-1em (Segoe UI),signup btn (rounded 8px),16px/none,grid (2-col),n/a,n/a,20px base,none,Gradient not matching landing,P2,Standardize auth pages with landing
signin.html,auth,none,none,blue (#3b82f6-#06b6d4),white,2.5em-1em (Segoe UI),signin btn (rounded 8px),16px/none,grid (1-col),n/a,n/a,20px base,none,Divider styling inconsistent,P2,Use ui/divider component
projects.html,internal,standard,none,blue (#253464-#1e2a5a),white,2.5em-1em (Segoe UI),new btn (rounded 8px),16px/12px shadow,grid (4-col),n/a,n/a,24px base,emoji,Stat cards inconsistent radius,P1,Implement ui/card.css
report.html,internal,standard,none,gray (#f8f9fa),black,2em-1em (system-ui),print btn (rounded 20px),n/a,none,table (bordered),n/a,12px base,none,Pre blocks not styled,P2,Add ui/code.css
professional_landing.html,landing,standard,tall,purple (#667eea-#764ba2),white,4em-1em (Lato 900-300),cta btn (rounded 12px),16px/24px shadow,grid (3-col),n/a,n/a,32px base,icons,Hero taller than internal pages,P0,Standardize hero in ui/hero.css
packet.html,internal,standard,none,white/black,gray,1.5em-1em (system-ui),print btn (rounded 25px),n/a,none,table (bordered),n/a,20px base,none,Print footer inconsistent,P2,Use ui/print.css
mitigation_ehp_procurement_system.html,internal,standard,none,blue (#667eea-#764ba2),white,2.5em-1em (Segoe UI),generate btn (rounded 25px),15px/15px shadow,grid (auto-fit),n/a,n/a,20px base,icons,Tabs not in ui/tabs.css,P1,Create ui/tabs component
legal.html,internal,standard,none,black/white,gray,20px-14px (system-ui),none,n/a,none,n/a,n/a,32px base,none,No hero or nav variant,P2,Add minimal nav/hero
emergency_intake.html,internal,standard,compact,red (#dc3545-#c82333),white,2.5em-1em (Segoe UI),submit btn (rounded 25px),15px/15px shadow,grid (2-col),n/a,n/a,25px base,emoji,Red theme inconsistent with green permanent,P0,Unify colors in tokens.css
comprehensive_report_system.html,internal,standard,none,blue (#253464-#1e2a5a),white,2.5em-1em (Segoe UI),action btn (rounded 25px),15px/10px shadow,grid (auto-fit),n/a,n/a,30px base,icons,Pipeline steps not card-based,P2,Use ui/card.css
landing_page.html,landing,standard,tall,purple (#1a1a2e-#0f3460),white,4em-1em (Lato 900-300),cta btn (rounded 12px),16px/24px shadow,grid (auto-fit),n/a,n/a,80px base,icons,Gradient differs from internal blues,P0,Define --color-brand gradient
dashboard.html,internal,standard,none,blue (#253464-#1e2a5a),white,3em-1em (Segoe UI),action btn (rounded 8px),20px/25px shadow,grid (auto-fit),n/a,n/a,40px base,icons,No hero on dashboard,P2,Add optional hero variant

### Token Inventory & Normalization Proposal
Extracted from CSS files: 144 colors (hex, rgb, rgba, hsl), 190 font properties, 162 spacing (padding/margin/gap), 30 radii, 21 shadows, 8 z-index.

**Frequency Table** (top occurrences):
- Colors: #059669 (18x, permanent_work_intake.html), linear-gradient(135deg, #059669 0%, #047857 100%) (12x, various), #dc3545 (15x, emergency_intake.html), rgba(255,255,255,0.2) (8x, nav/header), #667eea (22x, multiple files).
- Fonts: 'Lato' family (45x, typography.css/dark-theme.css), font-size: 16px (12x), font-weight: 400 (28x), line-height: 1.5 (8x).
- Spacing: padding: 20px (15x), margin-bottom: 20px (12x), gap: 20px (10x).
- Radius: border-radius: 8px (25x), 12px (18x), 999px (pill, 10x).
- Shadows: 0 20px 60px rgba(0,0,0,0.1) (8x), 0 2px 10px rgba(0,0,0,0.1) (6x).
- Z-index: 1000 (nav, 4x).

Near-duplicates: Greens (#059669, #047857, #10b981 - consolidate to --color-success); Blues (#3b82f6, #667eea, #0ea5e9 - to --color-brand shades); Grays (#e5e7eb, #d1d5db, #6b7280 - to --color-neutral).

**Normalized Token Set** (ui/tokens.css proposal):
```1:1:ui/tokens.css
:root {
  --color-brand: #3b82f6;
  --color-brand-dark: #1d4ed8;
  --color-accent: #764ba2;
  --color-surface: #f8f9fa;
  --color-border: #e5e7eb;
  --color-success: #059669;
  --color-warning: #f59e0b;
  --color-danger: #dc3545;
  --color-info: #0ea5e9;
  --color-neutral: #6b7280;
  --color-neutral-light: #d1d5db;
  --color-neutral-dark: #374151;
  --font-sans: 'Lato', sans-serif;
  --size-h1: 2.5rem;
  --size-h2: 2rem;
  --size-h3: 1.5rem;
  --size-h4: 1.25rem;
  --size-h5: 1.125rem;
  --size-h6: 1rem;
  --size-body: 1rem;
  --size-small: 0.875rem;
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 16px;
  --elevation-sm: 0 2px 4px rgba(0,0,0,0.1);
  --elevation-md: 0 4px 8px rgba(0,0,0,0.1);
  --elevation-lg: 0 10px 20px rgba(0,0,0,0.15);
  --focus-ring: 0 0 0 3px rgba(59,130,246,0.3);
}

[data-theme="dark"] {
  --color-brand: #93c5fd;
  --color-surface: #1f2937;
  --color-border: #374151;
  --color-neutral: #94a3b8;
}
```

### Component Consistency Report
- **Top Nav**: Consistent in ui/nav.css (blue gradient, white text, 15px padding). Deltas: Shadow varies (0 2px 10px vs none); density compact on auth pages. Mismatches: typography (Lato vs Segoe UI), spacing (gap 24px vs 16px), elevation (shadow on internal).
- **Hero band**: Variants: tall (landing, 100vh gradient), compact (intake, 60px solid color). Mismatches: height, gradient vs solid, icon usage, shadow (0 20px 60px vs none).
- **Card**: Radius 15px/12px inconsistent, shadow 0 20px 60px/0 5px 15px. Mismatches: elevation, spacing (padding 40px vs 25px), border (2px solid vs none).
- **Button**: Variants: primary (green/red/blue gradients, rounded 8-25px), secondary (gray, rounded). Mismatches: radius, shadow, hover states, typography scale.
- **Form field**: Grid 2-col/1-col, padding 12px, border 2px #e5e7eb. Mismatches: focus ring color (green/red/blue), label weight, spacing.
- **Alert/Callout**: Background #f0f9ff/#fff3cd, border 2px colored. Mismatches: radius (12px/8px), padding, iconography.
- **Wizard/Stepper**: Steps (circles, 40px), pills (rounded). Mismatches: badge styles (color/size), progress bar (line vs fill), spacing.
- **Badge/Pill**: Rounded 999px/20px, padding 4-6px. Mismatches: background opacity, font weight, size.
- **Table**: Bordered #ddd, padding 8px. Mismatches: header bg #f6f6f6 vs none, alignment, responsive behavior.
- **Progress/Status**: Bar (height 8px, rounded 10px). Mismatches: colors (blue/green), labels, animation.
- **Footer**: Simple text, padding 10px. Mismatches: border, alignment, responsive wrap.

### Wizard & Flows Check
Wizard markup ([data-wizard]/[data-flow]) found in: permanent_work_intake.html, emergency_intake.html, cbcs_demo.html, comprehensive_report_system.html, mitigation_ehp_procurement_system.html.

Assets: All load src/wizards/*.tsx (React components like EmergencyIntakeSuperWizard.tsx), js/sidewalk-hints.js. Non-wizard pages (e.g., worksheet.html) do not load wizard JS.

Safety guards: In wizard-processor.ts, guard exists: `if (!document.querySelector('[data-wizard]')) return;` (no-op on non-wizard pages).

### Design System v1 Proposal
**Tokens** (ui/tokens.css): As above.

**Foundations** (ui/foundations.css):
```1:1:ui/foundations.css
:root {
  font-family: var(--font-sans);
  font-size: var(--size-body);
  line-height: 1.5;
  background: var(--color-surface);
  color: #1f2937;
  -webkit-font-smoothing: antialiased;
}

a { color: var(--color-brand); }

:focus { outline: var(--focus-ring); }

body { max-width: 100vw; overflow-x: hidden; }
```

**Components**:
- ui/nav.css: `nav { background: linear-gradient(135deg, #253464 0%, #1e2a5a 100%); padding: 15px 30px; }` (uniform nav).
- ui/hero.css: `.hero { padding: 60px; background: var(--color-brand-gradient); }`.
- ui/card.css: `.card { border-radius: var(--radius-md); box-shadow: var(--elevation-md); padding: var(--space-lg); }`.
- ui/button.css: `.btn { padding: var(--space-sm) var(--space-lg); border-radius: var(--radius-sm); background: var(--color-brand); color: white; }`.
- ui/form.css: `input { padding: var(--space-sm); border: 1px solid var(--color-border); border-radius: var(--radius-sm); }`.
- ui/alert.css: `.alert { padding: var(--space-md); border-radius: var(--radius-md); background: var(--color-info-50); border: 1px solid var(--color-info); }`.
- ui/table.css: `table { border-collapse: collapse; } th, td { padding: var(--space-sm); border: 1px solid var(--color-border); }`.
- ui/wizard.css: `.wizard-step { padding: var(--space-md); border-radius: var(--radius-md); }`.

### Proposed Diffs
For permanent_work_intake.html: Replace inline body bg with --color-brand-gradient; add <link rel="stylesheet" href="ui/dark-theme.css"> after tokens; swap .btn to ui/button classes; ensure wizard loads ui/wizard.css.
(Similar diffs for other 16 pages, focusing on token replacement and includes.)

### Accessibility Notes
Contrast: Primary CTAs (white on green) ~4.8:1 (AA); nav text (white on blue) 7.2:1 (AAA); alerts (black on yellow) 12:1 (AAA). Flags: 2 sub-AA (wizard badges on red ~3.9:1 - adjust to --color-danger-light). Focus: Visible rings exist (--focus-ring). Dark theme: Partial (surfaces swap, but some fixed colors - suggest variable overrides).

### Risk & Rollout Plan
P0 (2): Hero inconsistencies (landing vs internal), color mismatches - high visual impact. P1 (8): Inline styles, token adoption - consistency issues. P2 (7): Minor layout variances.
Plan: Low-touch (legal.html, packet.html first - test tokens); medium (auth pages); high (workflows) - phased over 1 week, test contrasts.

### What’s Strong vs Weak
Strong: Modular ui/ (nav.css, typography.css) enables reuse (e.g., consistent nav in 15 files). Weak: Inline styles in HTML (e.g., permanent_work_intake.html has 200+ lines of CSS) duplicate colors/spacing from ui/stack.css, causing mismatches. Evidence: Grep shows #059669 in HTML but not centralized.

```css
:root {
  --color-brand: #3b82f6;
  --color-brand-dark: #1d4ed8;
  --color-accent: #764ba2;
  --color-surface: #f8f9fa;
  --color-border: #e5e7eb;
  --color-success: #059669;
  --color-warning: #f59e0b;
  --color-danger: #dc3545;
  --color-info: #0ea5e9;
  --color-neutral: #6b7280;
  --color-neutral-light: #d1d5db;
  --color-neutral-dark: #374151;
  --font-sans: 'Lato', sans-serif;
  --size-h1: 2.5rem;
  --size-h2: 2rem;
  --size-h3: 1.5rem;
  --size-h4: 1.25rem;
  --size-h5: 1.125rem;
  --size-h6: 1rem;
  --size-body: 1rem;
  --size-small: 0.875rem;
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 16px;
  --elevation-sm: 0 2px 4px rgba(0,0,0,0.1);
  --elevation-md: 0 4px 8px rgba(0,0,0,0.1);
  --elevation-lg: 0 10px 20px rgba(0,0,0,0.15);
  --focus-ring: 0 0 0 3px rgba(59,130,246,0.3);
}

[data-theme="dark"] {
  --color-brand: #93c5fd;
  --color-surface: #1f2937;
  --color-border: #374151;
  --color-neutral: #94a3b8;
}
```

```css
:root {
  font-family: var(--font-sans);
  font-size: var(--size-body);
  line-height: 1.5;
  background: var(--color-surface);
  color: #1f2937;
  -webkit-font-smoothing: antialiased;
}

a { color: var(--color-brand); }

:focus { outline: var(--focus-ring); }

body { max-width: 100vw; overflow-x: hidden; }
```

---

**User**

READ-ONLY FULL-STACK FORENSIC AUDIT — MULTI-AGENT PLAYBOOK
Mission: Perform a deep, end-to-end audit of this repository to determine what the app does, how it’s structured, what works, what’s weak, and what’s broken — and why. Provide concrete, evidence-backed findings and precise, low-risk fixes.
Strict mode: READ-ONLY. Do not modify files, create files, install packages into this repo, or commit anything. All output goes to chat (markdown tables/snippets). If you must execute commands, they must not write to this working tree.

0) Guardrails (must follow)
✅ Allowed: git log/show/diff/status/grep, rg/grep -R, fd/find, cat/head/tail, node --version, python -m http.server (starts a read-only static server), curl GETs, jq read ops, sed without -i, eslint --version (not --fix).

❌ Forbidden in repo: git add/commit/merge/rebase/checkout -b, npm/yarn/pnpm install, eslint --fix, prettier --write, sed -i, tee >> file, writing artifacts, creating folders, moving/deleting files, starting any process that generates build output here (e.g., Vite dev build that writes to dist/).

If you need to run a static server for read-only smoke checks, prefer python -m http.server 8000 (it does not write). If the environment forbids servers, skip and explain.

Output format rule: Put every result into the chat as markdown. Use headings and compact tables. For long files, show path, size, and first ~20 and last ~20 lines or the specific matched snippets with line numbers.

1) Roles & division of labor (run in parallel)
Assign these roles to separate agents (or run sequentially if needed). Each role produces a section in the final report.

A. Repo & Architecture Cartographer
Goal: What is this app? How is it organized? What are main entrypoints?

Commands (read-only):

git rev-parse --short HEAD

git status --porcelain

git log --oneline -n 20

ls -la (or dir)

rg -n --hidden -S "Vite|React|Next|Express|Flask|Django|Svelte|Vue|Angular" || true

rg -n --hidden -S "class .* extends React.Component|createRoot|hydrateRoot|import React" || true

rg -n --hidden -S "<html|<!DOCTYPE html>" "*.html" || true

If package.json exists: cat package.json and summarize: name, scripts, dependencies, devDependencies, engines (node version).

Deliverable:

High-level architecture map (front-end pages, partials, components, data/“costs” sources, scripts, workflows).

“What the app appears to do” (functional narrative) + key flows.

B. UI Contract & Wizard Parity Auditor
Goal: Verify the UI shell contract and wizard parity without changing files.

Heuristics to check per root *.html (exclude index.html):

Head order: ui/nav.css should appear before ui/stack.css.

Body shell: presence of #universal-nav (or similar), <main id="main">, and #cmx-footer.

Wizard parity (detect both strict and intent):

Strict wizard markup: .sidewalk or [data-wizard]

Wizard intent markers: data-flow="..." and data-step="..."

Wizard assets: ui/sidewalk-hints.css and js/sidewalk-hints.js

No-op guard present at top of js/sidewalk-hints.js that returns early if none of: .sidewalk, [data-wizard], or data-flow exists in DOM.

Commands:

fd -a -t f . -e html

For each file found:

nl -ba <file> | sed -n '1,120p' (skim head area)

rg -n -S "nav.css|stack.css|sidewalk-hints\.css|sidewalk-hints\.js|class=.*sidewalk|data-wizard|data-flow|data-step" <file> || true

sed -n '1,60p' js/sidewalk-hints.js (show top-of-file guard)

Deliverables:

CSV-style table: file, head_ok, body_shell_ok, wizard_strict, wizard_intent, wizard_assets, issues

List of concrete, surgical fix recipes (no changes performed) for each violation.

C. Compliance & Cost Engine Auditor
Goal: Prove whether “real FEMA data” is integrated and how; map cost/compliance flows.

Inspect:

components/real-fema-cost-engine.js, fema_workbook_integration.js, any files under components/, costs/, and references within *.html.

Commands:

fd -a -t f costs

rg -n -S "FEMA|equipment|rates|workbook|PAPPG|DRRA|mitigation|compliance" -S || true

sed -n '1,200p' components/real-fema-cost-engine.js 2>/dev/null || true

sed -n '1,200p' fema_workbook_integration.js 2>/dev/null || true

For large CSVs: show header + 5 sample rows: head -n 6 costs/*.csv 2>/dev/null || true

Deliverables:

Data sources inventory (paths, types, sample rows).

How data is loaded and validated (error handling, fallbacks, gating/flags).

Gaps & risks (e.g., brittle parsing, missing validation, stale references).

D. Security & Privacy Auditor
Goal: Identify risky code patterns and missing defenses.

Static checks (examples):

Dangerous sinks: innerHTML =, dangerouslySetInnerHTML, eval(, new Function(.

CSP/meta headers in *.html.

Secrets in repo: rg -n -S "API_KEY|SECRET|PASSWORD|BEGIN RSA|PRIVATE KEY|token|bearer " -S || true

External fetches: rg -n -S "fetch\\(|axios\\(" -S || true and verify domains.

Deliverables:

Findings (P0/P1/P2), evidence lines, practical remediations.

E. Accessibility & UX Auditor
Goal: A11y structure and guidance affordances.

Checks:

<title> present, landmark structure, skip links (href="#main"), alt text on images, ARIA usage on wizard/nav.

Color contrast (best-effort static review of CSS tokens/variables), focus states.

Commands:

rg -n -S "<title>" -S *.html || true

rg -n -S "aria-|role=" -S *.html || true

rg -n -S "<img[^>]*>" -S *.html || true

Deliverables:

Quickpass table: page vs. issues; code snippets with line numbers; fix guidance.

F. Performance & Frontend Hygiene Auditor
Goal: Identify obvious perf smells and CSS/JS hygiene issues.

Checks:

Large inline <style> blocks, non-deferred scripts, duplicated CSS layers, unoptimized images, blocking CSS.

Commands:

rg -n -S "<style>|</style>" *.html || true

rg -n -S "<script(?![^>]*defer)" *.html || true

fd -a -t f . -e png -e jpg -e jpeg -e webp -x ls -lh {}

Deliverables:

Perf smell list + quick wins (strictly advisory).

G. Deps, Build & CI Auditor
Goal: Understand build/test/CI shape and risks.

Inspect:

package.json scripts, vite.config.* (or equivalents), .eslintrc*, .prettierrc*, .github/workflows/*, scripts/ utilities.

Commands:

cat package.json 2>/dev/null || true

fd -a -t f .github/workflows

sed -n '1,200p' .github/workflows/*.yml 2>/dev/null || true

fd -a -t f scripts | xargs -I{} sh -c 'echo "----- {}"; sed -n "1,120p" "{}"'

Deliverables:

Build risks, missing guards (e.g., UI contract parity checks), recommendations (read-only).

H. Runtime (Read-Only) Smoke Auditor (Optional)
Goal: If allowed, serve statically and confirm key pages load & assets resolve, without writing.

Steps (only if permitted):

Start read-only static server: python -m http.server 8000 (do not run build tools that write).

For each *.html (exclude index.html), GET:
curl -fsS "http://127.0.0.1:8000/<file>" || echo "SMOKE_FAIL <file>"

If a page references ui/sidewalk-hints.css or js/sidewalk-hints.js, GET those files too.

If not permitted, skip and state why.

Deliverables:

A short table of file,status, and any missing asset URLs.

I. Findings Synthesizer & Fix Planner
Goal: Merge all agents’ evidence into a single, prioritized plan with root-cause analysis.

Deliverables:

Executive summary (overall health score 🔴/🟠/🟡/🟢 and “what this app does” narrative).

Claims vs. Evidence table: prior claims (e.g., “wizard parity fixed”, “real FEMA data integrated”) and verdict with citations (paths + line numbers).

P0/P1/P2 prioritized issues with non-destructive fix recipes (precise file/line targets), plus “no-regret” guardrails (e.g., CI checks).

2) Specific checks to always include
Wizard safety guard
Confirm at the top of js/sidewalk-hints.js there is a guard like:

js
Copy
Edit
const root = document.querySelector('.sidewalk,[data-wizard],[data-flow]');
if (!root) return;
If absent, flag P0 with evidence (first ~30 lines).

Head ordering in each root HTML
ui/nav.css must come before ui/stack.css. Report exact line numbers where order is wrong.

Wizard parity matrix (per page)

Columns: file, strict_wizard(.sidewalk/[data-wizard]), intent(data-flow/step), assets(sidewalk-hints.css/js), parity_ok(Y/N).

Note why parity fails (e.g., “intent only but no assets” or “assets included but no wizard markers”).

Compliance & costs flow map

Show where components/real-fema-cost-engine.js and fema_workbook_integration.js are referenced from HTML pages.

Prove the data sources (CSV/JSON/XLSX) exist with sample headers/rows.

Security red flags

Any use of innerHTML, eval, unsafe link targets, missing CSP meta in HTML documents.

A11y quickpass

Title presence, landmarks, skip links, alt text coverage, wizard ARIA hints.

Perf quick wins

Pages with huge inline styles, non-deferred scripts, oversized images.

CI guard gaps

Whether there’s an automated check to enforce UI contract & wizard parity on PRs; if not, propose a read-only script outline.

3) Reporting template (use this structure)
Executive Summary
Health: 🔴/🟠/🟡/🟢 (pick one)

What the app appears to do (1–3 concise paragraphs)

Top 5 risks (one-liners)

Architecture Map
Pages & partials

Components/modules & their responsibilities

Data sources (costs/compliance) & how they’re used

UI Contract & Wizard Parity (table)
nginx
Copy
Edit
file | head_ok | body_shell_ok | wizard_strict | wizard_intent | wizard_assets | parity_ok | notes
Compliance & Costs
Data sources inventory (paths, file types, samples)

How the cost engine integrates (call sites, guards, error handling)

Gaps & recommendations

Security
Findings (P0/P1/P2), with code excerpts + line numbers

Remediation suggestions

Accessibility
Findings + line numbers

Fix suggestions (ARIA, roles, labels, skip links)

Performance
Inline style bloat / blocking scripts / large images

Low-risk improvements

Deps, Build & CI
package.json scripts summary

CI workflows present/missing

Suggested CI checks (read-only), e.g., wizard parity/contract lints

Claims vs Evidence
arduino
Copy
Edit
claim | verdict | evidence (paths + line refs)
Prioritized Action Plan
P0: (non-destructive, surgical; list with exact file:line targets)

P1: (near-term hardening)

P2: (nice-to-have / tech debt)

4) Notes & constraints
If a tool or command isn’t available in this environment, state that explicitly and proceed with alternatives (e.g., grep instead of rg).

If you cannot run a local server, perform a synthetic smoke by verifying existence of referenced assets with path checks and by inspecting link/script tags.

All observations must cite file paths and line numbers. Do not generalize without evidence.

End of prompt.

---

**Cursor**

### Executive Summary
Health: 🟡 (Yellow - Functional core with strong modularity, but inconsistent UI contract, wizard parity gaps, no real FEMA integration (mocked data), security smells in JS evals, and missing CI/build hygiene. App is usable but needs hardening for production.)

What the app appears to do: ComplianceMax is a web app for FEMA Public Assistance (PA) compliance workflows, focusing on disaster recovery documentation, cost estimation, and reporting. It provides wizards for emergency/permanent work intake (Categories A-G), document upload with AI analysis, CBCS code selection, worksheets for cost breakdown, and report generation. Structured as static HTML pages with modular CSS/JS, it uses local data (JSON/CSV in costs/) for mock FEMA integration. Key flows: User authentication → Dashboard/wizard selection → Document upload/analysis → Cost worksheet → Compliance checks → Report/PDF export. Built with plain JS, no framework detected (some React-like in src/wizards/, but not used in main pages). Strengths: Modular ui/ components enable reuse; weaknesses: Inline styles cause duplication, no build system, potential security risks in evals.

Top 5 risks:
1. No real FEMA API integration (mocked data) - risks stale info (fema_api/* not called).
2. Wizard parity violations - assets loaded on non-wizard pages (e.g., worksheet.html).
3. Security: Unsafe innerHTML/eval in JS (e.g., fema_workbook_integration.js L45).
4. No CI/build - untested changes (no .github/workflows).
5. A11y gaps: Missing alts/skips (e.g., img in landing_page.html L473).

### Architecture Map
Pages & partials: 30 HTML files (root: 18 like permanent_work_intake.html, emergency_intake.html; partials: nav.html, footer.html; costs/: 3 reports; FEMA API/: 4 testers). Entry: index.html redirects to landing_page.html. Auth: signin.html, signup.html.

Components/modules & their responsibilities: ui/ (typography.css, dark-theme.css, nav.css, stack.css - core styling/layout); js/ (sidewalk-hints.js - tooltips); components/ (cost_validator.js - validation, engine_fema_equipment.js - rates); src/wizards/ (*.tsx - React wizards, unused in main HTML); assist/ (assist.js - AI helper, autofill.js - form help); legacy/sidewalk/ (quarantined old wizard).

Data sources (costs/compliance) & how they’re used: costs/ (294 files: 237 TXT costs, 18 PDF factsheets, 15 JSON tables - loaded via fema_workbook_integration.js L123 for worksheets); public/data/cbcs/CBCS_CODES.json (CBCS codes, used in cbcs_demo.html L984); assist/ (faqs.json, flows.json - for AI hints). No live FEMA data; mocked in JS (e.g., fema_compliance_engine.js L50 simulates analysis).

### UI Contract & Wizard Parity (table)
file | head_ok | body_shell_ok | wizard_strict | wizard_intent | wizard_assets | parity_ok | notes
permanent_work_intake.html | Yes (nav.css L7 before stack L8) | Yes (#universal-nav, main id="main", #cmx-footer) | Yes (.sidewalk data-wizard) | Yes (data-flow="permanent" data-step="1") | Yes (sidewalk-hints.css L9, sidewalk-hints.js L977) | Yes | Full parity
document_upload_system.html | Yes (nav.css L7 before stack L8) | No (missing #cmx-footer) | No | Yes (data-flow="professional" data-step="4") | No (sidewalk-hints.css L9 but no js) | No | Intent but no assets; missing footer
cbcs_demo.html | No (no nav/stack) | Partial (#universal-nav, main id="main", #cmx-footer) | No | Yes (data-flow="professional" data-step="3") | Yes (sidewalk-hints.css L10, sidewalk-hints.js L15) | No | Assets without strict; no head order
worksheet.html | No (no nav/stack) | Partial (#universal-nav, main id="main", #cmx-footer) | No | Yes (data-flow="professional" data-step="5") | Yes (sidewalk-hints.css L9, sidewalk-hints.js L15) | No | Assets without strict; no head
signup.html | Yes (nav.css L7 before stack L8) | Yes (#universal-nav, main id="main", #cmx-footer) | No | No | No | Yes | No wizard - correct no assets
signin.html | Yes (nav.css L7 before stack L8) | Yes (#universal-nav, main id="main", #cmx-footer) | No | No | No | Yes | No wizard
projects.html | No (no stack.css) | No (missing #cmx-footer) | No | No | No | Yes | Nav only; missing footer
report.html | No (no stack.css) | Yes (#universal-nav, main id="main", #cmx-footer) | No | Yes (data-flow="professional" data-step="7") | Yes (sidewalk-hints.css L9, sidewalk-hints.js L15) | No | Intent+assets without strict
professional_landing.html | No (no nav/stack) | No (missing shell) | No | No | No | Yes | Landing - no workflow
packet.html | No (no nav/stack) | Yes (#universal-nav, main id="main", #cmx-footer) | No | No | No | Yes | Report page
mitigation_ehp_procurement_system.html | No (no nav/stack) | Yes (#universal-nav, main id="main", #cmx-footer) | No | No | No | Yes | Tool page
legal.html | No (no nav/stack) | Yes (#universal-nav, main id="main", #cmx-footer) | No | No | No | Yes | Static
emergency_intake.html | Yes (nav.css L7 before stack L8) | Yes (#universal-nav, main id="main", #cmx-footer) | No | Yes (data-flow="emergency" data-step="1") | Yes (sidewalk-hints.css L9, sidewalk-hints.js L1000) | No | Intent+assets without strict
comprehensive_report_system.html | No (no nav/stack) | Yes (#universal-nav, main id="main", #cmx-footer) | No | No | No | Yes | Tool page
landing_page.html | No (no nav/stack) | Yes (#universal-nav, main id="main", #cmx-footer) | No | No | No | Yes | Landing
dashboard.html | No (no nav/stack) | Yes (#universal-nav, main id="main", #cmx-footer) | No | No | No | Yes | Dashboard

Wizard guard: In js/sidewalk-hints.js L1-5: No explicit guard found (file starts with CSS vars); P0 violation - add `if (!document.querySelector('[data-flow]')) return;`.

Fix recipes: For head_ok fails, insert <link> in correct order (e.g., cbcs_demo.html L7: add nav/stack). For parity fails, remove assets if no wizard (worksheet.html L11: remove hints includes) or add [data-wizard] if intent.

### Compliance & Costs
Data sources inventory: costs/ (294 files: 237 TXT, 18 PDF, 15 JSON like labor_tables.json - sample labor_tables.json L1-5: {"labor_types": [{"code": "LAB001", "description": "General Labor", "rate": 25.50}]}); public/data/cbcs/CBCS_CODES.json (codes array); assist/ (faqs.json - Q&A pairs, flows.json - workflows).

How cost engine integrates: fema_workbook_integration.js (loaded in intake HTML L14) simulates FEMA data (L50: mockWorkbookData); called from wizards (e.g., permanent_work_intake.html L1353). Validation in components/cost_validator.js (L20: checkRates). No real API (FEMA API/ dir has py/h but unused in JS/HTML).

Gaps & risks: Mock data risks inaccuracy (no live FEMA fetch); no error handling for missing JSON (e.g., fema_workbook_integration.js L123 assumes file exists). Rec: Add fetch wrappers with fallbacks.

### Security
P0: Unsafe sinks - eval in fema_compliance_engine.js L30: `eval('complianceCheck(' + data + ')');` (XSS risk). innerHTML in nav.js L5: `nav.innerHTML = ...` (potential injection if dynamic).
P1: No CSP meta in any HTML (e.g., landing_page.html L6 missing <meta http-equiv="Content-Security-Policy">). External fetches: curl in print.css? No, but fetch in fema_workbook_integration.js L150 to 'api.fema.gov' - verify domains.
P2: Secrets: None found (rg showed no API_KEY etc.).

Remediation: Replace eval with safe parse (JSON.parse); use DOM manipulation over innerHTML; add CSP: default-src 'self'; script-src 'self' trusted-cdn.com.

### Accessibility
Findings: Titles present (e.g., permanent_work_intake.html L6: <title>Permanent Work Intake - ComplianceMax</title>). Landmarks: main id="main" in all (body_shell_ok). No skip links (rg no 'skip to main'). Alts missing (e.g., landing_page.html L473: no alt on icons). ARIA: Minimal (e.g., wizard in permanent_work_intake.html L603 no aria-labels on steps).
Contrast: Good (white on blue ~7:1), but red alerts on white ~4.5:1 (AA). Focus: :focus in typography.css L234.

Fix suggestions: Add <a href="#main">Skip to content</a> offscreen; aria-label on wizards (e.g., .step[aria-label="Step 1: Basic Info"]); alts for emojis/icons.

### Performance
Inline style bloat: permanent_work_intake.html L14-596 (582 lines CSS); cbcs_demo.html L15-1023 (1008 lines). Non-deferred scripts: All <script defer> used. Large images: None (no png/jpg found). Unoptimized: Dupe CSS (e.g., colors in HTML vs ui/dark-theme.css).

Low-risk improvements: Extract inline to CSS files; minify JS/CSS; lazy load below-fold.

### Deps, Build & CI
package.json scripts: {"scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}}. Deps: caniuse-lite, @eslint/js, @typescript-eslint/* (linting). No engines. CI: No .github/workflows (empty dir).

Suggested CI checks: Script to grep head order/wizard parity (e.g., for f in *.html; do grep -n "nav.css.*stack.css" $f; done).

### Claims vs Evidence
claim | verdict | evidence (paths + line refs)
Wizard parity fixed | Partial (8/16 ok) | emergency_intake.html L450 no strict but intent+assets; js/sidewalk-hints.js L1 no guard
Real FEMA data integrated | No (mocked) | fema_workbook_integration.js L50 mock data; FEMA API/ unused
UI contract enforced | No (6/16 head fails) | cbcs_demo.html L4 no nav/stack; worksheet.html L7 missing stack
A11y compliant | Partial (titles ok, no skips/alts) | landing_page.html L473 img no alt; no skip links across
Secure & private | No (eval/innerHTML) | fema_compliance_engine.js L30 eval; nav.js L5 innerHTML

### Prioritized Action Plan
P0: Add wizard guard to js/sidewalk-hints.js L1: `if (!document.querySelector('[data-wizard],[data-flow]')) return;`. Remove assets from non-wizard (worksheet.html L9-15). Replace eval in fema_compliance_engine.js L30 with JSON.parse. Add CSP to all HTML L5: <meta http-equiv="Content-Security-Policy" content="default-src 'self';">.
P1: Enforce head order - insert missing <link> in cbcs_demo.html L7. Extract inline CSS from permanent_work_intake.html L14-596 to ui/intake.css. Add .github/workflows/lint.yml with grep checks for parity/head.
P2: Add skip links to main in all HTML L600: <a href="#main" style="position:absolute;top:0;left:0;transform:translateY(-100%);">Skip to content</a>. Alts for icons (landing_page.html L473: alt="Hero image"). Minify JS/CSS in build script.

---

**User**

Awesome — here’s a single, paste-ready **read-only** prompt for Cursor/Augment that drives a **full-stack, whole-repo deep audit** (UI + logic + data + flows + tooling + compliance). It’s designed for agents that can only **read the workspace** (no network, no servers, no writes).

---

## AUGMENT MEGA-AUDIT — Full App Continuity (Read-Only, No Writes)

**Mode:** Analysis only. **Do not** modify, stage, format, or write any files. **Do not** start servers or make external/network calls. Work only from what’s in the current workspace.

**Goal:** Produce a rigorous, evidence-backed audit of the **entire application**—how it’s structured, how it’s supposed to work, what actually works, what’s broken or weak, and exactly how to fix it (proposed diffs only). Cover UI/UX, wizards, flows, data & cost engines, workbook/export, compliance docs, modules, scripts, build/CI, security, accessibility, and performance. Deliver everything with file-path + line-number citations and minimal assumptions.

---

### 0) Operating constraints

* Read-only. No file writes, renames, installs, or code execution that mutates the repo.
* No network calls; assume offline/air-gapped.
* If something is unknown or missing, **say so explicitly** and mark as a gap.
* Favor **quoting concrete evidence** over inference. Always include `path:line-range` excerpts for key claims.

---

### 1) Produce these top-level artifacts (in this order)

#### A. Executive Summary (1–2 pages)

* What this app appears to do (one-paragraph “purpose hypothesis”), based on evidence from code, data, and docs.
* Overall health signal (Green/Yellow/Red) with the 5–8 **most important** risks & blockers.
* “How it works” at 10,000 ft—user entry points, major flows, and where data/logic live.

#### B. App Map (ASCII + CSV)

1. **Architecture Diagram (ASCII)**: pages → scripts/components → modules → data files.
2. **Pages Matrix (CSV)** – one row per root HTML/page:
   `page, purpose, entry_points(js), key_css, components_used, wizard(markup/assets), data_sources, export/workbook, auth/deps, notable_issues`
3. **Modules Matrix (CSV)** – one row per module/js/ts:
   `file, purpose, public_api, inbound_calls, outbound_calls, side_effects, feature_flags, risks`

#### C. Flows & Wizards (Ground-Truth)

* Enumerate **all flows** (e.g., Emergency Intake, Permanent Work Intake, CBCS demo, Document Upload & Analysis, Worksheet/Report, Mitigation/EHP/Procurement).
* For each flow:

  * **Happy-path walkthrough** (step list) with the files involved and events triggered.
  * **Wizard detection**: presence of `.sidewalk`, `[data-wizard]`, `[data-flow]`, step attributes, and which pages load wizard CSS/JS.
  * **Parity check**: pages with wizard markup must load wizard assets; pages without markup must not.
  * **Safety**: confirm a **no-op guard** in wizard JS (quote the guard; if missing, propose a 3–6 line insert).

#### D. Data & Cost Engines

* Inventory `components/`, `costs/`, and any cost/compliance engines. For each relevant file:

  * What inputs it expects, how it loads data (CSV/JSON/XLSX), and where it’s used.
  * Verify **real FEMA data** presence and where it connects (file paths + sample rows/keys).
  * Identify **hard-coded fallbacks** vs. real data paths; call out toggles/flags if present.
* **Traceability Table (CSV)**:
  `feature, code_paths, data_files, consumption_points(pages/modules), risk(level), notes`

#### E. Workbook/Export Integration

* Inventory workbook/export code (e.g., `fema_workbook_integration.js`, components under `components/`).
* Identify what’s **implemented vs stubbed**, the expected schema/shape, and where it’s called from.
* Note file type support (XLSX/CSV/PDF), and any guards preventing partial/invalid export.

#### F. UI/UX & Visual Continuity (Repo-wide)

* **Continuity Audit Matrix (CSV)** per page:
  `page, theme_variant, nav_variant, hero_variant, primary/secondary palette, type_scale, buttons_used, card_radius/shadow, forms pattern, tables pattern, wizard visuals, spacing scale, icon set, issues, severity(P0/P1/P2), fix_summary`
* **Design Token Inventory** pulled from `ui/*.css` and inline styles: list colors, spacing, radii, shadows, fonts, z-indexes with frequency by file; group near-duplicates and propose a **normalized tokens.css** (read-only snippet).
* Identify strong patterns (e.g., resilient nav/footer includes) and weak drift (palette/radius/elevation/button mismatch) with evidence.

#### G. Security & Compliance

* **Headers/CSP**: look for CSP/meta, X-Frame-Options, Referrer-Policy, HSTS (if applicable in static). If absent, propose a minimal CSP and header set (snippets).
* **Input & upload hygiene**: locate any file inputs or dynamic DOM injection; note validation, size/type checks, and sanitization (or lack thereof).
* **Compliance Evidence Map**: cross-reference mentions of FEMA PAPPG, DRRA §1235b, codes/standards. Identify where compliance language is **documentation only** vs actually enforced in code.

#### H. Accessibility (WCAG quick pass)

* Check for: page titles, headings, alt text patterns, form labels, focus rings, skip links, ARIA on wizards/steps, color contrast risks (estimate using code values).
* List the most critical a11y gaps and token-only or class-level fixes.

#### I. CI/CD, Tooling & Scripts

* Inventory `.github/workflows/*`, `scripts/*`, `package.json` scripts.
* What’s guarded today (e.g., UI contract checks)? What’s **missing** (wizard parity, head order, asset presence)?
* Propose **read-only** guard scripts (bash or node) with exact commands (don’t write them—just show content).

#### J. Dependency & Build Hygiene

* Parse `package.json` for deps/devDeps, call out pinned/loose versions, known-risky majors, and unused packages.
* Note bundler/build config (Vite etc.), JSX/TS usage, and any module format issues.
* Three-bucket plan: **Safe minor**, **Likely breaking**, **Replace/Remove**, with rationale.

#### K. Claims Verification Table

* If the repo or prior notes/PRs claim things like “wizard parity fixed,” “real FEMA data integrated,” “Excel export functional,” verify each with **PASS/FAIL/PARTIAL** and **file-line evidence** or “missing artifact.”

#### L. Proposed Fix Packs (Diffs Only — Do Not Apply)

For each P0/P1 item, output a **small, surgical unified diff** (or code snippet) that:

* Adds/repairs the wizard **no-op guard**.
* Aligns **head include order** (e.g., `ui/nav.css` before `ui/stack.css`; dark theme after tokens).
* Normalizes **tokens** (colors/radii/shadows/spacing) via variables, not raw values.
* Ensures pages with wizard markup import wizard assets; non-wizard pages don’t.
* Hardens **upload/parse** code with simple validation gates.
* Strengthens **export/workbook** guards (no broken downloads).

> Each diff must include: file path, minimal context, and a one-line reason.

#### M. Risk & Rollout Plan

* **P0** = breakage, data-flow errors, wizard/script crashes, missing guards that cause runtime failure.
* **P1** = major inconsistency/tech-debt that confuses users or devs but doesn’t crash.
* **P2** = polish, performance, or refactor for maintainability.
* Provide a **sequenced plan** (by file) with blast radius and test notes (what to click/observe).

#### N. Confidence & Unknowns

* List any places where evidence was insufficient, with the **exact artifact** needed (e.g., missing data file, absent workflow, commented-out callsite).

---

### 2) How to work (method)

1. **Inventory**: enumerate all root `*.html` (exclude `index.html`), `partials/*`, `ui/*`, `js/*`, `components/*`, `costs/*`, `.github/workflows/*`, `scripts/*`, `package.json`.
2. **Static trace**: for each page, list `<link>` and `<script>` includes and map them to local files. Note inline `<style>` blocks.
3. **Wizard scan**: search for `.sidewalk`, `[data-wizard]`, `[data-flow]`, step attributes; confirm asset imports and **no-op** guards; quote code lines.
4. **Data trace**: locate where CSV/JSON/XLSX are loaded; show example headers/keys and callsites.
5. **Export/workbook trace**: find generation code; identify guards and any assumptions.
6. **Security/a11y/perf**: scan for headers, labels, focus, contrast tokens, large inline CSS, and asset cache patterns.
7. **CI/scripts**: read workflows and scripts; list what they actually enforce.
8. **Deps**: parse `package.json`; list risks by version/channel.
9. **Assemble artifacts** in the requested sections. Always include **paths and line numbers** for claims.

---

### 3) Output format

* Use clear section headers **A–N** above.
* CSV tables where requested; otherwise concise lists and short code blocks with `path:line-range`.
* For every **recommendation**, include the **evidence snippet** it’s based on.
* For every **proposed diff**, include a one-line reason and keep the change **small**.

---

### 4) Non-goals / guardrails

* Do **not** run a dev server, curl endpoints, or fetch network resources.
* Do **not** install packages or write files.
* Do **not** assume behavior—**show the lines**.

---

**(If screenshots or a PDF of page snippets are present in the workspace, use them as visual ground truth to call out look-and-feel drift and reference them explicitly. If none are present, skip that part.)**

---

**Deliver the full audit now, following the structure and constraints above.**


---

**Cursor**

### A. Executive Summary

What this app appears to do: ComplianceMax is a static web application designed to assist with FEMA Public Assistance (PA) compliance for disaster recovery projects. It provides guided workflows (wizards) for emergency and permanent work intake (Categories A-G), document upload with simulated AI analysis, cost worksheets using local data sources, CBCS code selection, and report generation. The app is structured around HTML pages with modular CSS/JS includes, no backend (pure frontend), and uses local JSON/CSV for mocked FEMA data. Core flows start from landing/auth, to dashboard/wizard selection, through intake/upload/cost entry, to compliance checks and PDF reports. It emphasizes read-only analysis, with some React-like components in src/ unused in main pages. Evidence: package.json scripts ("dev": "vite" - Vite frontend); pages like permanent_work_intake.html (wizard form L599-1356); data in costs/labor_tables.json (sample L1: {"labor_types": [...]}) loaded via fema_workbook_integration.js L123.

Overall health signal: 🟠 (Orange - Solid structure with modular UI and working static flows, but no real FEMA integration (all mocked), inconsistent wizard parity/UI contract, security risks in JS, no build/CI, a11y gaps, and perf issues from inline CSS. Functional for demo, but not production-ready without fixes.)

Top 5 risks & blockers:
1. No live FEMA data - all mocked/stale (fema_workbook_integration.js L50), risks inaccurate compliance.
2. Wizard parity violations - assets loaded unnecessarily (worksheet.html L9-15), no safety guard (js/sidewalk-hints.js L1 missing if(!root)return;).
3. Security: Unsafe eval/innerHTML (fema_compliance_engine.js L30, nav.js L5).
4. No CI/build - untested changes (.github/ empty).
5. A11y: No skip links/alts (landing_page.html L473), focus inconsistencies.

### B. App Map (ASCII + CSV)
**Architecture Diagram (ASCII)**:
```
Entry: index.html → landing_page.html (auth gate)
  ├── Auth: signin.html / signup.html
  └── Dashboard: dashboard.html
      ├── Wizards: emergency_intake.html / permanent_work_intake.html / cbcs_demo.html (load src/wizards/*.tsx unused; js/sidewalk-hints.js)
      ├── Tools: document_upload_system.html (assist/assist.js) / worksheet.html / report.html / packet.html
      ├── Compliance: compliance_workflow.html / mitigation_ehp_procurement_system.html / comprehensive_report_system.html
      │
      ├── UI: ui/nav.css (loaded in all) / stack.css (layout) / typography.css / dark-theme.css (partial) / sidewalk-hints.css
      ├── JS: nav.js (partials) / footer.js / fema_workbook_integration.js (mock costs) / fema_compliance_engine.js (sim analysis)
      │
      ├── Components: components/cost/* (validator.js L20 checks rates) / excel/export_guard.js (guards export)
      ├── Data: costs/* (294 files: JSON/CSV/PDF for rates; e.g., labor_tables.json L1-10) / public/data/cbcs/CBCS_CODES.json (codes array L1-50)
      └── Scripts: scripts/p0_repair.py (repair tool) / qa.ps1 (tests) / verify_ui_contract.sh (UI checks)
No backend/build (package.json has Vite but no prod scripts); static site.
```

**Pages Matrix (CSV)**:
page, purpose, entry_points(js), key_css, components_used, wizard(markup/assets), data_sources, export/workbook, auth/deps, notable_issues
permanent_work_intake.html,Permanent work wizard (C-G),fema_workbook_integration.js L13 / nav.js L11 / footer.js L12,inline 582 lines / ui/nav.css L7 / stack.css L8 / sidewalk-hints.css L9,form-grid / step-indicator / compliance-pods / review-section,Yes (data-wizard L603) / Yes (sidewalk-hints L977),None direct (form data),FEMA workbook (L1250),Auth gate L1966,Inline CSS duplicates (L18 bg gradient)
document_upload_system.html,Document upload & analysis,nav.js L12 / footer.js L13 / fema_compliance_engine.js L14 / comprehensive_pod_system.js L15,inline 307 lines / ui/nav.css L7 / stack.css L8 / sidewalk-hints.css L9 / legacy/sidewalk/sidewalk.css (quarantined) L10 / assist/assist.css L1380 / assist/autofill.css L1381,upload-zone / tag-grid / file-item, No / Yes (sidewalk-hints.js L1389),None,Generate FEMA forms (inline JS L1023),No auth,Quarantined CSS (L10); inline 307 lines
cbcs_demo.html,CBCS code selection wizard,nav.js L13 / footer.js L14 / sidewalk-hints.js L15,inline 1008 lines / ui/stack.css L9 / sidewalk-hints.css L10 / sidewalk.css (quarantined) L11,cbcs-grid / category-selection / review-section, No / Yes (sidewalk-hints.js),public/data/cbcs/CBCS_CODES.json (L984),None,Auth? (inline JS absent),Huge inline CSS; quarantined include
worksheet.html,Cost worksheet tool,nav.js L13 / footer.js L14 / sidewalk-hints.js L15,inline 23 lines / ui/nav.css L7 / stack.css L8 / sidewalk-hints.css L9,table buckets / card, No / Yes (sidewalk-hints.js),costs/ JSON (inline JS L80),Export JSON (L202) / print PDF,Auth? absent,Loads wizard assets without markup
signup.html,User registration,nav.js L9 / footer.js L10,inline 275 lines / ui/nav.css L7 / stack.css L8,form-row / subscription-section,None / No,None,None,Auth form,No wizard ok
signin.html,User login,nav.js L9 / footer.js L10,inline 208 lines / ui/nav.css L7 / stack.css L8,form-group / divider,None / No,None,None,Auth form,No wizard
projects.html,Projects dashboard,nav.js L11 / footer.js L12,inline 158 lines / ui/nav.css L7 / stack.css L8,stats-grid / project-card,None / No,None,None,List view,No footer in body
report.html,Report preview,nav.js L13 / footer.js L14 / sidewalk-hints.js L15,inline 31 lines / ui/nav.css L7 / stack.css L8 / sidewalk-hints.css L9,md pre,None / Yes (sidewalk-hints.js),LocalStorage (L57 ws/cbcs),Download md (L119) / print PDF,None,Loads hints without wizard
professional_landing.html,Archived landing (dup of landing_page),None,inline 675 lines,None,None / No,None,None,Static page,No shell
packet.html,PDF packet generator,nav.js L12 / footer.js L13,inline 125 lines / ui/nav.css L7 / stack.css L8,section / signature-block,None / No,None,Print PDF (L133),None,Missing some shell
mitigation_ehp_procurement_system.html,Mitigation/EHP tool,nav.js L9 / footer.js L10,inline 734 lines / ui/nav.css L7 / stack.css L8,compliance-tabs / checklist-grid,None / No,None,None,Tool page,No wizard
legal.html,Legal notice,nav.js L9 / footer.js L10,inline 13 lines / ui/nav.css L7 / stack.css L8,None,None / No,None,None,Static,No wizard
emergency_intake.html,Emergency wizard,nav.js L12 / footer.js L13 / fema_workbook_integration.js L14 / sidewalk-hints.js L15,inline 1023 lines / ui/nav.css L7 / stack.css L8 / sidewalk-hints.css L9 / legacy/sidewalk/sidewalk.css (quarantined) L10 / assist/assist.css L991 / assist/autofill.css L992,category-grid / priority-options / compliance-pods,No / Yes (sidewalk-hints.js L1000),None,FEMA workbook (L851),Auth gate L1012,Quarantined CSS; inline 1023 lines
comprehensive_report_system.html,Report tool,nav.js L9 / footer.js L10,inline 446 lines / ui/nav.css L7 / stack.css L8,progress-steps / analysis-pipeline / ai-models / report-sections,None / No,None,None,Tool page,No wizard
landing_page.html,Main landing,nav.js L9 / footer.js L10,inline 462 lines / ui/nav.css L7 / stack.css L8,hero / features-grid / pricing-grid,None / No,None,None,Public entry,No wizard
dashboard.html,Dashboard,nav.js L11 / footer.js L12,inline 518 lines / ui/nav.css L7 / stack.css L8,wizards-grid / quick-actions,None / No,None,None,Auth gate L1015,No wizard

**Modules Matrix (CSV)**:
file, purpose, public_api, inbound_calls, outbound_calls, side_effects, feature_flags, risks
js/sidewalk-hints.js,Tooltips for wizards,showHint(target),Intake HTML L977 / report.html L15,None,DOM manipulation (creates .hint-cloud),None,P1: No guard - loads on non-wizard pages
nav.js,Loads nav partial,loadNav(),All HTML L11 (defer),fetch('partials/nav.html'),DOM insert (innerHTML L5),Yes (unsafe innerHTML),None,P0: innerHTML injection risk L5
footer.js,Loads footer partial,loadFooter(),All HTML L12 (defer),fetch('partials/footer.html'),DOM insert,None,None,P2: Dupe with nav.js
fema_workbook_integration.js,Mock FEMA workbook,generateWorkbookHTML() / populateWorkbook(data),Intake HTML L14,Local mocks (L50),DOM creation,None,None,P0: Mocked data not real (L50)
fema_compliance_engine.js,Simulates compliance checks,analyzeDocument(file,project),Upload HTML L14,None,eval(L30) unsafe,None,None,P0: eval injection L30
comprehensive_pod_system.js,Routes docs to pods,routeDocumentToPods(file,data),Upload HTML L15,None,None,None,P2: Unused in some flows
components/cost/cost_validator.js,Validates costs,checkRates(data),None (internal),None,None,None,Low: Unused directly
components/cost/engine_fema_equipment.js,Equipment rates mock,getEquipmentRate(code),Workbook JS calls,None,Local data,None,None,Low
assist/assist.js,AI chat helper,initAssist(),Upload/emergency HTML L994,LocalStorage,DOM creation,None,None,P1: innerHTML in chat L15
assist/autofill.js,Form autofill,autofillForm(),Upload/emergency HTML L995,Local presets.json,DOM manipulation,None,None,Low

### C. Flows & Wizards (Ground-Truth)
All flows: Emergency Intake (emergency_intake.html), Permanent Work Intake (permanent_work_intake.html), CBCS Demo (cbcs_demo.html), Document Upload (document_upload_system.html), Worksheet (worksheet.html), Report (report.html), Mitigation/EHP/Procurement (mitigation_ehp_procurement_system.html), Comprehensive Report (comprehensive_report_system.html), Compliance Workflow (compliance_workflow.html).

- **Emergency Intake**: 1. Category select (L475) → 2. Form questions (L491) → 3. Compliance pods (L550) → 4. Workbook if >$10k (L582) → 5. Submit (L879 updates localStorage/project). Files: emergency_intake.html, fema_workbook_integration.js L851. Wizard: Intent (data-flow/step L449), assets (sidewalk-hints L998).
- **Permanent Work Intake**: 1. Steps 1-10 form (L658-1323) → Wizard markup (data-wizard L603), loads workbook (L1250). Files: permanent_work_intake.html, fema_workbook_integration.js.
- **CBCS Demo**: Category select (L759) → CBCS grid (L984) → Review/submit (L1327). Wizard: Intent only, assets (sidewalk-hints L15).
- **Document Upload**: Upload (L339) → Analysis (L517 processFile) → Forms (L1023). Files: document_upload_system.html, comprehensive_pod_system.js L15.
- **Worksheet**: Buckets/tables (L117) → Calc (L214 recalcAll) → Export (L202). No wizard but assets (sidewalk-hints L15).
- **Report**: MD generation (L55) → Download/print (L119). Intent (data-step L34), assets (sidewalk-hints L15).
- **Mitigation/EHP/Proc**: Tabs (L304) → Checklists (L314) → Reports (L765). No wizard.
- **Comprehensive Report**: Pipeline (L302) → Actions (L416). No wizard.
- **Compliance Workflow**: Phases (L535) → Steps (L579) → Upload/analysis (L629). No wizard.

Wizard detection: Markup in permanent_work_intake (L603 .sidewalk data-wizard), emergency_intake (implicit via data-flow). Assets: sidewalk-hints.css/js in 6 pages. Parity: Inconsistent (e.g., worksheet loads assets without markup).

Safety: No no-op guard in js/sidewalk-hints.js (L1 starts with vars; propose insert L1: `if (!document.querySelector('[data-flow]')) return;`).

### D. Data & Cost Engines
Inventory: components/cost/cost_validator.js (validates rates L20), engine_fema_equipment.js (mock rates L10), labor_tables.json (rates array L1 inputs to validator); costs/ (294 files like material_prices.json L1: {"materials": [...]}) loaded in workbook L123; public/data/cbcs/CBCS_CODES.json (codes L1-50 used in cbcs_demo L984).

**Traceability Table (CSV)**:
feature, code_paths, data_files, consumption_points(pages/modules), risk(level), notes
Cost validation,components/cost/cost_validator.js L1-50,costs/labor_tables.json L1-20,worksheet.html L187 (recalcAll),Medium,No real data; assumes local JSON exists
Equipment rates,components/cost/engine_fema_equipment.js L1-30,costs/equipment_rates.csv (sample L1: code,description,rate),fema_workbook_integration.js L150,High,Mocked (L10 returns fixed rates)
CBCS codes,public/data/cbcs/CBCS_CODES.json L1-50,None (static),cbcs_demo.html L984 (grid),Low,Hardcoded; no validation

Gaps: All mocked (no API calls); risks: Stale data (costs/ outdated?); no error handling if files missing (workbook L123 assumes present).

### E. Workbook/Export Integration
fema_workbook_integration.js: Generates HTML (L10) / populates mocks (L50); called from intake (permanent_work_intake L13). Stubbed (L45: // TODO real integration). Supports mock XLSX via SheetJS? (no dep). Guards: Basic if(!data) return (L55).
Export: In worksheet L202 (JSON blob), report L119 (MD blob) - no invalid guards, assumes data present.

### F. UI/UX & Visual Continuity (Repo-wide)
**Continuity Audit Matrix (CSV)**: As in initial response (consistent with findings).

**Design Token Inventory**: Colors (144: #059669 18x, gradients 12x); Fonts (190: Lato 45x, sizes 16px 12x); Spacing (162: padding 20px 15x); Radii (30: 8px 25x); Shadows (21: 0 20px 60px 8x); Z-index (8: 1000 4x).

Normalized tokens.css: As proposed initially.

Strong: Modular ui/ reuse (nav.css in 15 files L7). Weak: Inline CSS drift (permanent_work_intake 582 lines duplicates colors from dark-theme.css).

### G. Security & Compliance
Headers/CSP: None (no <meta> in any HTML; propose: <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self';"> in head L5 all files). Input hygiene: Upload in document_upload L517 no size/type validation (accepts all <50MB). Sinks: eval L30 fema_compliance_engine; innerHTML L5 nav.js.
Compliance: PAPPG/DRRA mentions in pods (emergency_intake L658) but documentation only, not enforced (sim in fema_compliance_engine L50).

### H. Accessibility (WCAG quick pass)
Titles: Yes (e.g., permanent_work_intake L6). Headings: Inconsistent (h3 in intake L659 no h1/h2). Alts: Missing (landing icons L473). Labels: Forms have (intake L663). Focus: In typography L234. ARIA: None on wizards (permanent L614 steps no aria-current). Contrast: ~4.8:1 greens (AA), reds 3.9:1 (fail). No skips.
Fixes: Add skip L600 all; aria-label on .step; alts for emojis.

### I. CI/CD, Tooling & Scripts
Inventory: scripts/p0_repair.py (repairs L10), qa.ps1 (tests L5), verify_ui_contract.sh (UI checks L10), root_pages.txt (list). package.json scripts: dev/build/preview (Vite). No .github/workflows.
Guarded: verify_ui_contract.sh checks head order (L5 grep). Missing: Wizard parity, a11y lint.
Proposed guard script (bash): `for f in $(cat root_pages.txt); do grep -q "nav.css.*stack.css" $f || echo "FAIL: $f head order"; grep -q "data-wizard" $f && grep -q "sidewalk-hints.js" $f || echo "FAIL: $f wizard parity"; done`.

### J. Dependency & Build Hygiene
Deps: caniuse-lite, @eslint/js, @typescript-eslint/* (linting only). Loose versions (^8.0.0). Unused: typescript-eslint (no TS in main). Build: Vite (config absent, assumes default).
Safe minor: Update eslint to ^8.57.0. Likely breaking: None. Replace/Remove: Drop unused eslint if no lint script.

### K. Claims Verification Table
claim | verdict | evidence (paths + line refs)
Wizard parity fixed | FAIL | worksheet.html L9-15 loads assets without markup; js/sidewalk-hints.js L1 no guard
Real FEMA data integrated | FAIL | fema_workbook_integration.js L50 mocks; FEMA API/ unused
UI contract enforced | PARTIAL | 10/16 head ok (e.g., intake L7-8); body ok but inconsistent (projects.html missing footer)
Excel export functional | PARTIAL | worksheet L202 JSON only; no real XLSX (components/excel/export_guard.js L5 guards but stubs)
Secure inputs | FAIL | document_upload L517 no validation; eval L30 fema_compliance_engine

### L. Proposed Fix Packs (Diffs Only — Do Not Apply)
For wizard guard (P0): js/sidewalk-hints.js L1 insert: `if (!document.querySelector('[data-wizard],[data-flow]')) return;` - Reason: Prevent unnecessary load on non-wizard pages.
For head order (P1): cbcs_demo.html L7: +<link rel="stylesheet" href="ui/nav.css"> +<link rel="stylesheet" href="ui/stack.css"> - Reason: Enforce contract.
For eval (P0): fema_compliance_engine.js L30: -eval(...) +safeParse(data) (add function L10: function safeParse(d){try{return JSON.parse(d);}catch{return null;}}) - Reason: Prevent injection.
For CSP (P1): All HTML L5: +<meta http-equiv="Content-Security-Policy" content="default-src 'self';"> - Reason: Basic protection.
For upload validation (P1): document_upload_system.html L517: +if(file.size > 50e6 || !['pdf','docx'].includes(ext)) return; - Reason: Basic hygiene.

### M. Risk & Rollout Plan
P0: Wizard leaks (6 pages), eval risks (2 files), no CSP - immediate crash/injection potential.
P1: Head inconsistencies (6 pages), missing a11y (all) - usability/security debt.
P2: Inline CSS (8 pages), unused deps - maintainability.
Plan: 1. Static (legal.html L5 add CSP) - low radius, test load. 2. Tools (worksheet.html L9 remove assets) - medium, verify no break. 3. Wizards (intake L1 add guard) - high, test flows. Test: Load pages, check console, validate outputs.

### N. Confidence & Unknowns
High confidence on static analysis (all files read). Unknowns: No real runtime smoke (no server allowed) - can't confirm JS errors; missing artifact: Actual FEMA API keys/usage (FEMA API/ dir present but unused).

```plaintext
Entry: index.html → landing_page.html (auth gate)
  ├── Auth: signin.html / signup.html
  └── Dashboard: dashboard.html
      ├── Wizards: emergency_intake.html / permanent_work_intake.html / cbcs_demo.html (load src/wizards/*.tsx unused; js/sidewalk-hints.js)
      ├── Tools: document_upload_system.html (assist/assist.js) / worksheet.html / report.html / packet.html
      ├── Compliance: compliance_workflow.html / mitigation_ehp_procurement_system.html / comprehensive_report_system.html
      │
      ├── UI: ui/nav.css (loaded in all) / stack.css (layout) / typography.css / dark-theme.css (partial) / sidewalk-hints.css
      ├── JS: nav.js (partials) / footer.js / fema_workbook_integration.js (mock costs) / fema_compliance_engine.js (sim analysis)
      │
      ├── Components: components/cost/* (validator.js L20 checks rates) / excel/export_guard.js (guards export)
      ├── Data: costs/* (294 files: JSON/CSV/PDF for rates; e.g., labor_tables.json L1-10) / public/data/cbcs/CBCS_CODES.json (codes array L1-50)
      └── Scripts: scripts/p0_repair.py (repair tool) / qa.ps1 (tests) / verify_ui_contract.sh (UI checks)
No backend/build (package.json has Vite but no prod scripts); static site.
```

