
'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts'

interface TrendAnalysisProps {
  timeRange: string
}

const trendData = [
  { date: '2025-01-01', compliance: 88, projects: 12, documents: 156 },
  { date: '2025-02-01', compliance: 91, projects: 15, documents: 189 },
  { date: '2025-03-01', compliance: 89, projects: 18, documents: 234 },
  { date: '2025-04-01', compliance: 93, projects: 14, documents: 267 },
  { date: '2025-05-01', compliance: 95, projects: 22, documents: 312 },
  { date: '2025-06-01', compliance: 94, projects: 24, documents: 356 }
]

const riskTrendData = [
  { month: 'Jan', high: 5, medium: 12, low: 83 },
  { month: 'Feb', high: 3, medium: 15, low: 82 },
  { month: 'Mar', high: 4, medium: 11, low: 85 },
  { month: 'Apr', high: 2, medium: 13, low: 85 },
  { month: 'May', high: 3, medium: 9, low: 88 },
  { month: 'Jun', high: 2, medium: 8, low: 90 }
]

export function TrendAnalysis({ timeRange }: TrendAnalysisProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [activeChart, setActiveChart] = useState<'compliance' | 'risk'>('compliance')

  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 1500)
    return () => clearTimeout(timer)
  }, [timeRange])

  if (isLoading) {
    return (
      <Card className="glass border-white/20">
        <CardHeader>
          <CardTitle className="text-white">Trend Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80 flex items-center justify-center">
            <div className="animate-pulse text-white/60">Loading trend data...</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="glass border-white/20">
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="text-white">Trend Analysis</CardTitle>
          <div className="flex gap-2">
            <button
              onClick={() => setActiveChart('compliance')}
              className={`px-3 py-1 rounded text-xs ${
                activeChart === 'compliance' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-white/10 text-white/70 hover:bg-white/20'
              }`}
            >
              Compliance
            </button>
            <button
              onClick={() => setActiveChart('risk')}
              className={`px-3 py-1 rounded text-xs ${
                activeChart === 'risk' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-white/10 text-white/70 hover:bg-white/20'
              }`}
            >
              Risk
            </button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {activeChart === 'compliance' ? (
          <div className="space-y-4">
            <h3 className="text-white font-medium">Compliance & Project Trends</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={trendData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis 
                    dataKey="date" 
                    stroke="rgba(255,255,255,0.7)"
                    fontSize={12}
                    tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short' })}
                  />
                  <YAxis 
                    stroke="rgba(255,255,255,0.7)"
                    fontSize={12}
                  />
                  <Tooltip 
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,0.8)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px',
                      color: 'white'
                    }}
                    labelFormatter={(value) => new Date(value).toLocaleDateString()}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="compliance" 
                    stroke="#10B981" 
                    strokeWidth={3}
                    dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
                    name="Compliance Rate (%)"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="projects" 
                    stroke="#3B82F6" 
                    strokeWidth={3}
                    dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
                    name="Projects"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
            <div className="flex justify-center gap-6">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full" />
                <span className="text-white/70 text-sm">Compliance Rate</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full" />
                <span className="text-white/70 text-sm">Project Count</span>
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <h3 className="text-white font-medium">Risk Level Distribution</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={riskTrendData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis 
                    dataKey="month" 
                    stroke="rgba(255,255,255,0.7)"
                    fontSize={12}
                  />
                  <YAxis 
                    stroke="rgba(255,255,255,0.7)"
                    fontSize={12}
                  />
                  <Tooltip 
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,0.8)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px',
                      color: 'white'
                    }}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="low" 
                    stackId="1"
                    stroke="#10B981" 
                    fill="#10B981"
                    fillOpacity={0.6}
                    name="Low Risk"
                  />
                  <Area 
                    type="monotone" 
                    dataKey="medium" 
                    stackId="1"
                    stroke="#F59E0B" 
                    fill="#F59E0B"
                    fillOpacity={0.6}
                    name="Medium Risk"
                  />
                  <Area 
                    type="monotone" 
                    dataKey="high" 
                    stackId="1"
                    stroke="#EF4444" 
                    fill="#EF4444"
                    fillOpacity={0.6}
                    name="High Risk"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
            <div className="flex justify-center gap-6">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full" />
                <span className="text-white/70 text-sm">Low Risk</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-yellow-500 rounded-full" />
                <span className="text-white/70 text-sm">Medium Risk</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-red-500 rounded-full" />
                <span className="text-white/70 text-sm">High Risk</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
