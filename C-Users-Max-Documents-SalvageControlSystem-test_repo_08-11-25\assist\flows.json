{"emergency_intake": {"next": ["Upload required docs", "Go to Worksheet", "Build Report"], "docs_required_A": ["Photos (GPS)", "Debris type/location", "Force Account logs", "Disposal tickets"], "docs_required_B": ["Threat-to-life narrative", "Emergency work logs", "Force Account logs", "Photos of conditions"]}, "cbcs": {"next": ["Upload drawings/specs (PDF)", "Select CBCS codes", "Generate justification", "Stage to Worksheet"]}, "worksheet": {"next": ["Add line items", "Set cost source per line", "Check bucket totals", "Generate Report"]}, "report": {"next": ["Review auto-checks", "Download PDF packet"]}, "dashboard": {"next": ["New Emergency Intake", "New Professional Intake", "Open Worksheet", "Generate Report"]}, "landing_page": {"next": ["Go to Dashboard", "Emergency Intake", "Professional Intake"]}}