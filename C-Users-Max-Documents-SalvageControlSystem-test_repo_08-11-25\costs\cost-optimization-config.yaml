apiVersion: v1
kind: ConfigMap
metadata:
  name: cost-optimization-config
  namespace: monitoring
data:
  monitoring:
    enabled: true
    exporters:
      - prometheus
      - cloudwatch
    
    metrics:
      - name: compute_cost
        type: gauge
        description: "Hourly compute cost by service"
      - name: storage_cost
        type: gauge
        description: "Daily storage cost by service"
      - name: network_cost
        type: gauge
        description: "Daily network transfer cost"
        
    budgets:
      monthly_limit: 10000
      alerts:
        - threshold: 0.8
          notification: email
        - threshold: 0.9
          notification: slack
          
    optimization:
      auto_scaling:
        enabled: true
        metrics:
          - cpu_utilization
          - memory_utilization
        thresholds:
          scale_up: 0.8
          scale_down: 0.2
          
      resource_right_sizing:
        enabled: true
        check_interval: 24h
        min_sample_period: 7d
        
      storage_lifecycle:
        enabled: true
        rules:
          - age: 30d
            tier: infrequent_access
          - age: 90d
            tier: glacier
            
      reserved_instances:
        enabled: true
        coverage_target: 0.7
        look_back_period: 30d 