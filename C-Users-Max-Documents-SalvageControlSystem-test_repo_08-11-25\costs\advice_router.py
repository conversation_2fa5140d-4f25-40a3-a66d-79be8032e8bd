# FastAPI router for step-aware compliance advice
# This file shows the structure for your backend implementation

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from pydantic import BaseModel

# Import your existing auth dependencies
# from app.core.deps import get_db, get_current_active_user
# from app.models.user import User

router = APIRouter(prefix="/advice", tags=["Step Advice"])

# Request/Response models
class AdviceRequest(BaseModel):
    flow: str
    step: str
    api_key: str
    context: Dict[str, Any] = {}

class Recommendation(BaseModel):
    id: str
    title: str
    why: str
    actions: List[str]
    citations: List[str] = []
    severity: str = "medium"  # high, medium, low

class PolicyRef(BaseModel):
    doc_id: str
    version: str
    section: str
    url: str

class AdviceResponse(BaseModel):
    step: str
    summary: str
    recommendations: List[Recommendation]
    policy_refs: List[PolicyRef]

# Mock policy resolver service
class PolicyResolver:
    def resolve_latest_applicable(self, event_year: int, disaster_code: str) -> List[PolicyRef]:
        """Resolve latest applicable policy documents"""
        # Stub implementation - replace with real document pipeline integration
        return [
            PolicyRef(
                doc_id="fema-pappg",
                version="v2025",
                section="latest",
                url="https://www.fema.gov/assistance/public/applicant/policy-guidance"
            )
        ]

# Mock presets service
class AdvicePresets:
    def __init__(self):
        # This would be loaded from advice/presets.yaml
        self.presets = {
            "intake": {
                "summary": "Focus on complete applicant identification and incident context.",
                "recommendations": [
                    {
                        "id": "intake-001",
                        "title": "Verify UEI/DUNS Registration",
                        "why": "Required for all federal assistance per 2 CFR 25.200",
                        "actions": ["Check SAM.gov registration", "Ensure active status"],
                        "citations": ["2 CFR 25.200", "PAPPG v2025 §2.1"],
                        "severity": "high"
                    }
                ]
            },
            "eligibility": {
                "summary": "Confirm all eligibility criteria are met before proceeding.",
                "recommendations": [
                    {
                        "id": "elig-001", 
                        "title": "Document Legal Authority",
                        "why": "Applicant must have legal responsibility for damaged facility",
                        "actions": ["Provide ownership documentation", "Verify legal authority"],
                        "citations": ["44 CFR 206.221(a)", "PAPPG v2025 §3.1"],
                        "severity": "high"
                    }
                ]
            },
            "damage_inventory": {
                "summary": "Focus on complete site list and prelim costs with A–G mapping.",
                "recommendations": [
                    {
                        "id": "damage-001",
                        "title": "Map each site to a PA Category",
                        "why": "Eligibility hinges on category alignment; mismatches delay obligation.",
                        "actions": ["Assign A–G per site", "Add prelim cost per site"],
                        "citations": ["PAPPG v2025 §4.2.1"],
                        "severity": "high"
                    }
                ]
            },
            "permanent_basic": {
                "summary": "Establish clear project identity and permanent work eligibility.",
                "recommendations": [
                    {
                        "id": "perm-basic-001",
                        "title": "Document Pre-Disaster Condition",
                        "why": "Permanent work must restore to pre-disaster design and function",
                        "actions": ["Gather pre-disaster photos", "Document original specifications"],
                        "citations": ["44 CFR 206.226(a)", "PAPPG v2025 §5.1"],
                        "severity": "high"
                    }
                ]
            },
            "permanent_project": {
                "summary": "Define specific permanent work scope with clear damage assessment.",
                "recommendations": [
                    {
                        "id": "perm-proj-001",
                        "title": "Separate Emergency vs Permanent Work",
                        "why": "Different eligibility rules apply to Categories A/B vs C-G",
                        "actions": ["Identify immediate safety work", "Document permanent repairs needed"],
                        "citations": ["PAPPG v2025 §4.1", "44 CFR 206.223"],
                        "severity": "medium"
                    }
                ]
            },
            "permanent_compliance": {
                "summary": "Ensure all documentation requirements are identified and planned.",
                "recommendations": [
                    {
                        "id": "perm-comp-001",
                        "title": "Plan Documentation Strategy",
                        "why": "Missing documentation is the #1 cause of audit findings",
                        "actions": ["Review PAPPG documentation requirements", "Create document checklist"],
                        "citations": ["PAPPG v2025 §8.1", "2 CFR 200.318"],
                        "severity": "high"
                    }
                ]
            },
            "permanent_insurance": {
                "summary": "Document all insurance and other assistance to prevent duplication.",
                "recommendations": [
                    {
                        "id": "perm-ins-001",
                        "title": "Calculate Insurance Proceeds",
                        "why": "FEMA cannot duplicate insurance benefits per Stafford Act §312",
                        "actions": ["Obtain insurance settlement letters", "Document coverage limits"],
                        "citations": ["42 USC 5155", "PAPPG v2025 §8.2"],
                        "severity": "high"
                    }
                ]
            },
            "permanent_cbcs": {
                "summary": "Ensure compliance with applicable codes and standards.",
                "recommendations": [
                    {
                        "id": "perm-cbcs-001",
                        "title": "Apply Latest Codes per DRRA 1235(b)",
                        "why": "Must use codes in effect at time of repair, not original construction",
                        "actions": ["Identify applicable building codes", "Document code compliance"],
                        "citations": ["DRRA 1235(b)", "PAPPG v2025 §6.3"],
                        "severity": "high"
                    }
                ]
            },
            "permanent_mitigation": {
                "summary": "Evaluate Section 406 mitigation opportunities.",
                "recommendations": [
                    {
                        "id": "perm-mit-001",
                        "title": "Assess Mitigation Opportunities",
                        "why": "Section 406 mitigation is cost-effective hazard reduction",
                        "actions": ["Evaluate hazard reduction measures", "Calculate benefit-cost ratio"],
                        "citations": ["44 CFR 206.226(d)", "PAPPG v2025 §7.1"],
                        "severity": "medium"
                    }
                ]
            },
            "permanent_procurement": {
                "summary": "Ensure procurement compliance with federal requirements.",
                "recommendations": [
                    {
                        "id": "perm-proc-001",
                        "title": "Follow 2 CFR 200 Procurement Standards",
                        "why": "Non-compliance can result in questioned costs",
                        "actions": ["Use competitive procurement", "Document vendor selection"],
                        "citations": ["2 CFR 200.318-326", "PAPPG v2025 §8.4"],
                        "severity": "high"
                    }
                ]
            },
            "emergency_intake": {
                "summary": "Rapid setup for immediate life safety and property protection.",
                "recommendations": [
                    {
                        "id": "emerg-001",
                        "title": "Document Immediate Threat",
                        "why": "Emergency work must address immediate threats to life/safety",
                        "actions": ["Document threat to life/safety", "Justify emergency timeline"],
                        "citations": ["44 CFR 206.223(a)(1)", "PAPPG v2025 §4.1.1"],
                        "severity": "high"
                    }
                ]
            },
            "professional_cbcs": {
                "summary": "Advanced codes and standards analysis for complex projects.",
                "recommendations": [
                    {
                        "id": "prof-cbcs-001",
                        "title": "Engage Professional Engineer",
                        "why": "Complex projects require professional engineering analysis",
                        "actions": ["Retain licensed PE", "Document engineering analysis"],
                        "citations": ["State licensing requirements", "PAPPG v2025 §6.4"],
                        "severity": "medium"
                    }
                ]
            }
        }
    
    def get_recommendations(self, api_key: str) -> Dict[str, Any]:
        return self.presets.get(api_key, {
            "summary": "No specific recommendations available for this step.",
            "recommendations": []
        })

# Initialize services
policy_resolver = PolicyResolver()
advice_presets = AdvicePresets()

@router.post("/step", response_model=AdviceResponse)
async def get_step_advice(
    request: AdviceRequest,
    # db: Session = Depends(get_db),
    # current_user: User = Depends(get_current_active_user)
):
    """Get step-aware compliance advice with policy citations"""
    
    try:
        # Validate request
        if not request.flow or not request.step or not request.api_key:
            raise HTTPException(status_code=400, detail="Missing required fields")
        
        # Get preset recommendations
        preset_data = advice_presets.get_recommendations(request.api_key)
        
        # Resolve policy references
        context = request.context or {}
        event_year = context.get('event_year', 2024)
        disaster_code = context.get('disaster_code', 'DR-4781')
        
        policy_refs = policy_resolver.resolve_latest_applicable(event_year, disaster_code)
        
        # Build recommendations
        recommendations = []
        for rec_data in preset_data.get('recommendations', []):
            recommendations.append(Recommendation(**rec_data))
        
        return AdviceResponse(
            step=request.step,
            summary=preset_data.get('summary', ''),
            recommendations=recommendations,
            policy_refs=policy_refs
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# Health check endpoint
@router.get("/health")
async def health_check():
    """Health check for advice service"""
    return {"status": "healthy", "service": "step-advice"}
