// Excel Export Guard - Prevents export until unit cost libraries are provided
// Blocks generation of .xlsx files with fabricated or placeholder data

class ExcelExportGuard {
    constructor() {
        this.errors = [];
        this.warnings = [];
        this.blocked = false;
    }

    // Main export validation - returns true if export is allowed
    async canExport(costData) {
        this.errors = [];
        this.warnings = [];
        this.blocked = false;

        // Check for required unit cost libraries
        await this._checkUnitCostLibraries();
        
        // Validate cost data if provided
        if (costData) {
            this._validateCostData(costData);
        }

        // Export is allowed only if no errors and not blocked
        return this.errors.length === 0 && !this.blocked;
    }

    // Check if unit cost libraries are properly populated
    async _checkUnitCostLibraries() {
        try {
            // Check labor tables
            const laborResponse = await fetch('/C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25//C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25//C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25/components/cost/labor_tables.json'))));
            if (laborResponse.ok) {
                const laborData = await laborResponse.json();
                if (this._isPlaceholderData(laborData.labor_rates)) {
                    this.errors.push('Labor unit costs not provided - placeholder data detected');
                    this.blocked = true;
                }
            } else {
                this.errors.push('Labor cost library not found');
                this.blocked = true;
            }

            // Check material prices
            const materialResponse = await fetch('/C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25//C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25//C-Users-Max-Documents-SalvageControlSystem-test_repo_08-11-25/components/cost/material_prices.json'))));
            if (materialResponse.ok) {
                const materialData = await materialResponse.json();
                if (this._isPlaceholderData(materialData.materials)) {
                    this.errors.push('Material unit costs not provided - placeholder data detected');
                    this.blocked = true;
                }
            } else {
                this.errors.push('Material cost library not found');
                this.blocked = true;
            }

            // Check equipment rates (should be automatically loaded)
            if (window.FEMAEquipmentEngine && !window.FEMAEquipmentEngine.loaded) {
                this.warnings.push('FEMA equipment rates not loaded - attempting to load');
                try {
                    await window.FEMAEquipmentEngine.loadRates();
                } catch (error) {
                    this.errors.push('Failed to load FEMA equipment rates');
                    this.blocked = true;
                }
            }

        } catch (error) {
            this.errors.push(`Error checking unit cost libraries: ${error.message}`);
            this.blocked = true;
        }
    }

    // Check if data contains only placeholders
    _isPlaceholderData(data) {
        if (!data || typeof data !== 'object') return true;
        
        // Check for placeholder indicators
        if (data._placeholder_note || data._instructions) return true;
        
        // Check if all values are zero, empty, or placeholder text
        const values = Object.values(data);
        if (values.length === 0) return true;
        
        const realValues = values.filter(v => 
            v !== null && 
            v !== undefined && 
            v !== 0 && 
            v !== '' &&
            !String(v).includes('placeholder') &&
            !String(v).includes('USER MUST') &&
            !String(v).includes('_instructions')
        );
        
        return realValues.length === 0;
    }

    // Validate cost data structure and values
    _validateCostData(costData) {
        if (!costData.totals) {
            this.errors.push('No cost totals provided');
            return;
        }

        const { totals } = costData;
        const totalCost = totals.total || 0;

        // Check for zero costs
        if (totalCost === 0) {
            this.errors.push('Total cost is zero - no costs calculated');
            return;
        }

        // Check for negative costs
        if (totalCost < 0) {
            this.errors.push('Total cost is negative - invalid calculation');
            return;
        }

        // Validate component totals
        const componentSum = (totals.labor || 0) + (totals.materials || 0) + 
                           (totals.equipment || 0) + (totals.other || 0);
        
        if (Math.abs(componentSum - totalCost) > 0.01) {
            this.errors.push('Component costs do not sum to total cost');
        }

        // Check for extremely high costs (potential data error)
        if (totalCost > 1000000000) { // $1B
            this.warnings.push(`Extremely high total cost ($${(totalCost/1000000).toFixed(1)}M) - verify accuracy`);
        }
    }

    // Generate blocking message for user
    getBlockingMessage() {
        if (!this.blocked && this.errors.length === 0) {
            return null;
        }

        let message = 'Excel export is blocked:\n\n';
        
        if (this.errors.length > 0) {
            message += 'ERRORS:\n';
            this.errors.forEach(error => {
                message += `• ${error}\n`;
            });
        }

        if (this.warnings.length > 0) {
            message += '\nWARNINGS:\n';
            this.warnings.forEach(warning => {
                message += `• ${warning}\n`;
            });
        }

        message += '\nTo enable Excel export:\n';
        message += '1. Provide actual labor rates in components/cost/labor_tables.json\n';
        message += '2. Provide actual material prices in components/cost/material_prices.json\n';
        message += '3. Ensure all cost calculations are complete and valid\n';
        message += '4. Remove all placeholder data\n';

        return message;
    }

    // Get export status summary
    getExportStatus() {
        return {
            canExport: this.errors.length === 0 && !this.blocked,
            blocked: this.blocked,
            errors: this.errors.length,
            warnings: this.warnings.length,
            message: this.blocked || this.errors.length > 0 ? 
                     'Export blocked - unit cost libraries required' : 
                     'Ready for export'
        };
    }

    // Attempt export with user feedback
    async attemptExport(costData, exportFunction) {
        try {
            const canExport = await this.canExport(costData);
            
            if (!canExport) {
                const message = this.getBlockingMessage();
                alert(message);
                return false;
            }

            // Show warnings if any
            if (this.warnings.length > 0) {
                const warningMessage = 'Export warnings detected:\n\n' + 
                                     this.warnings.join('\n• ') + 
                                     '\n\nProceed with export?';
                
                if (!confirm(warningMessage)) {
                    return false;
                }
            }

            // Proceed with export
            if (typeof exportFunction === 'function') {
                await exportFunction(costData);
                return true;
            } else {
                throw new Error('Export function not provided');
            }

        } catch (error) {
            alert(`Export failed: ${error.message}`);
            return false;
        }
    }
}

// Stub export function that shows what would be exported
function stubExcelExport(costData) {
    return new Promise((resolve) => {
        const summary = {
            timestamp: new Date().toISOString(),
            filename: 'FEMA_Cost_Estimate.xlsx',
            structure: {
                'Project Info': 'Applicant details, project description',
                'Labor Costs': `${Object.keys(costData.labor || {}).length} labor items`,
                'Material Costs': `${Object.keys(costData.materials || {}).length} material items`,
                'Equipment Costs': `${Object.keys(costData.equipment || {}).length} equipment items`,
                'Other Costs': 'Engineering, permits, contingencies',
                'Summary': 'Totals and validation status'
            },
            totals: costData.totals || {},
            dataSource: {
                equipment: 'FEMA Schedule of Equipment Rates 2025',
                labor: 'User-provided rates (validated)',
                materials: 'User-provided prices (validated)',
                engineering: 'FEMA cost estimating guidelines'
            }
        };

        console.log('Excel Export Summary:', summary);
        
        alert(`Excel export would generate:\n\n` +
              `File: ${summary.filename}\n` +
              `Sheets: ${Object.keys(summary.structure).length}\n` +
              `Total Cost: $${(costData.totals?.total || 0).toLocaleString()}\n\n` +
              `Data Sources:\n` +
              `• Equipment: ${summary.dataSource.equipment}\n` +
              `• Labor: ${summary.dataSource.labor}\n` +
              `• Materials: ${summary.dataSource.materials}\n` +
              `• Engineering: ${summary.dataSource.engineering}\n\n` +
              `(Actual .xlsx generation requires SheetJS library)`);
        
        resolve(summary);
    });
}

// Global instances
window.ExcelExportGuard = window.ExcelExportGuard || ExcelExportGuard;
window.stubExcelExport = window.stubExcelExport || stubExcelExport;

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ExcelExportGuard, stubExcelExport };
}
